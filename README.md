# 📦 Environment Setup Guide

This guide will help you set up the following components on your local environment:

- Configure the Java build tool: Gradle
- Install Docker
- Pull and run a PostgreSQL container (with PostGIS support)
- Initialize database user, password, and database name

---

## 1️⃣ Configure Gradle

Ensure that **JDK 21** is installed. This project uses [<PERSON>rad<PERSON>](https://gradle.org/) as its build tool.

Refer to the Gradle setup guide here:  
https://wi3plswdprt.feishu.cn/wiki/V3jIwI6ApiBXKFkmPZXcy8Fjncc

---

## 2️⃣ Install Docker

If Docker is not installed on your machine, use the following links to install it based on your OS:

- [Docker for Mac](https://docs.docker.com/docker-for-mac/install/)
- [Docker for Windows](https://docs.docker.com/docker-for-windows/install/)
- [Docker for Linux](https://docs.docker.com/engine/install/)

After installation, verify Docker is working:

```bash
docker --version
```

---

## 3️⃣ Start Containers Using Docker Compose

The project includes a `docker-compose.yml` file that defines the required services.

To start all containers, navigate to the directory containing the file and run:

```bash
docker-compose up -d
```

This will pull the necessary images (if not already available) and start the containers in the background.

---

## 4️⃣ Start Specific Services

After the Docker Compose environment is up and running, you can start the required service by locating its corresponding `Application` class in the codebase and running it.

---

## ✅ Additional Tips

- To stop all running services:

```bash
docker-compose down
```

- To view logs of a specific service:

```bash
docker-compose logs -f servicename
```

- To check the status of all services:

```bash
docker-compose ps
```

---

For more detailed troubleshooting or advanced configurations, refer to the official [Docker Compose documentation](https://docs.docker.com/compose/).