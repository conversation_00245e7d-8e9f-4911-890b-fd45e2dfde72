package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_ALREADY_EXISTS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_NOT_FOUND;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.mapper.vendor.VendorDtoApplicationMapper;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.VendorApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorFactory;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class VendorApplicationServiceImpl implements VendorApplicationService {

    private final VendorService vendorService;
    private final VendorDtoApplicationMapper vendorDtoApplicationMapper;
    private final FinaleApplicationService finaleApplicationService;

    @Override
    public VendorDto create(CreateVendorCommand command) {
        Vendor vendor = VendorFactory.createVendor(command);
        Vendor existVendor = vendorService.findByVendorName(vendor.getVendorName());
        if (null != existVendor) {
            throw new ImsBusinessException(VENDOR_ALREADY_EXISTS);
        }
        FinaleVendorDto finaleVendorDto = finaleApplicationService.createVendor(command.getVendorName());
        if (finaleVendorDto == null) {
            log.error("[create] finaleVendorDto is null, vendorName: {}", command.getVendorName());
        } else {
            vendor.updateFinaleId(finaleVendorDto.getPartyId());
        }
        vendor = vendorService.save(vendor);
        return vendorDtoApplicationMapper.domainToDto(vendor);
    }

    @Override
    public VendorDto update(UpdateVendorCommand command) {
        Vendor vendor = vendorService.findById(command.getVendorId());
        if (vendor == null) {
            throw new ImsBusinessException(VENDOR_NOT_FOUND.getCode());
        }

        Vendor byVendorName = vendorService.findByVendorName(command.getVendorName());
        if (byVendorName != null && !byVendorName.getId().equals(command.getVendorId())) {
            throw new ImsBusinessException(VENDOR_ALREADY_EXISTS);
        }
        vendor.updateVendorName(command.getVendorName());
        if (command.getExternalPicking() != null) {
            vendor.setexternalPicking(command.getExternalPicking());
        }
        vendor = vendorService.update(vendor);

        return vendorDtoApplicationMapper.domainToDto(vendor);
    }

    @Override
    public VendorDto delete(UUID id) {
        Vendor vendor = vendorService.delete(id);
        return vendorDtoApplicationMapper.domainToDto(vendor);
    }

    @Override
    public void migrateFinaleVendor() {
        List<Vendor> vendorList = vendorService.findAll();
        for (Vendor vendor : vendorList) {
            if (vendor.getFinaleId() != null) {
                continue;
            }
            FinaleVendorDto finaleVendorDto = finaleApplicationService.getVendor(vendor.getVendorName());
            if (finaleVendorDto != null) {
                vendor.updateFinaleId(finaleVendorDto.getPartyId());
                vendorService.save(vendor);
            }
        }

    }
}
