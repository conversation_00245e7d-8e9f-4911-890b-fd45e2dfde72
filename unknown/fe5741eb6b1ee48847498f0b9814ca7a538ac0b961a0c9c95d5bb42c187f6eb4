package com.mercaso.ims.infrastructure.external.finale.enums;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import java.util.Arrays;

public enum StatusId {

    PRODUCT_INACTIVE,
    PRODUCT_ACTIVE,
    UNKNOWN,
    ;

    public static StatusId fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }

    public static StatusId fromAvailabilityStatus(String availabilityStatus) {
        if (availabilityStatus.equals(AvailabilityStatus.ARCHIVED.name())) {
            return PRODUCT_INACTIVE;
        } else {
            return PRODUCT_ACTIVE;
        }
    }
}
