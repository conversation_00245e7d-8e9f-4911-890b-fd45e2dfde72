package com.mercaso.ims.infrastructure.schedule;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.AttachmentDto;
import com.mercaso.ims.application.dto.GmailMessageDto;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.excel.data.CostcoItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.generator.CostcoItemDailyUpdatedExcelGenerator;
import com.mercaso.ims.infrastructure.external.google.GmailService;
import com.mercaso.ims.infrastructure.external.google.GoogleSheetAdaptor;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionDtoUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import javax.mail.search.SearchTerm;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

@Slf4j
class VendorPoInvoiceSchedulerTest {


    @Mock
    PgAdvisoryLock pgAdvisoryLock;
    @Mock
    EntityManagerFactory managerFactory;
    @Mock
    VernonAdaptor vernonAdaptor;
    @Mock
    VendorService vendorService;
    @Mock
    ItemCostCollectionService itemCostCollectionService;
    @Mock
    ItemCostCollectionApplicationService itemCostCollectionApplicationService;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @Mock
    DocumentApplicationService documentApplicationService;
    @Mock
    GmailService gmailService;
    @Mock
    GoogleSheetAdaptor googleSheetAdaptor;
    @Mock
    CostcoItemDailyUpdatedExcelGenerator costcoItemDailyUpdatedExcelGenerator;
    @InjectMocks
    VendorPoInvoiceScheduler vendorPoInvoiceScheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSyncVernorItemCostCollectionByGmail() {
        UUID id = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        Vendor vendor = VendorUtil.buildVendor(id);
        EntityManager entityManager = Mockito.mock(EntityManager.class);
        ItemCostCollectionDto itemCostCollectionDto = ItemCostCollectionDtoUtil.buildItemCostCollectionDto();

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(pgAdvisoryLock.unLock(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(managerFactory.createEntityManager()).thenReturn(entityManager);
        when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
        when(itemCostCollectionService.findByVendorId(any(UUID.class))).thenReturn(List.of(itemCostCollection));
        when(itemCostCollectionApplicationService.create(any(CreateItemCostCollectionCommand.class))).thenReturn(
            itemCostCollectionDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(documentApplicationService.uploadFileContent(any(byte[].class),
            anyString(),
            anyBoolean())).thenReturn(new DocumentResponse("signedUrl", "name"));
        when(gmailService.queryEmails(any(SearchTerm.class))).thenReturn(List.of(new GmailMessageDto("subject",
            "from",
            LocalDateTime.of(2025, Month.FEBRUARY, 11, 10, 31, 55).toInstant(
                ZoneOffset.UTC),
            List.of(new AttachmentDto(new byte[]{(byte) 0}, "filename")))));

        vendorPoInvoiceScheduler.syncVernorItemCostCollectionByGmail();

        verify(itemCostCollectionApplicationService, times(1)).create(any());

    }

    @Test
    void testSyncCostcoItemCostCollection() {
        UUID id = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        Vendor vendor = VendorUtil.buildVendor(id);
        EntityManager entityManager = Mockito.mock(EntityManager.class);
        CostcoItemDailyUpdatedData costcoItemDailyUpdatedData = new CostcoItemDailyUpdatedData(
            "whseNumber",
            "dNumber",
            "description",
            "cat1AndDescription",
            "vendorItemNumber",
            "description1",
            "description2",
            new BigDecimal(0),
            1,
            "inTransit",
            "onOrder",
            "statusDesc",
            "bbs",
            "randomWeight",
            "upc",
            "mpkQty",
            "mpkType",
            "unitWeight",
            "asOf");
        List<List<Object>> readGoogleSheet = List.of(

            List.of(costcoItemDailyUpdatedData.getWhseNumber(),
                costcoItemDailyUpdatedData.getDNumber(),
                costcoItemDailyUpdatedData.getDescription(),
                costcoItemDailyUpdatedData.getCat1AndDescription(),
                costcoItemDailyUpdatedData.getVendorItemNumber(),
                costcoItemDailyUpdatedData.getDescription1(),
                costcoItemDailyUpdatedData.getDescription2(), 10, costcoItemDailyUpdatedData.getOnHand(),
                costcoItemDailyUpdatedData.getInTransit(),
                costcoItemDailyUpdatedData.getOnOrder(),
                costcoItemDailyUpdatedData.getStatusDesc(),
                costcoItemDailyUpdatedData.getBbs(),
                costcoItemDailyUpdatedData.getRandomWeight(),
                costcoItemDailyUpdatedData.getUpc(),
                costcoItemDailyUpdatedData.getMpkQty(),
                costcoItemDailyUpdatedData.getMpkType(),
                costcoItemDailyUpdatedData.getUnitWeight(),
                costcoItemDailyUpdatedData.getAsOf()));
        ItemCostCollectionDto itemCostCollectionDto = ItemCostCollectionDtoUtil.buildItemCostCollectionDto();
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(pgAdvisoryLock.unLock(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(managerFactory.createEntityManager()).thenReturn(entityManager);
        when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
        when(itemCostCollectionService.findByVendorId(any(UUID.class))).thenReturn(List.of(itemCostCollection));
        when(itemCostCollectionApplicationService.create(any(CreateItemCostCollectionCommand.class))).thenReturn(
            itemCostCollectionDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(documentApplicationService.uploadExcel(any(byte[].class), anyString())).thenReturn(new DocumentResponse("signedUrl",
            "name"));
        when(googleSheetAdaptor.readGoogleSheet(any(), anyString())).thenReturn(List.of(List.of("test")))
            .thenReturn(readGoogleSheet);
        when(costcoItemDailyUpdatedExcelGenerator.generate(any(), anyString())).thenReturn(
            new byte[]{(byte) 0});

        vendorPoInvoiceScheduler.syncCostcoItemCostCollection();

        verify(itemCostCollectionApplicationService, times(1)).create(any());

    }

    @Test
    void testSyncSevenStarItemCostCollectionByGmail() {
        UUID id = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        Vendor vendor = VendorUtil.buildVendor(id);
        EntityManager entityManager = Mockito.mock(EntityManager.class);
        ItemCostCollectionDto itemCostCollectionDto = ItemCostCollectionDtoUtil.buildItemCostCollectionDto();

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(pgAdvisoryLock.unLock(any(EntityManager.class), anyInt(), anyString())).thenReturn(Boolean.TRUE);
        when(managerFactory.createEntityManager()).thenReturn(entityManager);
        when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
        when(itemCostCollectionService.findByVendorId(any(UUID.class))).thenReturn(List.of(itemCostCollection));
        when(itemCostCollectionApplicationService.create(any(CreateItemCostCollectionCommand.class))).thenReturn(
            itemCostCollectionDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(documentApplicationService.uploadExcel(any(byte[].class),
            anyString())).thenReturn(new DocumentResponse("signedUrl", "name"));
        when(gmailService.queryEmails(any(SearchTerm.class))).thenReturn(List.of(new GmailMessageDto("subject",
            "from",
            LocalDateTime.of(2025, Month.FEBRUARY, 11, 10, 31, 55).toInstant(
                ZoneOffset.UTC),
            List.of(new AttachmentDto(new byte[]{(byte) 0}, "filename")))));

        vendorPoInvoiceScheduler.syncSevenStarCostCollectionByGmail();

        verify(itemCostCollectionApplicationService, times(1)).create(any());

    }
}