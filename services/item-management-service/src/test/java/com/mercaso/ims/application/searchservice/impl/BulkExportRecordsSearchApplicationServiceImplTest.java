package com.mercaso.ims.application.searchservice.impl;

import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.CustomizedBulkExportRecordsJpaDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BulkExportRecordsSearchApplicationServiceImplTest {

    @Mock
    private CustomizedBulkExportRecordsJpaDao customizedBulkExportRecordsJpaDao;

    @InjectMocks
    private BulkExportRecordsSearchApplicationServiceImpl bulkExportRecordsSearchApplicationService;

    private BulkExportRecordsDto sampleDto;
    private BulkExportRecordsSearchDto expectedSearchDto;

    @BeforeEach
    void setUp() {
        sampleDto = BulkExportRecordsDto.builder()
            .id(UUID.randomUUID())
            .fileName("test_export.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now())
            .customFilter("{\"filter\": \"test\"}")
            .exportBy("testUser")
            .createdAt(Instant.now())
            .build();

        expectedSearchDto = BulkExportRecordsSearchDto.builder()
            .data(List.of(sampleDto))
            .totalCount(1L)
            .build();
    }

    @Test
    void searchBulkExportRecords_WithValidQuery_ShouldReturnResults() {
        BulkExportRecordsQuery query = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(20)
            .build();

        when(customizedBulkExportRecordsJpaDao.getBulkExportRecords(any()))
            .thenReturn(expectedSearchDto);

        BulkExportRecordsSearchDto result = bulkExportRecordsSearchApplicationService
            .searchBulkExportRecords(query);

        assertNotNull(result);
        assertEquals(expectedSearchDto.getTotalCount(), result.getTotalCount());
        assertEquals(expectedSearchDto.getData().size(), result.getData().size());
        assertEquals(expectedSearchDto.getData().getFirst().getFileName(),
            result.getData().getFirst().getFileName());
    }

    @Test
    void searchBulkExportRecords_WithNullQuery_ShouldReturnEmptyResults() {
        when(customizedBulkExportRecordsJpaDao.getBulkExportRecords(null))
            .thenReturn(BulkExportRecordsSearchDto.builder()
                .data(List.of())
                .totalCount(0L)
                .build());

        BulkExportRecordsSearchDto result = bulkExportRecordsSearchApplicationService
            .searchBulkExportRecords(null);

        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void searchBulkExportRecords_WithFilterConditions_ShouldReturnFilteredResults() {
        Instant startDate = Instant.now().minusSeconds(3600);
        Instant endDate = Instant.now();
        String userName = "testUser";

        BulkExportRecordsQuery query = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(20)
            .createdStartDate(startDate)
            .createdEndDate(endDate)
            .createdBy(userName)
            .build();

        when(customizedBulkExportRecordsJpaDao.getBulkExportRecords(any()))
            .thenReturn(expectedSearchDto);

        BulkExportRecordsSearchDto result = bulkExportRecordsSearchApplicationService
            .searchBulkExportRecords(query);

        assertNotNull(result);
        assertEquals(expectedSearchDto.getTotalCount(), result.getTotalCount());
        assertEquals(expectedSearchDto.getData().size(), result.getData().size());
        assertEquals(expectedSearchDto.getData().getFirst().getExportBy(),
            result.getData().getFirst().getExportBy());
    }
}