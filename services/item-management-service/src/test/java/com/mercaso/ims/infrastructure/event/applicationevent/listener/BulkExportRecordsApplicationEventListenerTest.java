package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.event.BulkExportRecordsApplicationEvent;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import com.mercaso.ims.infrastructure.external.google.GmailService;
import com.mercaso.ims.infrastructure.util.SecurityUtil;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BulkExportRecordsApplicationEventListenerTest {

    @Mock
    private DocumentApplicationService documentApplicationService;

    @Mock
    private GmailService gmailService;

    @Mock
    private BulkExportRecordsService bulkExportRecordsService;

    @InjectMocks
    private BulkExportRecordsApplicationEventListener listener;

    private final String fileName = "export_records_2023.xlsx";
    private final Instant searchTime = Instant.now();
    private final String userEmail = "<EMAIL>";
    private final UUID bulkExportRecordsId = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        // Setup common test data if needed
    }

    @Test
    void shouldSendEmailWithAttachmentWhenHandlingEvent() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(userEmail);

            // Mock document getSignedUrl success
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn("http://signed-url.com/file");

            // Mock email sending success (no attachments, expect null for 4th param)
            when(gmailService.sendEmail(eq(userEmail), anyString(), anyString(), isNull()))
                .thenReturn(true);

            // Mock bulk export records for update
            BulkExportRecords mockBulkExportRecords = BulkExportRecords.builder()
                .id(bulkExportRecordsId)
                .fileName(fileName)
                .searchTime(searchTime)
                .build();
            when(bulkExportRecordsService.findById(bulkExportRecordsId)).thenReturn(mockBulkExportRecords);

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            verify(documentApplicationService, times(1)).getSignedUrl(fileName);

            // Verify email is sent with correct parameters
            verify(gmailService, times(1)).sendEmail(
                    eq(userEmail),
                    eq("Bulk Export Records: " + fileName),
                    anyString(),
                    isNull());

            // Verify database update
            verify(bulkExportRecordsService, times(1)).findById(bulkExportRecordsId);
            verify(bulkExportRecordsService, times(1)).update(any(BulkExportRecords.class));
        }
    }

    @Test
    void shouldLogErrorWhenEmailSendingFails() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(userEmail);

            // Mock document getSignedUrl success
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn("http://signed-url.com/file");

            // Mock email sending failure
            when(gmailService.sendEmail(eq(userEmail), anyString(), anyString(), any(List.class)))
                .thenReturn(false);

            when(bulkExportRecordsService.findById(any())).thenReturn(null);

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            verify(documentApplicationService, times(1)).getSignedUrl(fileName);

            // Verify email is sent with correct parameters (updated email body format)
            verify(gmailService, times(1)).sendEmail(
                eq(userEmail),
                eq("Bulk Export Records: " + fileName),
                anyString(), // Use anyString() since email body format has changed
                isNull());

            // Should not update database when email sending fails
            verify(bulkExportRecordsService, times(1)).findById(any());
            verify(bulkExportRecordsService, times(0)).update(any());
        }
    }

    @Test
    void shouldHandleNullEmail() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method returning null
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(null);

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            // Should not download document or send email when no recipient email
            verify(documentApplicationService, never()).downloadDocument(anyString());
            verify(gmailService, never()).sendEmail(anyString(), anyString(), anyString(), any(List.class));
            verify(bulkExportRecordsService, never()).findById(any());
            verify(bulkExportRecordsService, never()).update(any());
        }
    }

    @Test
    void shouldHandleNullEvent() {
        // When
        listener.handleBulkExportRecordsApplicationEvent(null);

        // Then
        verify(documentApplicationService, never()).downloadDocument(anyString());
        verify(gmailService, never()).sendEmail(anyString(), anyString(), anyString(), any(List.class));
        verify(bulkExportRecordsService, never()).findById(any());
        verify(bulkExportRecordsService, never()).update(any());
    }

    @Test
    void shouldHandleInvalidEventData() {
        // Given - event with null data
        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(null)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        // When
        listener.handleBulkExportRecordsApplicationEvent(event);

        // Then
        verify(documentApplicationService, never()).downloadDocument(anyString());
        verify(gmailService, never()).sendEmail(anyString(), anyString(), anyString(), any(List.class));
        verify(bulkExportRecordsService, never()).findById(any());
        verify(bulkExportRecordsService, never()).update(any());
    }

    @Test
    void shouldHandleDocumentDownloadFailure() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(userEmail);

            // Mock document getSignedUrl failure
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn(null);

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            verify(documentApplicationService, times(1)).getSignedUrl(fileName);
            // Should not send email when document download fails
            verify(gmailService, never()).sendEmail(anyString(), anyString(), anyString(), any(List.class));
            verify(bulkExportRecordsService, never()).findById(any());
            verify(bulkExportRecordsService, never()).update(any());
        }
    }

    @Test
    void shouldHandleEmailSendingFailureGracefully() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(userEmail);

            // Mock document download success
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn(fileName);

            // Mock email sending failure
            when(gmailService.sendEmail(eq(userEmail), anyString(), anyString(), any(List.class)))
                .thenReturn(false);

            when(bulkExportRecordsService.findById(any())).thenReturn(BulkExportRecords.builder().build());

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            verify(documentApplicationService, times(1)).getSignedUrl(fileName);
            verify(gmailService, times(1)).sendEmail(anyString(), anyString(), anyString(), isNull());
            // Should not update database when email sending fails
            verify(bulkExportRecordsService, times(1)).findById(any());
            verify(bulkExportRecordsService, times(1)).update(any());
        }
    }

    @Test
    void shouldHandleDatabaseUpdateFailureGracefully() {
        // Given
        BulkExportRecordsDto data = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .fileName(fileName)
            .searchTime(searchTime)
            .build();

        BulkExportRecordsPayloadDto payload = BulkExportRecordsPayloadDto.builder()
            .bulkExportRecordsId(bulkExportRecordsId)
            .data(data)
            .build();

        BulkExportRecordsApplicationEvent event = new BulkExportRecordsApplicationEvent("test", payload);

        try (MockedStatic<SecurityUtil> securityUtil = Mockito.mockStatic(SecurityUtil.class)) {
            // Mock the static method
            securityUtil.when(SecurityUtil::getLoginUserEmail).thenReturn(userEmail);

            // Mock document download success
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn(fileName);

            // Mock email sending success
            when(gmailService.sendEmail(eq(userEmail), anyString(), anyString(), any(List.class)))
                .thenReturn(true);

            // Mock database operation failure
            when(bulkExportRecordsService.findById(bulkExportRecordsId)).thenReturn(null);

            // When
            listener.handleBulkExportRecordsApplicationEvent(event);

            // Then
            verify(documentApplicationService, times(1)).getSignedUrl(fileName);
            verify(gmailService, times(1)).sendEmail(anyString(), anyString(), anyString(), isNull());
            // Should not call update when record not found
            verify(bulkExportRecordsService, never()).update(any());
        }
    }
}