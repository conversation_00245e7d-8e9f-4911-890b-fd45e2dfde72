package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryItemSalesTrendRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_SALES_TREND_URL = "/v1/query/item-sales-trend";

    public QueryItemSalesTrendRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<ItemSalesTrendDto> searchItemSalesTrend(UUID itemId, ItemSalesTrendTimeGrain timeGrain) throws Exception {
        String url = String.format("%s?itemId=%s&timeGrain=%s", SEARCH_ITEM_SALES_TREND_URL, itemId, timeGrain);
        return getEntityList(url, ItemSalesTrendDto.class);
    }
}
