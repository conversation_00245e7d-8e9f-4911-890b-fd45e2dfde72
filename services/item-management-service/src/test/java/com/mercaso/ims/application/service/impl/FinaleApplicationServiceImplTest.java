package com.mercaso.ims.application.service.impl;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {FinaleApplicationServiceImpl.class})
class FinaleApplicationServiceImplTest extends AbstractTest {

    @MockBean
    FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @MockBean
    FeatureFlagsManager featureFlagsManager;
    @Autowired
    FinaleApplicationServiceImpl finaleApplicationServiceImpl;


    @Test
    void testSyncItemToFinale() {
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        finaleApplicationServiceImpl.syncItemToFinale(itemDto);
        verify(finaleExternalApiAdaptor).createAsSkuNotExist(anyString());
        verify(finaleExternalApiAdaptor).getFinaleProduct(anyString());
    }
}