package com.mercaso.ims.infrastructure.repository.category;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.category.jpa.CategoryJpaDao;
import com.mercaso.ims.infrastructure.repository.category.jpa.dataobject.CategoryDo;
import com.mercaso.ims.infrastructure.repository.category.jpa.mapper.CategoryDoMapper;
import com.mercaso.ims.utils.category.CategoryUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {CategoryRepositoryImpl.class})
@ExtendWith(SpringExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
class CategoryRepositoryImplTest {

    @MockBean
    private CategoryDoMapper categoryDoMapper;

    @MockBean
    private CategoryJpaDao categoryJpaDao;

    @Autowired
    private CategoryRepositoryImpl categoryRepositoryImpl;

    @Test
    void testSaveWithCategory_thenReturnNull() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        when(categoryDoMapper.domainToDo(Mockito.<Category>any())).thenReturn(null);
        when(categoryJpaDao.save(Mockito.<CategoryDo>any())).thenReturn(categoryDo);

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenReturn(null);
        when(categoryDoMapper.domainToDo(Mockito.<Category>any())).thenReturn(categoryDo2);

        // Act
        Category actualSaveResult = categoryRepositoryImpl.save(null);

        // Assert
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryDoMapper).domainToDo(isNull());
        verify(categoryJpaDao).save(isA(CategoryDo.class));
        assertNull(actualSaveResult);
    }


    @Test
    void testSaveWithCategory_thenThrowImsBusinessException() {
        // Arrange
        when(categoryDoMapper.domainToDo(Mockito.<Category>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> categoryRepositoryImpl.save(null));
        verify(categoryDoMapper).domainToDo(isNull());
    }


    @Test
    void testFindByIdWithUuid_givenCategoryDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        Optional<CategoryDo> ofResult = Optional.of(categoryDo);
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenReturn(null);

        // Act
        Category actualFindByIdResult = categoryRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        Optional<CategoryDo> ofResult = Optional.of(categoryDo);
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> categoryRepositoryImpl.findById(uuid));
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdateWithCategory_thenReturnNull() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        Optional<CategoryDo> ofResult = Optional.of(categoryDo);

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        when(categoryJpaDao.save(Mockito.<CategoryDo>any())).thenReturn(categoryDo2);
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        CategoryDo categoryDo3 = CategoryUtil.buildCategoryDo();
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenReturn(null);
        when(categoryDoMapper.domainToDo(Mockito.<Category>any())).thenReturn(categoryDo3);
        Category domain = mock(Category.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        Category actualUpdateResult = categoryRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryDoMapper).domainToDo(isA(Category.class));
        verify(categoryJpaDao).findById(isA(UUID.class));
        verify(categoryJpaDao).save(isA(CategoryDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testDeleteById_givenCategoryDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        Optional<CategoryDo> ofResult = Optional.of(categoryDo);

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        when(categoryJpaDao.save(Mockito.<CategoryDo>any())).thenReturn(categoryDo2);
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenReturn(null);

        // Act
        Category actualDeleteByIdResult = categoryRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findById(isA(UUID.class));
        verify(categoryJpaDao).save(isA(CategoryDo.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_givenCategoryJpaDaoFindByIdReturnEmpty_thenReturnNull() {
        // Arrange
        Optional<CategoryDo> emptyResult = Optional.empty();
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        Category actualDeleteByIdResult = categoryRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(categoryJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        Optional<CategoryDo> ofResult = Optional.of(categoryDo);

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        when(categoryJpaDao.save(Mockito.<CategoryDo>any())).thenReturn(categoryDo2);
        when(categoryJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> categoryRepositoryImpl.deleteById(uuid));
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findById(isA(UUID.class));
        verify(categoryJpaDao).save(isA(CategoryDo.class));
    }


    @Test
    void testFindByName_givenCategoryDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        when(categoryJpaDao.findByName(Mockito.<String>any())).thenReturn(Lists.newArrayList(categoryDo));
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenReturn(null);

        // Act
        List<Category> actualFindByNameResult = categoryRepositoryImpl.findByName("Name");

        // Assert
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findByName("Name");
        assertNotNull(actualFindByNameResult);
    }


    @Test
    void testFindByName_thenThrowImsBusinessException() {
        // Arrange
        CategoryDo categoryDo = CategoryUtil.buildCategoryDo();
        when(categoryJpaDao.findByName(Mockito.<String>any())).thenReturn(Lists.newArrayList(categoryDo));
        when(categoryDoMapper.doToDomain(Mockito.<CategoryDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> categoryRepositoryImpl.findByName("Name"));
        verify(categoryDoMapper).doToDomain(isA(CategoryDo.class));
        verify(categoryJpaDao).findByName("Name");
    }


    @Test
    void testFindAllByNameIn_givenCategoryDoMapper_whenArrayList_thenReturnEmpty() {
        // Arrange
        when(categoryJpaDao.findAllByNameIn(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        // Act
        List<Category> actualFindAllByNameInResult = categoryRepositoryImpl.findAllByNameIn(new ArrayList<>());

        // Assert
        verify(categoryJpaDao).findAllByNameIn(isA(List.class));
        assertTrue(actualFindAllByNameInResult.isEmpty());
    }

    @Test
    void testFindAllByIdIn_WithResults() {
        // Arrange
        List<UUID> ids = new ArrayList<>();
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        ids.add(id1);
        ids.add(id2);

        List<CategoryDo> categoryDos = new ArrayList<>();
        CategoryDo categoryDo1 = CategoryUtil.buildCategoryDo();
        categoryDo1.setId(id1);
        categoryDo1.setName("Category 1");

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        categoryDo2.setId(id2);
        categoryDo2.setName("Category 2");

        categoryDos.add(categoryDo1);
        categoryDos.add(categoryDo2);

        Category category1 = Category.builder()
            .id(id1)
            .name("Category 1")
            .build();

        Category category2 = Category.builder()
            .id(id2)
            .name("Category 2")
            .build();

        when(categoryJpaDao.findAllByIdIn(ids)).thenReturn(categoryDos);
        when(categoryDoMapper.doToDomain(categoryDo1)).thenReturn(category1);
        when(categoryDoMapper.doToDomain(categoryDo2)).thenReturn(category2);

        // Act
        List<Category> result = categoryRepositoryImpl.findAllByIdIn(ids);

        // Assert
        assertNotNull(result);
        Assertions.assertEquals(2, result.size());
        // Check that the order is reversed
        Assertions.assertEquals(id2, result.get(0).getId());
        Assertions.assertEquals(id1, result.get(1).getId());

        verify(categoryJpaDao).findAllByIdIn(ids);
        verify(categoryDoMapper).doToDomain(categoryDo1);
        verify(categoryDoMapper).doToDomain(categoryDo2);
    }

    @Test
    void testFindAllByIdIn_EmptyList() {
        // Arrange
        List<UUID> emptyIds = new ArrayList<>();
        when(categoryJpaDao.findAllByIdIn(emptyIds)).thenReturn(new ArrayList<>());

        // Act
        List<Category> result = categoryRepositoryImpl.findAllByIdIn(emptyIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(categoryJpaDao).findAllByIdIn(emptyIds);
    }

    @Test
    void testFindAllByIdIn_NullResult() {
        // Arrange
        List<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());

        when(categoryJpaDao.findAllByIdIn(ids)).thenReturn(null);

        // Act
        List<Category> result = categoryRepositoryImpl.findAllByIdIn(ids);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(categoryJpaDao).findAllByIdIn(ids);
    }

    @Test
    void testFindByNameAndStatus_WithResults() {
        // Arrange
        String name = "TestCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        List<CategoryDo> categoryDos = new ArrayList<>();
        CategoryDo categoryDo1 = CategoryUtil.buildCategoryDo();
        categoryDo1.setId(UUID.randomUUID());
        categoryDo1.setName(name);
        categoryDo1.setStatus(status);

        CategoryDo categoryDo2 = CategoryUtil.buildCategoryDo();
        categoryDo2.setId(UUID.randomUUID());
        categoryDo2.setName(name);
        categoryDo2.setStatus(status);

        categoryDos.add(categoryDo1);
        categoryDos.add(categoryDo2);

        Category category1 = Category.builder()
            .id(categoryDo1.getId())
            .name(name)
            .status(status)
            .build();

        Category category2 = Category.builder()
            .id(categoryDo2.getId())
            .name(name)
            .status(status)
            .build();

        when(categoryJpaDao.findAllByNameAndStatus(name, status)).thenReturn(categoryDos);
        when(categoryDoMapper.doToDomain(categoryDo1)).thenReturn(category1);
        when(categoryDoMapper.doToDomain(categoryDo2)).thenReturn(category2);

        // Act
        List<Category> result = categoryRepositoryImpl.findByNameAndStatus(name, status);

        // Assert
        assertNotNull(result);
        Assertions.assertEquals(2, result.size());
        // Check that the order is reversed
        Assertions.assertEquals(categoryDo2.getId(), result.get(0).getId());
        Assertions.assertEquals(categoryDo1.getId(), result.get(1).getId());

        verify(categoryJpaDao).findAllByNameAndStatus(name, status);
        verify(categoryDoMapper).doToDomain(categoryDo1);
        verify(categoryDoMapper).doToDomain(categoryDo2);
    }

    @Test
    void testFindByNameAndStatus_EmptyResult() {
        // Arrange
        String name = "NonExistentCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        when(categoryJpaDao.findAllByNameAndStatus(name, status)).thenReturn(new ArrayList<>());

        // Act
        List<Category> result = categoryRepositoryImpl.findByNameAndStatus(name, status);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(categoryJpaDao).findAllByNameAndStatus(name, status);
    }

    @Test
    void testFindByNameAndStatus_NullResult() {
        // Arrange
        String name = "TestCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        when(categoryJpaDao.findAllByNameAndStatus(name, status)).thenReturn(null);

        // Act
        List<Category> result = categoryRepositoryImpl.findByNameAndStatus(name, status);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(categoryJpaDao).findAllByNameAndStatus(name, status);
    }

    @Test
    void testFindByNameAndStatus_NullName() {
        // Arrange
        String name = null;
        CategoryStatus status = CategoryStatus.ACTIVE;

        when(categoryJpaDao.findAllByNameAndStatus(name, status)).thenReturn(new ArrayList<>());

        // Act
        List<Category> result = categoryRepositoryImpl.findByNameAndStatus(name, status);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(categoryJpaDao).findAllByNameAndStatus(name, status);
    }
}
