package com.mercaso.ims.application.service.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailImsUpdatedPayloadDto;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentTransitionEvents;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.ims.infrastructure.util.SpringContextUtil;
import com.mercaso.ims.utils.brand.BrandUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailCommandUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailDtoUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailUtil;
import com.mercaso.ims.utils.vendor.VendorItemDtoUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import java.lang.reflect.Method;

class ItemAdjustmentRequestDetailApplicationServiceImplTest {

    @Mock
    ItemAdjustmentRequestDetailDtoApplicationMapper adjustmentRequestDetailDtoApplicationMapper;
    @Mock
    ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;
    @Mock
    BusinessEventService businessEventService;
    @Mock
    BrandService brandService;
    @Mock
    AttributeService attributeService;
    @Mock
    VendorService vendorService;
    @Mock
    VendorItemService vendorItemService;
    @Mock
    VendorItemApplicationService vendorItemApplicationService;
    @Mock
    ItemQueryApplicationService itemQueryApplicationService;
    @Mock
    ItemService itemService;
    @Mock
    ItemApplicationService itemApplicationService;
    @Mock
    FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    @Mock
    ItemDtoApplicationMapper itemDtoApplicationMapper;
    @InjectMocks
    ItemAdjustmentRequestDetailApplicationServiceImpl itemAdjustmentRequestDetailApplicationServiceImpl;
    @Mock
    StateMachineProcessor<ItemAdjustmentRequestDetail, ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> stateMachineProcessor;

    // Constants for category hierarchy depths
    private static final int DEPTH_TO_PARENT = 1;
    private static final int DEPTH_TO_GRANDPARENT = 2;
    private static final int DEPTH_TO_GREAT_GRANDPARENT = 3;
    
    @Mock
    private CategoryApplicationService categoryApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreate() {

        UUID id = UUID.randomUUID();
        ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
        when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
            detailDto);
        when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(
            ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id));
        when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);

        ItemAdjustmentRequestDetailDto result = itemAdjustmentRequestDetailApplicationServiceImpl.create(
            ItemAdjustmentRequestDetailCommandUtil.buildCreateItemAdjustmentRequestDetailCommand(id));
        Assertions.assertEquals(detailDto, result);
    }

    @Test
    void testUpdateIms() {

        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id);
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            itemAdjustmentRequestDetailApplicationServiceImpl.updateIms(id);
            verify(itemAdjustmentRequestDetailRepository).save(any(ItemAdjustmentRequestDetail.class));
        }


    }

    @Test
    void testCompleteItemAdjustmentRequest() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id);
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);

            ItemAdjustmentRequestDetailDto result = itemAdjustmentRequestDetailApplicationServiceImpl.completeItemAdjustmentRequest(
                id);
            Assertions.assertEquals(detailDto, result);
        }
    }

    @Test
    void testUpdateImsAsItemAdjustmentRequestCreate() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(id);
            VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
            ItemDto itemDto = ItemDTOUtil.buildItemDTO();
            Brand brand = BrandUtil.buildBrand(id);
            Vendor vendor = VendorUtil.buildVendor("vendor");
            VendorItem vendorItem = VendorItemUtil.buildVendorItem();
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);
            when(brandService.findByName(anyString())).thenReturn(brand);
            when(brandService.save(any(Brand.class))).thenReturn(brand);
            when(vendorService.findById(any(UUID.class))).thenReturn(vendor);
            when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
            when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(vendorItem);

            when(vendorItemApplicationService.create(any(CreateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(vendorItemApplicationService.update(any(UpdateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
            when(itemService.findById(any(UUID.class))).thenReturn(null);
            when(itemService.findBySku(anyString())).thenReturn(null);
            when(itemApplicationService.create(any(CreateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.update(any(UpdateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.deleteItemById(any(DeleteItemCommand.class))).thenReturn(ItemDTOUtil.buildItemDTO());

            itemAdjustmentRequestDetailApplicationServiceImpl.updateImsAsItemAdjustmentRequest(
                ItemAdjustmentRequestDetailDtoUtil.buildItemAdjustmentRequestDetailDto(id));

            verify(itemApplicationService).create(any(CreateItemCommand.class));
        }
    }

    @Test
    void testUpdateImsAsItemAdjustmentRequestUpdate() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildUpdateItemAdjustmentRequestDetailDto(
                id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildUpdateItemAdjustmentRequestDetail(id);
            Item item = ItemUtil.buildItem();
            Brand brand = BrandUtil.buildBrand(id);
            Vendor vendor = VendorUtil.buildVendor("testvendor");
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);
            when(brandService.findByName(anyString())).thenReturn(brand);
            when(brandService.save(any(Brand.class))).thenReturn(brand);
            when(vendorService.findById(any(UUID.class))).thenReturn(vendor);
            when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
            when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(null);

            VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
            ItemDto itemDto = ItemDTOUtil.buildItemDTO();
            when(vendorItemApplicationService.create(any(CreateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(vendorItemApplicationService.update(any(UpdateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
            when(itemService.findById(any(UUID.class))).thenReturn(item);
            when(itemService.findBySku(anyString())).thenReturn(item);
            when(itemApplicationService.create(any(CreateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.update(any(UpdateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.deleteItemById(any(DeleteItemCommand.class))).thenReturn(ItemDTOUtil.buildItemDTO());

            itemAdjustmentRequestDetailApplicationServiceImpl.updateImsAsItemAdjustmentRequest(detailDto);

            verify(businessEventService).dispatch(any(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.class));

        }
    }

    @Test
    void testUpdateImsAsItemAdjustmentRequestDelete() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildDeleteItemAdjustmentRequestDetailDto(
                id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildUpdateItemAdjustmentRequestDetail(id);
            Item item = ItemUtil.buildItem();
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);
            when(brandService.findByName(anyString())).thenReturn(null);
            when(brandService.save(any(Brand.class))).thenReturn(null);
            when(vendorService.findById(any(UUID.class))).thenReturn(null);
            when(vendorService.findByVendorName(anyString())).thenReturn(null);
            when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(null);

            VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
            ItemDto itemDto = ItemDTOUtil.buildItemDTO();
            when(vendorItemApplicationService.create(any(CreateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(vendorItemApplicationService.update(any(UpdateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
            when(itemService.findById(any(UUID.class))).thenReturn(item);
            when(itemService.findBySku(anyString())).thenReturn(item);
            when(itemApplicationService.create(any(CreateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.update(any(UpdateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.deleteItemById(any(DeleteItemCommand.class))).thenReturn(ItemDTOUtil.buildItemDTO());

            itemAdjustmentRequestDetailApplicationServiceImpl.updateImsAsItemAdjustmentRequest(detailDto);

            verify(itemApplicationService).deleteItemById(any(DeleteItemCommand.class));
            verify(businessEventService).dispatch(any(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.class));

        }
    }


    @Test
    void testUpdateImsAsItemAdjustmentRequestRemoveUpc() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequestDetailDto detailDto = ItemAdjustmentRequestDetailDtoUtil.buildRemoveUpcAdjustmentRequestDetailDto(
                id);
            ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailUtil.buildRemoveUpcAdjustmentRequestDetail(id);
            Item item = ItemUtil.buildItem();
            when(adjustmentRequestDetailDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequestDetail.class))).thenReturn(
                detailDto);
            when(itemAdjustmentRequestDetailRepository.save(any(ItemAdjustmentRequestDetail.class))).thenReturn(detail);
            when(itemAdjustmentRequestDetailRepository.findById(any())).thenReturn(detail);
            when(businessEventService.dispatch(any(BusinessEventPayloadDto.class))).thenReturn(null);
            when(brandService.findByName(anyString())).thenReturn(null);
            when(brandService.save(any(Brand.class))).thenReturn(null);
            when(vendorService.findById(any(UUID.class))).thenReturn(null);
            when(vendorService.findByVendorName(anyString())).thenReturn(null);
            when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(null);

            VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
            ItemDto itemDto = ItemDTOUtil.buildItemDTO();
            when(vendorItemApplicationService.create(any(CreateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(vendorItemApplicationService.update(any(UpdateVendorItemCommand.class))).thenReturn(vendorItemDto);
            when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
            when(itemService.findById(any(UUID.class))).thenReturn(item);
            when(itemService.findBySku(anyString())).thenReturn(item);
            when(itemApplicationService.create(any(CreateItemCommand.class))).thenReturn(itemDto);
            when(itemApplicationService.update(any(UpdateItemCommand.class))).thenReturn(itemDto);
            itemAdjustmentRequestDetailApplicationServiceImpl.updateImsAsItemAdjustmentRequest(detailDto);

            verify(itemApplicationService).cleanItemUPCs(any(CleanItemUpcCommand.class));
            verify(businessEventService).dispatch(any(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.class));

        }
    }

    /**
     * Helper method to invoke the private findCategoryIdFromHierarchy method using reflection
     */
    private UUID invokeFindCategoryIdFromHierarchy(String department, String category, String subCategory, String clazz) 
            throws Exception {
        Method method = ItemAdjustmentRequestDetailApplicationServiceImpl.class.getDeclaredMethod(
            "findCategoryIdFromHierarchy", String.class, String.class, String.class, String.class);
        method.setAccessible(true);
        return (UUID) method.invoke(itemAdjustmentRequestDetailApplicationServiceImpl, 
            department, category, subCategory, clazz);
    }

    @Test
    void testFindCategoryIdFromHierarchy_NullClass() throws Exception {
        // Arrange
        String department = null;
        String category = null;
        String subCategory = null;
        String clazz = null;
        
        // Act
        UUID result = invokeFindCategoryIdFromHierarchy(department, category, subCategory, clazz);
        
        // Assert
        Assertions.assertNull(result);
        // Verify no interactions with categoryApplicationService
        Mockito.verifyNoInteractions(categoryApplicationService);
    }
    
    @Test
    void testFindCategoryIdFromHierarchy_EmptyClass() throws Exception {
        // Arrange
        String department = "";
        String category = "";
        String subCategory = "";
        String clazz = "";
        
        // Act
        UUID result = invokeFindCategoryIdFromHierarchy(department, category, subCategory, clazz);
        
        // Assert
        Assertions.assertNull(result);
        // Verify no interactions with categoryApplicationService
        Mockito.verifyNoInteractions(categoryApplicationService);
    }
    
    @Test
    void testFindCategoryIdFromHierarchy_NoLeafCategoriesFound() throws Exception {
        // Arrange
        String department = "Department1";
        String category = "Category1";
        String subCategory = "SubCategory1";
        String clazz = "Class1";
        
        when(categoryApplicationService.searchCategoriesByName(clazz)).thenReturn(new ArrayList<>());
        
        // Act
        UUID result = invokeFindCategoryIdFromHierarchy(department, category, subCategory, clazz);
        
        // Assert
        Assertions.assertNull(result);
        verify(categoryApplicationService).searchCategoriesByName(clazz);
    }
    
    @Test
    void testFindCategoryIdFromHierarchy_MatchingHierarchy() throws Exception {
        // Arrange
        String department = "Department1";
        String category = "Category1";
        String subCategory = "SubCategory1";
        String clazz = "Class1";
        UUID expectedCategoryId = UUID.randomUUID();
        
        // Create a leaf category
        CategoryDto leafCategory = new CategoryDto();
        leafCategory.setCategoryId(expectedCategoryId);
        leafCategory.setCategoryName(clazz);
        
        List<CategoryDto> leafCategories = new ArrayList<>();
        leafCategories.add(leafCategory);
        
        // Create a category tree
        Map<Integer, String> categoryTree = new HashMap<>();
        categoryTree.put(DEPTH_TO_PARENT, subCategory);
        categoryTree.put(DEPTH_TO_GRANDPARENT, category);
        categoryTree.put(DEPTH_TO_GREAT_GRANDPARENT, department);
        
        when(categoryApplicationService.searchCategoriesByName(clazz)).thenReturn(leafCategories);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(expectedCategoryId)).thenReturn(categoryTree);
        
        // Act
        UUID result = invokeFindCategoryIdFromHierarchy(department, category, subCategory, clazz);
        
        // Assert
        Assertions.assertEquals(expectedCategoryId, result);
        verify(categoryApplicationService).searchCategoriesByName(clazz);
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(expectedCategoryId);
    }

}