package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.mapper.vendor.VendorDtoApplicationMapper;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class VendorApplicationServiceImplTest {

    @Mock
    VendorService vendorService;
    @Mock
    VendorDtoApplicationMapper vendorDtoApplicationMapper;
    @Mock
    FinaleApplicationService finaleApplicationService;
    @InjectMocks
    VendorApplicationServiceImpl vendorApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreate() {
        UUID id = UUID.randomUUID();
        VendorDto vendorDto = new VendorDto(id,
            "vendorName",
            "vendorContactName",
            "vendorContactTel",
            "vendorCompanyName",
            VendorStatus.ACTIVE, null, null);

        when(vendorService.findByVendorName(anyString())).thenReturn(null);
        when(vendorService.save(any(Vendor.class))).thenReturn(Vendor.builder().id(id).build());
        when(vendorDtoApplicationMapper.domainToDto(any(Vendor.class))).thenReturn(vendorDto);
        when(finaleApplicationService.createVendor(any())).thenReturn(new FinaleVendorDto("1001", "test"));
        VendorDto result = vendorApplicationServiceImpl.create(new CreateVendorCommand(id, "vendorName", null));
        Assertions.assertEquals(id, result.getId());
    }

    @Test
    void testUpdate() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        doNothing().when(vendor).updateVendorName(Mockito.<String>any());
        when(vendorService.update(Mockito.<Vendor>any())).thenReturn(null);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        when(vendorDtoApplicationMapper.domainToDto(Mockito.<Vendor>any())).thenThrow(new ImsBusinessException("Code"));
        UpdateVendorCommand command = new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null);
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> vendorApplicationServiceImpl.update(command));
        verify(vendorDtoApplicationMapper).domainToDto(isNull());
        verify(vendor).updateVendorName("Vendor Name");
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorService).update(isA(Vendor.class));
    }

    @Test
    void testUpdate_givenVendorServiceFindByIdReturnNull_thenThrowImsBusinessException() {
        // Arrange
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);
        UpdateVendorCommand command = new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null);
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> vendorApplicationServiceImpl.update(command));
        verify(vendorService).findById(isA(UUID.class));
    }

    @Test
    void testUpdate_thenReturnVendorCompanyName() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        doNothing().when(vendor).updateVendorName(Mockito.<String>any());
        when(vendorService.update(Mockito.<Vendor>any())).thenReturn(null);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        VendorDto.VendorDtoBuilder builderResult = VendorDto.builder();
        UUID id = UUID.randomUUID();
        VendorDto buildResult = builderResult.id(id)
            .vendorCompanyName("Vendor Company Name")
            .vendorContactName("Vendor Contact Name")
            .vendorContactTel("Vendor Contact Tel")
            .vendorName("Vendor Name")
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
        when(vendorDtoApplicationMapper.domainToDto(Mockito.<Vendor>any())).thenReturn(buildResult);

        // Act
        VendorDto actualUpdateResult = vendorApplicationServiceImpl
            .update(new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null));

        // Assert
        verify(vendorDtoApplicationMapper).domainToDto(isNull());
        verify(vendor).updateVendorName("Vendor Name");
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorService).update(isA(Vendor.class));
        assertEquals("Vendor Company Name", actualUpdateResult.getVendorCompanyName());
        assertEquals("Vendor Contact Name", actualUpdateResult.getVendorContactName());
        assertEquals("Vendor Contact Tel", actualUpdateResult.getVendorContactTel());
        assertEquals("Vendor Name", actualUpdateResult.getVendorName());
        assertEquals(VendorStatus.ACTIVE, actualUpdateResult.getVendorStatus());
        assertSame(id, actualUpdateResult.getId());
    }

    @Test
    void testDelete_thenReturnVendorCompanyName() {
        // Arrange
        when(vendorService.delete(Mockito.<UUID>any())).thenReturn(null);
        VendorDto.VendorDtoBuilder builderResult = VendorDto.builder();
        UUID id = UUID.randomUUID();
        VendorDto buildResult = builderResult.id(id)
            .vendorCompanyName("Vendor Company Name")
            .vendorContactName("Vendor Contact Name")
            .vendorContactTel("Vendor Contact Tel")
            .vendorName("Vendor Name")
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
        when(vendorDtoApplicationMapper.domainToDto(Mockito.<Vendor>any())).thenReturn(buildResult);

        // Act
        VendorDto actualDeleteResult = vendorApplicationServiceImpl.delete(UUID.randomUUID());

        // Assert
        verify(vendorDtoApplicationMapper).domainToDto(isNull());
        verify(vendorService).delete(isA(UUID.class));
        assertEquals("Vendor Company Name", actualDeleteResult.getVendorCompanyName());
        assertEquals("Vendor Contact Name", actualDeleteResult.getVendorContactName());
        assertEquals("Vendor Contact Tel", actualDeleteResult.getVendorContactTel());
        assertEquals("Vendor Name", actualDeleteResult.getVendorName());
        assertEquals(VendorStatus.ACTIVE, actualDeleteResult.getVendorStatus());
        assertSame(id, actualDeleteResult.getId());
    }

    @Test
    void testDelete_thenThrowImsBusinessException() {
        // Arrange
        when(vendorService.delete(Mockito.<UUID>any())).thenReturn(null);
        when(vendorDtoApplicationMapper.domainToDto(Mockito.<Vendor>any())).thenThrow(new ImsBusinessException("Code"));

        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorApplicationServiceImpl.delete(id));
        verify(vendorDtoApplicationMapper).domainToDto(isNull());
        verify(vendorService).delete(isA(UUID.class));
    }
}