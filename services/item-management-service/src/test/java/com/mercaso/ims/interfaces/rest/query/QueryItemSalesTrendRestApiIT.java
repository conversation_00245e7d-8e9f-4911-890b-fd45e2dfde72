package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueryItemSalesTrendRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchItemSalesTrend() throws Exception {
        // Given
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;

        // When
        List<ItemSalesTrendDto> itemSalesTrendDtos = queryItemSalesTrendRestApiUtil.searchItemSalesTrend(itemId, timeGrain);

        // Then
        Assertions.assertNotNull(itemSalesTrendDtos);
    }

    @Test
    void shouldSuccessWhenSearchItemSalesTrendWithWeeklyGrain() throws Exception {
        // Given
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.WEEK;

        // When
        List<ItemSalesTrendDto> itemSalesTrendDtos = queryItemSalesTrendRestApiUtil.searchItemSalesTrend(itemId, timeGrain);

        // Then
        Assertions.assertNotNull(itemSalesTrendDtos);
    }

    @Test
    void shouldSuccessWhenSearchItemSalesTrendWithMonthlyGrain() throws Exception {
        // Given
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.MONTH;

        // When
        List<ItemSalesTrendDto> itemSalesTrendDtos = queryItemSalesTrendRestApiUtil.searchItemSalesTrend(itemId, timeGrain);

        // Then
        Assertions.assertNotNull(itemSalesTrendDtos);
    }
}
