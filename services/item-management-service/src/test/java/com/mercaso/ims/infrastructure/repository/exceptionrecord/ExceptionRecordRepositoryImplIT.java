package com.mercaso.ims.infrastructure.repository.exceptionrecord;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ExceptionRecordRepositoryImplIT extends AbstractIT {

    @Test
    void testSave() {

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();

        ExceptionRecord result = exceptionRecordRepository.save(exceptionRecord);

        Assertions.assertNotNull(result);


    }

    @Test
    void testFindById() {
        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();

        ExceptionRecord savedExceptionRecord = exceptionRecordRepository.save(exceptionRecord);

        ExceptionRecord result = exceptionRecordRepository.findById(savedExceptionRecord.getId());
        Assertions.assertNotNull(result);
        Assertions.assertEquals(exceptionRecord.getDescription(), result.getDescription());

    }


    @Test
    void testDeleteById() {
        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();

        ExceptionRecord savedExceptionRecord = exceptionRecordRepository.save(exceptionRecord);
        exceptionRecordRepository.deleteById(savedExceptionRecord.getId());

        ExceptionRecord result = exceptionRecordRepository.findById(savedExceptionRecord.getId());
        Assertions.assertNull(result);

    }
}