package com.mercaso.ims.infrastructure.repository.test.jpa;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateTestOrderCommand;
import com.mercaso.ims.domain.testorder.TestOrder;
import com.mercaso.ims.domain.testorder.TestOrderFactory;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class TestOrderRepositoryImplIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSaveTestOrder() {
        String name = "testName";
        int status = 23;
        CreateTestOrderCommand command = new CreateTestOrderCommand(name, status);
        TestOrder testOrder = TestOrderFactory.create(command);
        TestOrder result = testOrderRepository.save(testOrder);
        UUID id = result.getId();
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
        Assertions.assertEquals(status, result.getStatus());
        testOrderRepository.deleteById(id);
        TestOrder result2 = testOrderRepository.findById(id);
        Assertions.assertNull(result2);

    }
}