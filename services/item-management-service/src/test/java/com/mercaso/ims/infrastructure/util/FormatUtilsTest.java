package com.mercaso.ims.infrastructure.util;

import java.math.BigDecimal;
import java.time.Instant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class FormatUtilsTest {

    @Test
    void testCurrencyFormat() {
        BigDecimal d = new BigDecimal("123.45");
        String result = FormatUtils.currencyFormat(d);
        Assertions.assertEquals("$123.45", result);
    }

    @Test
    void testCurrencyWithSymbolFormat() {
        BigDecimal d = new BigDecimal("123.45");
        String symbol = "€";
        String result = FormatUtils.currencyWithSymbolFormat(d, symbol);
        Assertions.assertEquals("€123.45", result);
    }


    @Test
    void testNumberFormat() {
        BigDecimal bigDecimal = new BigDecimal("123.456");
        String result = FormatUtils.numberFormat(bigDecimal);
        Assertions.assertEquals("123.46", result);
    }

    @Test
    void testInstantToStringWithFormat() {
        Instant instant = Instant.now();
        String format = "MM/dd/yyyy HH:mm";
        String result = FormatUtils.instantToStringWithFormat(instant, format);
        Assertions.assertNotNull(result);

    }

    @Test
    void testConvertToUpperCamel() {
        String str = "hook chassis";
        String result = FormatUtils.convertToUpperCamel(str);
        Assertions.assertEquals("Hook Chassis", result);
    }

    @Test
    void testIsLongString() {
        String str = "123";
        boolean result = FormatUtils.isLongString(str);
        Assertions.assertTrue(result);
    }

    @Test
    void testIsImageUrl() {
        String urlStr = "http://example.com/image.jpg";
        boolean result = FormatUtils.isImageUrl(urlStr);
        Assertions.assertTrue(result);
    }

    @Test
    void testCleanInput() {
        String input = "   Hello, World!   ";
        String result = FormatUtils.cleanInput(input);
        Assertions.assertEquals("Hello, World!", result);
    }

    @Test
    void testToPercentageString() {
        BigDecimal value = new BigDecimal("0.75");
        String result = FormatUtils.toPercentageString(value);
        Assertions.assertEquals("75.00%", result);
    }

    @Test
    void testToPriceFormat() {
        BigDecimal price = new BigDecimal("1234.56");
        String result = FormatUtils.toPriceFormat(price);
        Assertions.assertEquals("$1,234.56", result);
    }

    @Test
    void testToBoldString() {
        String str = "Hello, World!";
        String result = FormatUtils.toBoldString(str);
        Assertions.assertEquals("*Hello, World!*", result);
    }

}
