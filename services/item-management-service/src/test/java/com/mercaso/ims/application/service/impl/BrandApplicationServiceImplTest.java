package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BrandApplicationServiceImplTest {

    @Mock
    BrandService brandService;
    @Mock
    BrandDtoApplicationMapper brandDtoApplicationMapper;
    @InjectMocks
    BrandApplicationServiceImpl brandApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateBrand_Success() {
        // Arrange
        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        UUID id = UUID.randomUUID();
        CreateBrandCommand command = new CreateBrandCommand(brandName, "logo", "description");
        Brand savedBrand = Brand.builder().id(id).name(brandName).build();
        BrandDto expectedDto = new BrandDto(id, brandName);

        when(brandService.findByName(brandName)).thenReturn(null);
        when(brandService.save(any(Brand.class))).thenReturn(savedBrand);
        when(brandDtoApplicationMapper.domainToDto(savedBrand)).thenReturn(expectedDto);

        // Act
        BrandDto result = brandApplicationServiceImpl.createBrand(command);

        // Assert
        assertEquals(id, result.getBrandId());
        assertEquals(brandName, result.getBrandName());
        verify(brandService).findByName(brandName);
        verify(brandService).save(any(Brand.class));
        verify(brandDtoApplicationMapper).domainToDto(savedBrand);
    }

    @Test
    void testCreateBrand_InvalidBrandName_Empty() {
        // Arrange
        CreateBrandCommand command = new CreateBrandCommand("", "logo", "description");

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.INVALID_BRAND_NAME.getCode(), exception.getCode());
        verify(brandService, never()).findByName(anyString());
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_InvalidBrandName_Blank() {
        // Arrange
        CreateBrandCommand command = new CreateBrandCommand("   ", "logo", "description");

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.INVALID_BRAND_NAME.getCode(), exception.getCode());
        verify(brandService, never()).findByName(anyString());
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_BrandAlreadyExists() {
        // Arrange
        String brandName = "ExistingBrand";
        CreateBrandCommand command = new CreateBrandCommand(brandName, "logo", "description");
        Brand existingBrand = Brand.builder().id(UUID.randomUUID()).name(brandName).build();

        when(brandService.findByName(brandName)).thenReturn(existingBrand);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_ALREADY_EXIST.getCode(), exception.getCode());
        verify(brandService).findByName(brandName);
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_SuccessWithTrimmedName() {
        // Arrange
        String brandNameWithSpaces = "  TestBrand  ";
        String trimmedBrandName = "TestBrand";
        UUID id = UUID.randomUUID();
        CreateBrandCommand command = new CreateBrandCommand(brandNameWithSpaces, "logo", "description");
        Brand savedBrand = Brand.builder().id(id).name(trimmedBrandName).build();
        BrandDto expectedDto = new BrandDto(id, trimmedBrandName);

        when(brandService.findByName(trimmedBrandName)).thenReturn(null);
        when(brandService.save(any(Brand.class))).thenReturn(savedBrand);
        when(brandDtoApplicationMapper.domainToDto(savedBrand)).thenReturn(expectedDto);

        // Act
        BrandDto result = brandApplicationServiceImpl.createBrand(command);

        // Assert
        assertEquals(id, result.getBrandId());
        assertEquals(trimmedBrandName, result.getBrandName());
        verify(brandService).findByName(trimmedBrandName);
        verify(brandService).save(any(Brand.class));
        verify(brandDtoApplicationMapper).domainToDto(savedBrand);
    }
}