package com.mercaso.ims.infrastructure.repository.itemcostchangerequest;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemCostCollectionItemRepositoryImplIT extends AbstractIT {


    @Test
    void testSave() {
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        ItemCostChangeRequest result = itemCostChangeRequestRepository.save(itemCostChangeRequest);
        Assertions.assertEquals(itemCostChangeRequest.getVendorId(), result.getVendorId());
        Assertions.assertEquals(itemCostChangeRequest.getItemCostCollectionId(), result.getItemCostCollectionId());
        Assertions.assertEquals(itemCostChangeRequest.getItemId(), result.getItemId());
    }

    @Test
    void testFindById() {
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        ItemCostChangeRequest savedItemCostChangeRequest = itemCostChangeRequestRepository.save(itemCostChangeRequest);

        ItemCostChangeRequest result = itemCostChangeRequestRepository.findById(savedItemCostChangeRequest.getId());
        Assertions.assertNotNull(result);

        Assertions.assertEquals(savedItemCostChangeRequest.getId(), result.getId());
        Assertions.assertEquals(savedItemCostChangeRequest.getItemCostCollectionId(), result.getItemCostCollectionId());
        Assertions.assertEquals(itemCostChangeRequest.getItemId(), result.getItemId());
    }

    @Test
    void testUpdate() {
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        ItemCostChangeRequest savedItemCostChangeRequest = itemCostChangeRequestRepository.save(itemCostChangeRequest);
        ItemCostChangeRequest updatedItemCostChangeRequest = itemCostChangeRequestRepository.findById(savedItemCostChangeRequest.getId());
        updatedItemCostChangeRequest.setStatus(ItemCostChangeRequestStatus.REJECTED);
        ItemCostChangeRequest result = itemCostChangeRequestRepository.update(updatedItemCostChangeRequest);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(ItemCostChangeRequestStatus.REJECTED, result.getStatus());
    }

    @Test
    void testDeleteById() {
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        ItemCostChangeRequest savedItemCostChangeRequest = itemCostChangeRequestRepository.save(itemCostChangeRequest);

        ItemCostChangeRequest result = itemCostChangeRequestRepository.deleteById(savedItemCostChangeRequest.getId());
        Assertions.assertNotNull(result.getDeletedAt());
    }
}