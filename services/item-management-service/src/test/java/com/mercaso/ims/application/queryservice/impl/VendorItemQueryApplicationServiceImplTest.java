package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.mapper.vendoritem.VendorItemDtoApplicationMapper;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorItemQueryApplicationServiceImpl.class})
class VendorItemQueryApplicationServiceImplTest {

    private ItemService itemService;
    private VendorService vendorService;
    private VendorItemService vendorItemService;
    private VendorItemDtoApplicationMapper vendorItemMapper;
    private FeatureFlagsManager featureFlagsManager;

    private VendorItemQueryApplicationServiceImpl service;

    @BeforeEach
    void setUp() {
        itemService = mock(ItemService.class);
        vendorService = mock(VendorService.class);
        vendorItemService = mock(VendorItemService.class);
        vendorItemMapper = mock(VendorItemDtoApplicationMapper.class);
        featureFlagsManager = mock(FeatureFlagsManager.class);

        service = new VendorItemQueryApplicationServiceImpl(
            itemService, vendorService, vendorItemService, vendorItemMapper
        );
    }

    @Test
    void testFindByVendorIdAndItemIds() {
        UUID vendorId = UUID.randomUUID();
        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();

        Vendor vendor = Vendor.builder().id(vendorId).vendorName("VendorA").build();

        VendorItem vendorItem1 = VendorItem.builder().itemId(itemId1).vendorId(vendorId).build();

        VendorItem vendorItem2 = VendorItem.builder().itemId(itemId2).vendorId(vendorId).build();

        VendorItemDto dto1 = new VendorItemDto();
        dto1.setVendorName("VendorA");
        dto1.setVendorFinaleId("F123");
        VendorItemDto dto2 = new VendorItemDto();
        dto2.setVendorName("VendorA");
        dto2.setVendorFinaleId("F123");

        when(vendorService.findAll()).thenReturn(List.of(vendor));
        when(vendorItemService.findByVendorIdAndItemIdIn(eq(vendorId), ArgumentMatchers.anyList()))
            .thenReturn(List.of(vendorItem1, vendorItem2));
        when(vendorItemMapper.domainToDto(vendorItem1)).thenReturn(dto1);
        when(vendorItemMapper.domainToDto(vendorItem2)).thenReturn(dto2);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        List<VendorItemDto> result = service.findByVendorIdAndItemIds(vendorId, Arrays.asList(itemId1, itemId2));

        assertEquals(2, result.size());

        assertEquals("VendorA", dto1.getVendorName());
        assertEquals("F123", dto1.getVendorFinaleId());

        assertNull(dto1.getCost());
        assertNull(dto2.getCost());

        verify(vendorService, times(1)).findAll();
        verify(vendorItemService, times(1)).findByVendorIdAndItemIdIn(eq(vendorId), anyList());
        verify(vendorItemMapper, times(2)).domainToDto(any(VendorItem.class));
    }
}
