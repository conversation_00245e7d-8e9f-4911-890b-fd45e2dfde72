package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.event.ItemCostCollectionCreatedEvent;
import com.mercaso.ims.application.dto.payload.ItemCostCollectionCreatedPayloadDto;
import com.mercaso.ims.application.service.ItemCostChangeRequestApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.process.matcher.VendorItemMatcher;
import com.mercaso.ims.infrastructure.process.parser.ItemCostCollectionParser;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionDtoUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ItemCostCollectionApplicationEventListenerTest {


    ItemCostCollectionParser mockItemCostCollectionParser = Mockito.mock(ItemCostCollectionParser.class);


    VendorItemMatcher vendorItemMatcher = Mockito.mock(VendorItemMatcher.class);

    ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService = Mockito.mock(
        ItemCostChangeRequestApplicationService.class);

    ItemService itemService = Mockito.mock(ItemService.class);
    VendorItemService vendorItemService = Mockito.mock(VendorItemService.class);
    VendorService vendorService = Mockito.mock(VendorService.class);
    VendorItemApplicationService vendorItemApplicationService = Mockito.mock(VendorItemApplicationService.class);
    ItemRegPriceService itemRegPriceService = Mockito.mock(ItemRegPriceService.class);


    ItemCostCollectionApplicationEventListener itemCostCollectionApplicationEventListener = new ItemCostCollectionApplicationEventListener(
        List.of(mockItemCostCollectionParser),
        List.of(vendorItemMatcher),
        itemCostChangeRequestApplicationService,
        itemService,
        vendorItemService,
        vendorService,
        vendorItemApplicationService,
        itemRegPriceService);

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHandleItemCostCollectionCreatedEvent() {
        Vendor vendor = VendorUtil.buildVendor("vendor");
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();

        ItemCostCollectionItemParsingResultDto itemCostCollectionItemParsingResultDto = new ItemCostCollectionItemParsingResultDto();
        itemCostCollectionItemParsingResultDto.setVendorItemName("vendorItemName");
        itemCostCollectionItemParsingResultDto.setVendorSkuNumber("test");
        itemCostCollectionItemParsingResultDto.setCost(BigDecimal.TEN);
        itemCostCollectionItemParsingResultDto.setUpc("UPC");

        when(mockItemCostCollectionParser.isSupported(any(), any())).thenReturn(true);
        when(vendorItemMatcher.isSupported(any(), any())).thenReturn(true);
        when(mockItemCostCollectionParser.parse(any())).thenReturn(List.of(itemCostCollectionItemParsingResultDto));
        when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
        when(vendorItemService.findByVendorID(any(UUID.class))).thenReturn(List.of(vendorItem));

        when(itemCostChangeRequestApplicationService.createItemCostChangeRequest(any(CreateItemCostChangeRequestCommand.class))).thenReturn(
            new ItemCostChangeRequestDto(UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                "skuNumber",
                "ACTIVE",
                "vendorSkuNumber",
                "vendorItemName",
                new BigDecimal(0),
                new BigDecimal(0),
                new BigDecimal(0),
                new BigDecimal(0),
                new BigDecimal(0),
                MatchedType.MISS_MATCHED,
                ItemCostChangeRequestStatus.PENDING,
                UUID.randomUUID(),
                true,
                UUID.randomUUID(),
                true,
                "upc",
                true,
                CostType.DIRECT_COST.getCostTypeName(), null
            ));
        ItemCostCollectionDto itemCostCollectionDto = ItemCostCollectionDtoUtil.buildItemCostCollectionDto();
        itemCostCollectionDto.setVendorName(VendorConstant.VERNON_SALES);
        itemCostCollectionApplicationEventListener.handleItemCostCollectionCreatedEvent(new ItemCostCollectionCreatedEvent(
            "source",
            new ItemCostCollectionCreatedPayloadDto(itemCostCollectionDto, UUID.randomUUID())));
        verify(mockItemCostCollectionParser).parse(any());
    }
}