package com.mercaso.ims.interfaces.rest.search;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemPriceExceptionRecordListDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordListDto;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SearchExceptionRecordRestApiIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSearchPriceExceptionRecord() throws Exception {
        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        String title = itemDo.getTitle().substring(2);

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();
        exceptionRecord.setEntityId(itemDo.getId());
        exceptionRecordRepository.save(exceptionRecord);
        ItemPriceExceptionRecordListDto result = searchExceptionRecordRestApiUtil.searchPriceExceptionRecord(null,
            null,
            sku,
            title,
            null,
            null,
            null);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getTotalCount());
        Assertions.assertEquals(itemDo.getId(), result.getData().get(0).getItemId());

    }

    @Test
    void shouldSuccessWhenSearchCostExceptionRecord() throws Exception {
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemDataWithVendor(sku, DOWNEY_WHOLESALE, vendorItemNumber);

        VendorItemDo vendorItem = vendorItemJpaDao.findByVendorIdAndItemId(itemDo.getPrimaryVendorId(), itemDo.getId());

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildCostExceptionRecord();
        exceptionRecord.setEntityId(vendorItem.getId());
        exceptionRecordRepository.save(exceptionRecord);
        VendorItemCostExceptionRecordListDto result = searchExceptionRecordRestApiUtil.searchCostExceptionRecord(null,
            null,
            sku,
            null,
            null,
            null,
            null,
            null);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getTotalCount());
        Assertions.assertEquals(itemDo.getId(), result.getData().get(0).getItemId());

    }

}
