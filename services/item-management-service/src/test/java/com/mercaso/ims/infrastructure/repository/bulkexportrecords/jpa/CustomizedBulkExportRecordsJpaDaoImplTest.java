package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa;

import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.AdditionalMatchers.*;

@ExtendWith(MockitoExtension.class)
class CustomizedBulkExportRecordsJpaDaoImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private CustomizedBulkExportRecordsJpaDaoImpl dao;

    private BulkExportRecordsQuery testQuery;
    private List<BulkExportRecordsDto> mockData;
    private Instant testStartDate;
    private Instant testEndDate;

    @BeforeEach
    void setUp() {
        testStartDate = Instant.now().minus(7, ChronoUnit.DAYS);
        testEndDate = Instant.now();

        testQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(10)
            .createdStartDate(testStartDate)
            .createdEndDate(testEndDate)
            .createdBy("testUser")
            .sort(BulkExportRecordsQuery.SortType.CREATED_AT_DESC)
            .build();

        mockData = Arrays.asList(
            BulkExportRecordsDto.builder()
                .id(UUID.randomUUID())
                .fileName("export1.xlsx")
                .searchTime(Instant.now())
                .exportBy("testUser")
                .createdAt(Instant.now())
                .build(),
            BulkExportRecordsDto.builder()
                .id(UUID.randomUUID())
                .fileName("export2.xlsx")
                .searchTime(Instant.now())
                .exportBy("testUser")
                .createdAt(Instant.now())
                .build()
        );
    }

    @Test
    void getBulkExportRecords_WithValidQuery_ShouldReturnResults() {
        // Given
        // Mock count query (returns Long)
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(2L));

        // Mock data query (returns BulkExportRecordsDto)
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(mockData);

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(testQuery);

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getTotalCount());
        assertEquals(2, result.getData().size());
        assertEquals("export1.xlsx", result.getData().get(0).getFileName());
        assertEquals("export2.xlsx", result.getData().get(1).getFileName());

        // Verify that jdbcTemplate.query was called twice (count + data)
        verify(jdbcTemplate, times(2)).query(anyString(), any(PreparedStatementSetter.class), any(RowMapper.class));
    }

    @Test
    void getBulkExportRecords_WithNullQuery_ShouldReturnEmptyResult() {
        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(null);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertTrue(result.getData().isEmpty());

        // Verify no database calls were made
        verifyNoInteractions(jdbcTemplate);
    }

    @Test
    void getBulkExportRecords_WithMinimalQuery_ShouldReturnResults() {
        // Given
        BulkExportRecordsQuery minimalQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(20)
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(1L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(mockData.getFirst()));

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(minimalQuery);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getTotalCount());
        assertEquals(1, result.getData().size());
    }

    @Test
    void getBulkExportRecords_WithDateRangeOnly_ShouldBuildCorrectQuery() {
        // Given
        BulkExportRecordsQuery dateQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(10)
            .createdStartDate(testStartDate)
            .createdEndDate(testEndDate)
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(0L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of());

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(dateQuery);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertTrue(result.getData().isEmpty());

        // Verify SQL contains date conditions
        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(jdbcTemplate, times(2)).query(sqlCaptor.capture(), any(PreparedStatementSetter.class), any(RowMapper.class));

        String countSql = sqlCaptor.getAllValues().get(0);
        String dataSql = sqlCaptor.getAllValues().get(1);

        assertTrue(countSql.contains("ber.created_at >= ?"));
        assertTrue(countSql.contains("ber.created_at <= ?"));
        assertTrue(dataSql.contains("ber.created_at >= ?"));
        assertTrue(dataSql.contains("ber.created_at <= ?"));
    }

    @Test
    void getBulkExportRecords_WithUserNameOnly_ShouldBuildCorrectQuery() {
        // Given
        BulkExportRecordsQuery userQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(10)
            .createdBy("specificUser")
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(1L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(mockData.get(0)));

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(userQuery);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getTotalCount());
        assertEquals(1, result.getData().size());

        // Verify SQL contains user name condition
        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(jdbcTemplate, times(2)).query(sqlCaptor.capture(), any(PreparedStatementSetter.class), any(RowMapper.class));

        String countSql = sqlCaptor.getAllValues().get(0);
        String dataSql = sqlCaptor.getAllValues().get(1);

        assertTrue(countSql.contains("ber.created_by = ?"));
        assertTrue(dataSql.contains("ber.created_by = ?"));
    }

    @Test
    void getBulkExportRecords_WithException_ShouldReturnEmptyResult() {
        // Given
        when(jdbcTemplate.query(anyString(), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenThrow(new RuntimeException("Database error"));

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(testQuery);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void getBulkExportRecords_WithPagination_ShouldIncludeLimitOffset() {
        // Given
        BulkExportRecordsQuery paginationQuery = BulkExportRecordsQuery.builder()
            .page(2)
            .pageSize(5)
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(10L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(mockData);

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(paginationQuery);

        // Then
        assertNotNull(result);
        assertEquals(10L, result.getTotalCount());
        assertEquals(2, result.getData().size());

        // Verify SQL contains LIMIT and OFFSET
        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(jdbcTemplate, times(2)).query(sqlCaptor.capture(), any(PreparedStatementSetter.class), any(RowMapper.class));

        String dataSql = sqlCaptor.getAllValues().get(1);
        assertTrue(dataSql.contains("LIMIT"));
        assertTrue(dataSql.contains("OFFSET"));
    }

    @Test
    void getBulkExportRecords_WithSortParameter_ShouldIncludeOrderBy() {
        // Given
        BulkExportRecordsQuery sortQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(10)
            .sort(BulkExportRecordsQuery.SortType.CREATED_AT_DESC)
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(1L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(mockData);

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(sortQuery);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getTotalCount());

        // Verify SQL contains ORDER BY
        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(jdbcTemplate, times(2)).query(sqlCaptor.capture(), any(PreparedStatementSetter.class), any(RowMapper.class));

        String dataSql = sqlCaptor.getAllValues().get(1);
        assertTrue(dataSql.contains("ORDER BY ber.created_at DESC"));
    }

    @Test
    void getBulkExportRecords_WithEmptyUserName_ShouldNotIncludeUserCondition() {
        // Given
        BulkExportRecordsQuery emptyUserQuery = BulkExportRecordsQuery.builder()
            .page(1)
            .pageSize(10)
            .createdBy("")
            .build();

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(0L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of());

        // When
        BulkExportRecordsSearchDto result = dao.getBulkExportRecords(emptyUserQuery);

        // Then
        assertNotNull(result);
        assertEquals(0L, result.getTotalCount());

        // Verify SQL does not contain user name condition
        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(jdbcTemplate, times(2)).query(sqlCaptor.capture(), any(PreparedStatementSetter.class), any(RowMapper.class));

        String countSql = sqlCaptor.getAllValues().getFirst();
        assertFalse(countSql.contains("ber.created_user_name ILIKE ?"));
    }

    @Test
    void getBulkExportRecords_PreparedStatementSetter_ShouldSetParametersCorrectly() throws SQLException {
        // Given
        ArgumentCaptor<PreparedStatementSetter> setterCaptor = ArgumentCaptor.forClass(PreparedStatementSetter.class);

        // Mock count query
        when(jdbcTemplate.query(contains("COUNT(*)"), setterCaptor.capture(), any(RowMapper.class)))
            .thenReturn(List.of(1L));

        // Mock data query
        when(jdbcTemplate.query(not(contains("COUNT(*)")), setterCaptor.capture(), any(RowMapper.class)))
            .thenReturn(mockData);

        // When
        dao.getBulkExportRecords(testQuery);

        // Then
        List<PreparedStatementSetter> setters = setterCaptor.getAllValues();
        assertEquals(2, setters.size()); // One for count, one for data

        // Test parameter setting for count query
        PreparedStatement mockPs = mock(PreparedStatement.class);
        setters.getFirst().setValues(mockPs);

        verify(mockPs).setTimestamp(1, Timestamp.from(testStartDate));
        verify(mockPs).setTimestamp(2, Timestamp.from(testEndDate));
        verify(mockPs).setString(3, "testUser");
    }

    @Test
    void getBulkExportRecords_RowMapper_ShouldMapResultSetCorrectly() throws SQLException {
        // Given
        ResultSet mockRs = mock(ResultSet.class);
        UUID testId = UUID.randomUUID();
        Instant testTime = Instant.now();

        when(mockRs.getString("id")).thenReturn(testId.toString());
        when(mockRs.getString("file_name")).thenReturn("test.csv");
        when(mockRs.getTimestamp("search_time")).thenReturn(Timestamp.from(testTime));
        when(mockRs.getTimestamp("send_email_time")).thenReturn(Timestamp.from(testTime.plusSeconds(300)));
        when(mockRs.getString("custom_filter")).thenReturn("{\"test\":true}");
        when(mockRs.getString("created_user_name")).thenReturn("testUser");
        when(mockRs.getTimestamp("created_at")).thenReturn(Timestamp.from(testTime));

        ArgumentCaptor<RowMapper<BulkExportRecordsDto>> mapperCaptor = ArgumentCaptor.forClass(RowMapper.class);

        // Mock the count query (returns Long)
        when(jdbcTemplate.query(contains("COUNT(*)"), any(PreparedStatementSetter.class), any(RowMapper.class)))
            .thenReturn(List.of(1L));

        // Mock the data query (returns BulkExportRecordsDto)
        when(jdbcTemplate.query(not(contains("COUNT(*)")), any(PreparedStatementSetter.class), mapperCaptor.capture()))
            .thenReturn(mockData);

        // When
        dao.getBulkExportRecords(testQuery);

        // Then
        RowMapper<BulkExportRecordsDto> mapper = mapperCaptor.getValue(); // Get the captured mapper
        BulkExportRecordsDto result = mapper.mapRow(mockRs, 1);

        assertNotNull(result);
        assertEquals(testId, result.getId());
        assertEquals("test.csv", result.getFileName());
        assertEquals(testTime, result.getSearchTime());
        assertEquals("testUser", result.getExportBy());
    }
}
