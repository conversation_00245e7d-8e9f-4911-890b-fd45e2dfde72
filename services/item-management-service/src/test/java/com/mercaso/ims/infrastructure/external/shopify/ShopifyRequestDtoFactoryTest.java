package com.mercaso.ims.infrastructure.external.shopify;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.infrastructure.external.shopify.dto.InventoryItemDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.MetafieldDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.MetafieldInfoDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class ShopifyRequestDtoFactoryTest {


    @Test
    void testBuildShopifyProductDto() {
        // Arrange
        ShopifyProductDto.ProductDto product = ShopifyRequestDtoFactory.buildShopifyProductDto(new ItemDto(), null)
            .getProduct();
        List<ShopifyProductDto.Variant> variants = product.getVariants();
        List<ShopifyProductDto.Options> options = product.getOptions();

        // Assert product fields
        assertProductFields(product);

        // Assert variant fields
        assertVariantFields(variants, options);

        // Assert common fields for all variants
        assertCommonVariantFields(variants);

        // Assert null values and specific fields
        assertNullFields(options, variants);
    }

    private void assertProductFields(ShopifyProductDto.ProductDto product) {
        assertEquals(3, product.getVariants().size());
        assertEquals(1, product.getOptions().size());
        assertEquals("draft", product.getStatus());
        assertEquals("web", product.getPublishedScope());
        assertNull(product.getImage());
        assertNull(product.getId());
        assertNull(product.getBodyHtml());
        assertNull(product.getHandle());
        assertNull(product.getProductType());
        assertNull(product.getTags());
        assertNull(product.getTemplateSuffix());
        assertNull(product.getTitle());
        assertNull(product.getVendor());
        assertNull(product.getImages());
    }

    private void assertVariantFields(List<ShopifyProductDto.Variant> variants, List<ShopifyProductDto.Options> options) {
        assertEquals("Individual", variants.get(0).getTitle());
        assertEquals("Pack No CRV", variants.get(1).getTitle());
        assertEquals("Pack", variants.get(2).getTitle());

        List<String> values = options.getFirst().getValues();
        assertEquals(3, values.size());
        assertEquals("Individual", values.get(0));
        assertEquals("Pack No CRV", values.get(1));
        assertEquals("Pack", values.get(2));
        assertEquals("Title", options.getFirst().getName());
        assertEquals(1, options.getFirst().getPosition().intValue());
    }

    private void assertCommonVariantFields(List<ShopifyProductDto.Variant> variants) {
        for (ShopifyProductDto.Variant variant : variants) {
            assertEquals("deny", variant.getInventoryPolicy());
            assertEquals("manual", variant.getFulfillmentService());
            assertEquals("shopify", variant.getInventoryManagement());
            assertFalse(variant.getTaxable());
        }
    }

    private void assertNullFields(List<ShopifyProductDto.Options> options, List<ShopifyProductDto.Variant> variants) {
        assertNull(options.getFirst().getId());
        assertNull(options.getFirst().getProductId());

        for (ShopifyProductDto.Variant variant : variants) {
            assertNull(variant.getRequiresShipping());
            assertNull(variant.getWeight());
            assertNull(variant.getGrams());
            assertNull(variant.getPosition());
            assertNull(variant.getId());
            assertNull(variant.getImageId());
            assertNull(variant.getInventoryItemId());
            assertNull(variant.getInventoryQuantity());
            assertNull(variant.getBarcode());
            assertNull(variant.getCompareAtPrice());
            assertNull(variant.getOption1());
            assertNull(variant.getOption2());
            assertNull(variant.getOption3());
            assertNull(variant.getSku());
            assertNull(variant.getWeightUnit());
            assertNull(variant.getPrice());
        }
    }


    @Test
    void testBuildShopifyProductDto2() {
        // Arrange
        ItemDto itemDto = new ItemDto();
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = mock(ShopifyGraphQLQueryResponseDto.class);
        when(shopifyGraphQLQueryResponseDto.getData()).thenThrow(new IllegalStateException("foo"));

        // Act and Assert
        assertThrows(IllegalStateException.class,
            () -> ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto, shopifyGraphQLQueryResponseDto));
        verify(shopifyGraphQLQueryResponseDto).getData();
    }


    @Test
    void testBuildVariantImageDto() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).build();

        ShopifyGraphQLQueryResponseDto.DataDto data = new ShopifyGraphQLQueryResponseDto.DataDto();
        data.setProducts(new ShopifyGraphQLQueryResponseDto.ProductsDto());
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = ShopifyGraphQLQueryResponseDto.builder()
            .data(data)
            .build();

        // Act and Assert
        assertNull(ShopifyRequestDtoFactory.buildVariantImageDto(itemDto, shopifyGraphQLQueryResponseDto));
    }


    @Test
    void testExtractId() {
        // Arrange, Act and Assert
        assertEquals(42L, ShopifyRequestDtoFactory.extractId("42").longValue());
        assertNull(ShopifyRequestDtoFactory.extractId("/"));
    }


    @Test
    void testBuildMetaFieldDto() {
        // Arrange and Act
        List<MetafieldDto> actualBuildMetaFieldDtoResult = ShopifyRequestDtoFactory.buildMetaFieldDto(new ItemDto());

        // Assert
        assertTrue(actualBuildMetaFieldDtoResult.isEmpty());
    }


    @Test
    void testBuildMetaFieldDto2() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(UUID.randomUUID())
            .brandName("Brand Name")
            .build();
        CategoryDto categoryTree = CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("Category Name")
            .build();
        ItemRegPriceDto itemRegPrice = ItemRegPriceDto.builder()
            .crv(new BigDecimal("2.30"))
            .itemRegPriceId(UUID.randomUUID())
            .regPrice(new BigDecimal("2.30"))
            .regPriceIndividual(new BigDecimal("2.30"))
            .regPricePlusCrv(new BigDecimal("2.30"))
            .build();
        VendorItemDto primaryVendorItem = VendorItemDto.builder()
            .vendorId(UUID.randomUUID())
            .vendorItemId(UUID.randomUUID())
            .aisle("Aisle")
            .cost(new BigDecimal("2.30"))
            .highestCost(new BigDecimal("2.30"))
            .lowestCost(new BigDecimal("2.30"))
            .note("Note")
            .statusChangeReason("Just cause")
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto itemDto = bodyHtmlResult
            .brand(brand)
            .brandId(UUID.randomUUID())
            .category("Category")
            .categoryId(UUID.randomUUID())
            .categoryName("Category Name")
            .categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .id(UUID.randomUUID())
            .itemAttributes(new ArrayList<>())
            .itemImages(new ArrayList<>())
            .itemPromoPrices(new ArrayList<>())
            .itemRegPrice(itemRegPrice)
            .itemTags(new ArrayList<>())
            .itemType("Item Type")
            .itemUPCs(new ArrayList<>())
            .locationId(3)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(UUID.randomUUID())
            .primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .vendorItemDtos(new ArrayList<>())
            .build();

        // Act
        List<MetafieldDto> actualBuildMetaFieldDtoResult = ShopifyRequestDtoFactory.buildMetaFieldDto(itemDto);

        // Assert
        assertEquals(12, actualBuildMetaFieldDtoResult.size());

        Map<String, String> expectedValues = Map.of(
            "brand", "Brand Name",
            "company_id", "1",
            "id_tag", "1_3",
            "pack_no_crv", "2.30",
            "each", "2.30",
            "location_id", "3",
            "product_title", "New Description"
        );

        for (MetafieldDto metafieldDto : actualBuildMetaFieldDtoResult) {
            MetafieldInfoDto metafield = metafieldDto.getMetafield();
            String key = metafield.getKey();
            if (expectedValues.containsKey(key)) {
                assertEquals(expectedValues.get(key), metafield.getValue());
            }
            assertNull(metafield.getId());
            assertNull(metafield.getOwnerId());
            assertNull(metafield.getAdminGraphqlApiId());
            assertNull(metafield.getDescription());
            assertNull(metafield.getOwnerResource());
            assertNull(metafield.getCreatedAt());
            assertNull(metafield.getUpdatedAt());
        }

        assertEquals("single_line_text_field", actualBuildMetaFieldDtoResult.get(2).getMetafield().getType());
        assertEquals("number_decimal", actualBuildMetaFieldDtoResult.get(3).getMetafield().getType());
        assertEquals("number_decimal", actualBuildMetaFieldDtoResult.get(4).getMetafield().getType());
        assertEquals("number_integer", actualBuildMetaFieldDtoResult.get(0).getMetafield().getType());
        assertEquals("single_line_text_field", actualBuildMetaFieldDtoResult.get(7).getMetafield().getType());
    }


    @Test
    void testBuildMetaFieldDto3() {
        // Arrange
        ItemDto itemDto = mock(ItemDto.class);
        BrandDto brandDto = BrandDto.builder()
            .brandId(UUID.randomUUID())
            .brandName("Brand Name")
            .build();
        when(itemDto.getBrand()).thenReturn(brandDto);

        ItemRegPriceDto itemRegPriceDto = ItemRegPriceDto.builder()
            .crv(new BigDecimal("2.30"))
            .itemRegPriceId(UUID.randomUUID())
            .regPrice(new BigDecimal("2.30"))
            .regPriceIndividual(new BigDecimal("2.30"))
            .regPricePlusCrv(new BigDecimal("2.30"))
            .build();

        UUID bottleSizeAttributeId = UUID.fromString("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0");
        ItemAttributeDto itemAttributeDto = ItemAttributeDto.builder().attributeId(bottleSizeAttributeId).value("12").build();
        when(itemDto.getItemRegPrice()).thenReturn(itemRegPriceDto);
        when(itemDto.getItemSize()).thenReturn(10.0f);
        when(itemDto.getCompanyId()).thenReturn(1);
        when(itemDto.getLocationId()).thenReturn(1);
        when(itemDto.getPackageSize()).thenReturn(3);
        when(itemDto.getBottleSizeAttributeV2()).thenReturn(itemAttributeDto);
        when(itemDto.getItemSizeUnitMeasure()).thenReturn("Item Size Unit Measure");
        when(itemDto.getNewDescription()).thenReturn("New Description");

        // Act
        List<MetafieldDto> metaFieldDtoList = ShopifyRequestDtoFactory.buildMetaFieldDto(itemDto);

        // Assert
        assertEquals(12, metaFieldDtoList.size());

        Map<String, String> expectedValues = Maps.newHashMap();
        expectedValues.put("item_size", "10.0");
        expectedValues.put("company_id", "1");
        expectedValues.put("location_id", "1");
        expectedValues.put("pack_size", "3");
        expectedValues.put("unit_size", "{\"value\":\"12\",\"unit\":\"fl_oz\"}");
        expectedValues.put("unit_of_measure", "Item Size Unit Measure");
        expectedValues.put("product_title", "New Description");
        expectedValues.put("brand", "Brand Name");
        expectedValues.put("pack_no_crv", "2.30");
        expectedValues.put("each", "2.30");
        expectedValues.put("id_tag", "1_1");
        expectedValues.put("item_weight_pounds", "0.0");

        // Assert for each metafield, matching key and value
        for (MetafieldDto metafieldDto : metaFieldDtoList) {
            String key = metafieldDto.getMetafield().getKey();
            assertTrue(expectedValues.containsKey(key));
            assertEquals(expectedValues.get(key), metafieldDto.getMetafield().getValue());
        }

        // Validate common properties for all Metafields
        for (MetafieldDto metafieldDto : metaFieldDtoList) {
            MetafieldInfoDto metafield = metafieldDto.getMetafield();
            assertNull(metafield.getId());
            assertNull(metafield.getOwnerId());
            assertNull(metafield.getAdminGraphqlApiId());
            assertNull(metafield.getDescription());
            assertNull(metafield.getOwnerResource());
            assertNull(metafield.getCreatedAt());
            assertNull(metafield.getUpdatedAt());
        }

        // Validate types based on the key
        Map<String, String> expectedTypes = Maps.newHashMap();
        expectedTypes.put("brand", "single_line_text_field");
        expectedTypes.put("item_size", "number_decimal");
        expectedTypes.put("unit_size", "volume");
        expectedTypes.put("pack_no_crv", "number_decimal");
        expectedTypes.put("each", "number_decimal");
        expectedTypes.put("pack_size", "number_integer");
        expectedTypes.put("company_id", "number_integer");
        expectedTypes.put("location_id", "number_integer");
        expectedTypes.put("product_title", "single_line_text_field");
        expectedTypes.put("unit_of_measure", "single_line_text_field");
        expectedTypes.put("id_tag", "single_line_text_field");
        expectedTypes.put("item_weight_pounds", "number_decimal");

        for (MetafieldDto metafieldDto : metaFieldDtoList) {
            String key = metafieldDto.getMetafield().getKey();
            assertEquals(expectedTypes.get(key), metafieldDto.getMetafield().getType());
        }
    }


    @Test
    void testBuildMetaFieldDto4() {
        // Arrange
        ItemDto itemDto = mock(ItemDto.class);
        BrandDto buildResult = BrandDto.builder()
            .brandId(UUID.randomUUID())
            .brandName("Brand Name")
            .build();
        when(itemDto.getBrand()).thenReturn(buildResult);

        ItemRegPriceDto buildResult2 = ItemRegPriceDto.builder()
            .crv(new BigDecimal("2.3"))
            .itemRegPriceId(UUID.randomUUID())
            .regPrice(new BigDecimal("2.3"))
            .regPriceIndividual(new BigDecimal("2.3"))
            .regPricePlusCrv(new BigDecimal("2.3"))
            .build();
        when(itemDto.getItemRegPrice()).thenReturn(buildResult2);
        when(itemDto.getItemSize()).thenReturn(1f);
        when(itemDto.getCompanyId()).thenReturn(1);
        when(itemDto.getLocationId()).thenReturn(null);
        when(itemDto.getPackageSize()).thenReturn(3);
        when(itemDto.getItemSizeUnitMeasure()).thenReturn("Item Size Unit Measure");
        when(itemDto.getNewDescription()).thenReturn("New Description");

        // Act
        List<MetafieldDto> actualBuildMetaFieldDtoResult = ShopifyRequestDtoFactory.buildMetaFieldDto(itemDto);

        // Assert
        verify(itemDto, atLeast(1)).getBrand();
        verify(itemDto, atLeast(1)).getCompanyId();
        verify(itemDto, atLeast(1)).getItemRegPrice();
        verify(itemDto, atLeast(1)).getItemSize();
        verify(itemDto).getItemSizeUnitMeasure();
        verify(itemDto, atLeast(1)).getLocationId();
        verify(itemDto).getNewDescription();
        verify(itemDto).getPackageSize();

        // Assert general properties of the MetafieldDtos
        assertEquals(9, actualBuildMetaFieldDtoResult.size());

        // Metafield values and their corresponding keys
        String[] expectedValues = {"1.0", "10.0", "Brand Name", "2.3", "2.3", "3",
            "Item Size Unit Measure", "New Description"};

        // Loop through and validate all metafield DTOs
        for (int i = 0; i < expectedValues.length; i++) {
            MetafieldInfoDto metafield = actualBuildMetaFieldDtoResult.get(i).getMetafield();
            assertNull(metafield.getId());
            assertNull(metafield.getOwnerId());
            assertNull(metafield.getAdminGraphqlApiId());
            assertNull(metafield.getDescription());
            assertNull(metafield.getOwnerResource());
            assertNull(metafield.getCreatedAt());
            assertNull(metafield.getUpdatedAt());
        }
    }


    @Test
    void testBuildInventoryItemDto() {
        // Arrange
        ItemDto itemDto = new ItemDto();

        // Act and Assert
        assertNull(ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto, new ShopifyProductDto()));
    }

    @Test
    void testBuildInventoryItemDto2() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).build();
        ShopifyProductDto.ShopifyProductDtoBuilder builderResult4 = ShopifyProductDto.builder();
        ShopifyProductDto.ProductDto.ProductDtoBuilder idResult2 = ShopifyProductDto.ProductDto.builder()
            .bodyHtml("Not all who wander are lost")
            .handle("Handle")
            .id(1L);
        ShopifyProductDto.Image.ImageBuilder srcResult = ShopifyProductDto.Image.builder()
            .adminGraphqlApiId("42")
            .alt("Alt")
            .height(10.0d)
            .id(1L)
            .position(1)
            .productId(1L)
            .src("Src");
        ShopifyProductDto.Image image = srcResult.variantIds(new ArrayList<>()).width(10.0d).build();
        ShopifyProductDto.ProductDto.ProductDtoBuilder imageResult = idResult2.image(image);
        ShopifyProductDto.ProductDto.ProductDtoBuilder imagesResult = imageResult.images(new ArrayList<>());
        ShopifyProductDto.ProductDto.ProductDtoBuilder titleResult = imagesResult.options(new ArrayList<>())
            .productType("Product Type")
            .publishedAt("Published At")
            .publishedScope("Published Scope")
            .status("Status")
            .tags("Tags")
            .templateSuffix("Template Suffix")
            .title("Dr");
        ShopifyProductDto.ProductDto product = titleResult.variants(new ArrayList<>()).vendor("Vendor").build();
        ShopifyProductDto shopifyProductDto = builderResult4.product(product).build();

        // Act and Assert
        assertNull(ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto, shopifyProductDto));
    }


    @Test
    void testBuildInventoryItemDto3() {
        // Arrange
        ItemDto itemDto = mock(ItemDto.class);
        when(itemDto.getSkuNumber()).thenReturn("42");
        when(itemDto.getVendorItemDtos()).thenReturn(new ArrayList<>());

        // Act
        InventoryItemDto actualBuildInventoryItemDtoResult = ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto,
            new ShopifyProductDto());

        // Assert
        verify(itemDto).getSkuNumber();
        verify(itemDto).getVendorItemDtos();
        assertNull(actualBuildInventoryItemDtoResult);
    }


    @Test
    void testBuildInventoryItemDto4() {
        // Arrange
        ArrayList<VendorItemDto> vendorItemDtoList = new ArrayList<>();
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto buildResult = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        vendorItemDtoList.add(buildResult);
        ItemDto itemDto = mock(ItemDto.class);
        when(itemDto.getSkuNumber()).thenReturn("42");
        when(itemDto.getVendorItemDtos()).thenReturn(vendorItemDtoList);

        // Act
        InventoryItemDto actualBuildInventoryItemDtoResult = ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto,
            new ShopifyProductDto());

        // Assert
        verify(itemDto).getSkuNumber();
        verify(itemDto).getVendorItemDtos();
        assertNull(actualBuildInventoryItemDtoResult);
    }


    @Test
    void testBuildInventoryItemDto5() {
        // Arrange
        ArrayList<VendorItemDto> vendorItemDtoList = new ArrayList<>();
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto buildResult = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        vendorItemDtoList.add(buildResult);
        ItemDto itemDto = mock(ItemDto.class);
        when(itemDto.getSkuNumber()).thenReturn("42");
        when(itemDto.getVendorItemDtos()).thenReturn(vendorItemDtoList);
        ShopifyProductDto.ShopifyProductDtoBuilder builderResult = ShopifyProductDto.builder();
        ShopifyProductDto.ProductDto.ProductDtoBuilder idResult = ShopifyProductDto.ProductDto.builder()
            .bodyHtml("Not all who wander are lost")
            .handle("Handle")
            .id(1L);
        ShopifyProductDto.Image.ImageBuilder srcResult = ShopifyProductDto.Image.builder()
            .adminGraphqlApiId("42")
            .alt("Alt")
            .height(10.0d)
            .id(1L)
            .position(1)
            .productId(1L)
            .src("Src");
        ShopifyProductDto.Image image = srcResult.variantIds(new ArrayList<>()).width(10.0d).build();
        ShopifyProductDto.ProductDto.ProductDtoBuilder imageResult = idResult.image(image);
        ShopifyProductDto.ProductDto.ProductDtoBuilder imagesResult = imageResult.images(new ArrayList<>());
        ShopifyProductDto.ProductDto.ProductDtoBuilder titleResult = imagesResult.options(new ArrayList<>())
            .productType("Product Type")
            .publishedAt("Published At")
            .publishedScope("Published Scope")
            .status("Status")
            .tags("Tags")
            .templateSuffix("Template Suffix")
            .title("Dr");
        ShopifyProductDto.ProductDto product = titleResult.variants(new ArrayList<>()).vendor("Vendor").build();
        ShopifyProductDto shopifyProductDto = builderResult.product(product).build();

        // Act
        InventoryItemDto actualBuildInventoryItemDtoResult = ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto,
            shopifyProductDto);

        // Assert
        verify(itemDto).getSkuNumber();
        verify(itemDto).getVendorItemDtos();
        assertNull(actualBuildInventoryItemDtoResult);
    }


    @Test
    void testBuildInventoryItemDto6() {
        // Arrange
        ArrayList<VendorItemDto> vendorItemDtoList = new ArrayList<>();
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto buildResult = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        vendorItemDtoList.add(buildResult);
        ItemDto itemDto = mock(ItemDto.class);
        when(itemDto.getSkuNumber()).thenReturn("42");
        when(itemDto.getVendorItemDtos()).thenReturn(vendorItemDtoList);

        // Act
        InventoryItemDto actualBuildInventoryItemDtoResult = ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto, null);

        // Assert
        verify(itemDto).getSkuNumber();
        verify(itemDto).getVendorItemDtos();
        assertNull(actualBuildInventoryItemDtoResult);
    }


    @Test
    void testBuildInventoryItemDto7() {
        // Arrange
        ArrayList<VendorItemDto> vendorItemDtoList = new ArrayList<>();
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto buildResult = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        vendorItemDtoList.add(buildResult);
        ItemDto itemDto = mock(ItemDto.class);
        when(itemDto.getVendorItemDtos()).thenReturn(vendorItemDtoList);
        ShopifyProductDto shopifyProductDto = mock(ShopifyProductDto.class);
        when(shopifyProductDto.getProduct()).thenThrow(new IllegalStateException("foo"));

        // Act and Assert
        assertThrows(IllegalStateException.class,
            () -> ShopifyRequestDtoFactory.buildInventoryItemDto(itemDto, shopifyProductDto));
        verify(itemDto).getVendorItemDtos();
        verify(shopifyProductDto).getProduct();
    }

    @Test
    @DisplayName("Test buildShopifyProductDto(ItemDto, ShopifyGraphQLQueryResponseDto); then return Product Handle is 'Beverage'")
    void testBuildShopifyProductDto_thenReturnProductHandleIsBeverage() {

        CategoryDto categoryTree = new CategoryDto();
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ArrayList<ItemImageDto> itemImages = new ArrayList<>();
        ArrayList<ItemUPCDto> itemUPCs = new ArrayList<>();
        ArrayList<ItemTagDto> itemTags = new ArrayList<>();
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();

        ItemDto itemDto = new ItemDto(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, "Beverage", "Dr", "42",
            "The characteristics of someone or something", "Beverage", "Beverage", "java.text", 3, "Beverage", "Beverage",
            "Beverage", "Beverage", AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID,
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, 1, 1, AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, "Beverage",
            categoryTree, "Beverage", "Beverage", "Not all who wander are lost", "Beverage", "https://example.org/example",
            brand, primaryVendorItem, primaryVendorItem, "Beverage", "Beverage", "Beverage", "Beverage", "Beverage", null,
            null,
            null, 1, itemAttributes,
            itemImages, itemUPCs, itemTags, itemRegPrice, itemPromoPrices, new ArrayList<>(), null);
        itemDto.setDepartment(",");

        // Act and Assert
        ShopifyProductDto.ProductDto product = ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto, null).getProduct();
        assertEquals("Beverage", product.getHandle());
        assertEquals("Beverage", product.getProductType());
        assertEquals("beverage", product.getStatus());
        List<ShopifyProductDto.Image> images = product.getImages();
        assertEquals(1, images.size());
        ShopifyProductDto.Image getResult = images.get(0);
        assertEquals("https://example.org/example", getResult.getSrc());
        assertNull(getResult.getPosition());
        assertNull(getResult.getId());
        assertNull(getResult.getProductId());
        assertNull(getResult.getAdminGraphqlApiId());
        assertNull(getResult.getAlt());
        assertNull(getResult.getVariantIds());
        assertEquals(800.0d, getResult.getHeight().doubleValue());
        assertEquals(800.0d, getResult.getWidth().doubleValue());
    }


    @Test
    void testBuildShopifyProductDto_thenReturnProductStatusIsDraft() {
        // Arrange
        ItemDto itemDto = new ItemDto();
        itemDto.setDepartment(",");

        // Act and Assert
        ShopifyProductDto.ProductDto product = ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto, null).getProduct();
        assertEquals("draft", product.getStatus());
        assertNull(product.getBodyHtml());
        assertNull(product.getHandle());
        assertNull(product.getProductType());
        assertNull(product.getTitle());
        assertNull(product.getVendor());
    }

    @Test
    void testBuildShopifyProductDto_thenReturnProductTitleIsDr() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).build();
        itemDto.setDepartment(",");

        // Act and Assert
        ShopifyProductDto.ProductDto product = ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto, null).getProduct();
        assertEquals("Dr", product.getTitle());
        assertEquals("Handle", product.getHandle());
        assertEquals("Not all who wander are lost", product.getBodyHtml());
        assertEquals("Type", product.getProductType());
        assertEquals("Vendor Name", product.getVendor());
        assertEquals("availability status", product.getStatus());
        assertNull(product.getImages());
    }
}
