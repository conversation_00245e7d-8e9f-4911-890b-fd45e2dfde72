CREATE TABLE item_sales_trend
(
    id                UUID         NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id           VARCHAR(64)  NOT NULL,
    sku_number        VARCHAR(100) NOT NULL,
    sales_quantity    INTEGER,
    time_dim          DATE,
    time_grain        VARCHAR(10),
    created_at        TIMESTAMP    NOT NULL,
    created_by        <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARCHAR(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

CREATE INDEX idx_item_id ON item_sales_trend (item_id);
CREATE INDEX idx_sku_number ON item_sales_trend (sku_number);

COMMENT ON TABLE item_grade IS 'Sales Trend of Products';

COMMENT ON COLUMN item_sales_trend.id IS 'Primary key, unique identifier for the Item grade.';
COMMENT ON COLUMN item_sales_trend.item_id IS 'UNIQUE item_id of the  Item grade.';
COMMENT ON COLUMN item_sales_trend.sales_quantity IS 'Sales volume.';
COMMENT ON COLUMN item_sales_trend.time_dim IS 'Grade of the Item grade.';