CREATE TABLE company
(
    id         UUID         NOT NULL PRIMARY KEY,
    company_id BIGINT,
    name       <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP    NOT NULL,
    created_by VA<PERSON><PERSON>R(255) NOT NULL,
    updated_at TIMESTAMP,
    updated_by VARC<PERSON>R(255),
    deleted_at TIMESTAMP,
    deleted_by VARCHAR(255)
);
create index company_company_id_idx on company (company_id);
