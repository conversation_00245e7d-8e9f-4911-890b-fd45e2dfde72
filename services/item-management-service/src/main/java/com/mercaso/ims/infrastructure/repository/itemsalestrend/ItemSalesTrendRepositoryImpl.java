package com.mercaso.ims.infrastructure.repository.itemsalestrend;

import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollectionRepository;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrendRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.ItemCostCollectionJpaDao;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.dataobject.ItemCostCollectionDo;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.mapper.ItemCostCollectionDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

@Component
@RequiredArgsConstructor
public class ItemSalesTrendRepositoryImpl implements ItemSalesTrendRepository {

    private final ItemCostCollectionJpaDao itemCostCollectionJpaDao;

    private final ItemCostCollectionDoMapper itemCostCollectionDoMapper;


    @Override
    public ItemCostCollection save(ItemCostCollection domain) {
        ItemCostCollectionDo doObj = itemCostCollectionDoMapper.domainToDo(domain);
        doObj = itemCostCollectionJpaDao.save(doObj);
        return itemCostCollectionDoMapper.doToDomain(doObj);
    }

    @Override
    public ItemSalesTrend save(ItemSalesTrend domain) {
        return null;
    }

    @Override
    public ItemCostCollection findById(UUID id) {
        ItemCostCollectionDo doObj = itemCostCollectionJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        return itemCostCollectionDoMapper.doToDomain(doObj);
    }

    @Override
    public ItemSalesTrend update(ItemSalesTrend domain) {
        return null;
    }

    @Override
    public ItemCostCollection update(ItemCostCollection domain) {
        ItemCostCollectionDo doObj = itemCostCollectionJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(doObj)) {
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_COST_COLLECTION_NOT_FOUND);
        }
        ItemCostCollectionDo doObjTarget = itemCostCollectionDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(doObjTarget, doObj, ignoreProperties.toArray(new String[0]));
        ItemCostCollectionDo result = itemCostCollectionJpaDao.save(doObj);
        return itemCostCollectionDoMapper.doToDomain(result);
    }

    @Override
    public ItemCostCollection deleteById(UUID id) {
        ItemCostCollectionDo doObj = itemCostCollectionJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        doObj.setDeletedAt(Instant.now());
        doObj.setDeletedBy(SecurityUtil.getLoginUserId());
        doObj.setDeletedUserName(SecurityUtil.getUserName());
        doObj = itemCostCollectionJpaDao.save(doObj);
        return itemCostCollectionDoMapper.doToDomain(doObj);
    }

    @Override
    public List<ItemCostCollection> findByVendorId(UUID vendorId) {

        return Optional.ofNullable(itemCostCollectionJpaDao.findByVendorId(vendorId))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemCostCollectionDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<ItemSalesTrend> findByItemIdAndTimeGrain(UUID itemId, ItemSalesTrendTimeGrain timeGrain) {
        return List.of();
    }
}
