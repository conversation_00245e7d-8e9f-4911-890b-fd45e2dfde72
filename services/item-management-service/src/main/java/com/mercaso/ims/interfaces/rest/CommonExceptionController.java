package com.mercaso.ims.interfaces.rest;


import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class CommonExceptionController {

    private final MessageSource messageSource;

    @ExceptionHandler(value = ImsBusinessException.class)
    public ResponseEntity<Map<String, Object>> handleImsBusinessException(ServletRequest request, ImsBusinessException ex) {
        String message = messageSource.getMessage(ex.getCode(),
            (ex.getArgs() != null ? ex.getArgs() : new Object[]{}),
            Locale.getDefault());
        HttpServletRequest req = (HttpServletRequest) request;

        log.warn("IMS business exception, path= {}, code= {}, message= {} ",
            req.getRequestURI(),
            ex.getCode(),
            ex.getMessage());

        return ResponseEntity.unprocessableEntity()
            .body(Map.of("errorCode", ex.getCode(),
                "message", message)
            );
    }

}