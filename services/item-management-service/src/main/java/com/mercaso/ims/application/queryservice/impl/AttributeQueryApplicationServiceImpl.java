package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.application.dto.AttributeDto;
import com.mercaso.ims.application.mapper.attribute.AttributeDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.AttributeQueryApplicationService;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.attribute.service.AttributeService;

import java.util.List;

import com.mercaso.ims.infrastructure.util.ItemSizeUtil;
import com.mercaso.ims.infrastructure.util.VolumeUtil;
import com.mercaso.ims.infrastructure.util.WeightUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AttributeQueryApplicationServiceImpl implements AttributeQueryApplicationService {

    private final AttributeService attributeService;

    private final AttributeDtoApplicationMapper attributeDtoApplicationMapper;

    @Override
    public List<AttributeDto> queryOrFilterAttributes(String name) {

        List<Attribute> attributes = StringUtils.isBlank(name)
            ? attributeService.findAll()
            : attributeService.findByFuzzyName(name);

        return attributes.stream()
                .map(attribute -> {
                    AttributeDto dto = attributeDtoApplicationMapper.domainToDto(attribute);
                    // Set attribute units based on attribute ID
                    if (attribute.getId().equals(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)) {
                        dto.setAttributeUnits(VolumeUtil.getAllBottleUnits());
                    } else if (attribute.getId().equals(AttributeConstant.WEIGHT_ATTRIBUTE_ID)) {
                        dto.setAttributeUnits(WeightUtil.getAllWeightUnits());
                    } else if (attribute.getId().equals(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID)) {
                        dto.setAttributeUnits(ItemSizeUtil.getAllItemSizeUnits());
                    }
                    return dto;
                })
                .toList();
    }
}
