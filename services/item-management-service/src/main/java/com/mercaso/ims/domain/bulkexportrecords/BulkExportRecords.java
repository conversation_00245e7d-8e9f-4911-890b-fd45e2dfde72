package com.mercaso.ims.domain.bulkexportrecords;

import com.mercaso.ims.domain.BaseDomain;
import java.time.Instant;
import java.util.UUID;

import com.mercaso.ims.domain.bulkexportrecords.enums.SendStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class BulkExportRecords extends BaseDomain {
    private UUID id;

    private String fileName;

    private Instant searchTime;

    private Instant sendEmailTime;

    private String customFilter;

    private String exportBy;

    private SendStatus sendStatus;
}
