package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.infrastructure.excel.data.UpdateVendorItemsRequestData;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class UpdateVendorItemsRequestDataListener extends ItemAdjustmentRequestDataListener<UpdateVendorItemsRequestData> {

    public UpdateVendorItemsRequestDataListener(UUID itemAdjustmentRequestId,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
        ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
        ItemRepository itemRepository,
        VendorRepository vendorRepository,
        VendorItemRepository vendorItemRepository,
        CategoryApplicationService categoryApplicationService,
        FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(UpdateVendorItemsRequestData updateVendorItemsRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(updateVendorItemsRequestData.getSku())
            .vendor(updateVendorItemsRequestData.getVendor())
            .vendorItemNumber(updateVendorItemsRequestData.getVendorItemNumber())
            .vendorAisle(updateVendorItemsRequestData.getVendorAisle())
            .poVendorItemCost(updateVendorItemsRequestData.getPoVendorItemCost())
            .jitVendorItemCost(updateVendorItemsRequestData.getJitVendorItemCost())
            .vendorItemAvailability(updateVendorItemsRequestData.getVendorItemAvailability())
            .vendorItemType(updateVendorItemsRequestData.getVendorItemType())
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.UPDATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(UpdateVendorItemsRequestData updateVendorItemsRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        Item item = itemRepository.findBySku(updateVendorItemsRequestData.getSku());
        if (item == null) {
            failureReasons.add(ItemAdjustmentFailureReason.ITEM_NOT_FOUND);
        }
        if (!updateVendorItemsRequestData.haveVendorName()) {
            failureReasons.add(ItemAdjustmentFailureReason.VENDOR_NAME_IS_REQUIRED);
        }
        ItemAdjustmentFailureReason validateVendorResult = validateVendor(updateVendorItemsRequestData.getVendor());
        if (validateVendorResult != null) {
            failureReasons.add(validateVendorResult);
        }
        if (!updateVendorItemsRequestData.haveVendorItemInfo()) {
            failureReasons.add(ItemAdjustmentFailureReason.VENDOR_ITEM_INFO_IS_REQUIRED);
        }

        if (updateVendorItemsRequestData.haveVendorItemCostInfo() || isCreateVendorItem(updateVendorItemsRequestData.getVendor(),
            item)) {
            if (!updateVendorItemsRequestData.haveVendorItemType()) {
                failureReasons.add(ItemAdjustmentFailureReason.VENDOR_ITEM_TYPE_REQUIRED);
            }
        }

        if (updateVendorItemsRequestData.getVendorItemType() != null) {
            switch (updateVendorItemsRequestData.getVendorItemType()) {
                case "DIRECT":
                    if (updateVendorItemsRequestData.getPoVendorItemCost() == null) {
                        failureReasons.add(ItemAdjustmentFailureReason.DIRECT_VENDOR_ITEM_COST_IS_REQUIRED);
                    }
                    break;
                case "JIT":
                    if (updateVendorItemsRequestData.getJitVendorItemCost() == null) {
                        failureReasons.add(ItemAdjustmentFailureReason.JIT_VENDOR_ITEM_COST_IS_REQUIRED);
                    }
                    break;
                case "DIRECT&JIT":
                    if (updateVendorItemsRequestData.getPoVendorItemCost() == null
                        || updateVendorItemsRequestData.getJitVendorItemCost() == null) {
                        failureReasons.add(ItemAdjustmentFailureReason.DIRECT_JIT_VENDOR_ITEM_COST_IS_REQUIRED);
                    }
                    break;
                default:
                    failureReasons.add(ItemAdjustmentFailureReason.INVALID_VENDOR_ITEM_TYPE);
                    break;
            }
        }

        ItemAdjustmentFailureReason validateBackupVendor = validateBackupVendor(updateVendorItemsRequestData.getVendorItemType(),
            updateVendorItemsRequestData.getVendor());
        if (validateBackupVendor != null) {
            failureReasons.add(validateBackupVendor);
        }
        return failureReasons;

    }


    private ItemAdjustmentFailureReason validateBackupVendor(String vendorItemType,
        String vendorName) {
        if (VendorItemType.JIT.getTypeName().equals(vendorItemType) || VendorItemType.DIRECT_JIT.getTypeName()
            .equals(vendorItemType)) {
            Vendor vendor = vendorRepository.findByVendorName(vendorName);
            if (vendor == null) {
                return null;
            }
            if (vendor.getExternalPicking() == null || !vendor.getExternalPicking()) {
                return ItemAdjustmentFailureReason.INVALID_JIT_VENDOR;
            }
        }
        return null;
    }

    private boolean isCreateVendorItem(String vendorName, Item item) {
        if (StringUtils.isNotBlank(vendorName)) {
            Vendor vendor = vendorRepository.findByVendorName(vendorName);
            if (vendor == null) {
                return false;
            }
            VendorItem vendorItem = vendorItemRepository.findByVendorIDAndItemId(vendor.getId(), item.getId());
            if (null == vendorItem) {
                return true;
            }
        }
        return false;
    }


}
