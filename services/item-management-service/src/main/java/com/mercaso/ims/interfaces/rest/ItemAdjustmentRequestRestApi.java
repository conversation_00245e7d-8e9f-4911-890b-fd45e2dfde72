package com.mercaso.ims.interfaces.rest;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/v1/item-adjustment-request")
@RequiredArgsConstructor
@Slf4j
public class ItemAdjustmentRequestRestApi {

    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    private final ResourceLoader resourceLoader;


    @GetMapping("/{id}/detail/download")
    @PreAuthorize("hasAuthority('ims:read:item-adjustment-requests')")
    public DocumentResponse downloadItemAdjustmentDetail(@PathVariable("id") UUID itemAdjustmentRequestId) throws IOException {
        return itemAdjustmentRequestApplicationService.downloadItemAdjustmentDetail(itemAdjustmentRequestId);
    }

    @PostMapping("/upload")
    @PreAuthorize("hasAuthority('ims:write:item-adjustment-requests')")
    public ItemAdjustmentRequestDto uploadItemAdjustmentRequest(@RequestParam("requestFile") MultipartFile file,
        @RequestParam("type") String type) {
        return itemAdjustmentRequestApplicationService.uploadItemAdjustmentRequest(file, ItemAdjustmentRequestType.valueOf(type));
    }

    @GetMapping("/{id}/file")
    @PreAuthorize("hasAuthority('ims:read:item-adjustment-requests')")
    public DocumentResponse getItemAdjustmentRequestFile(@PathVariable UUID id) {
        return itemAdjustmentRequestApplicationService.getItemAdjustmentRequestFile(id);
    }

    @GetMapping("/template/download")
    public void downloadItemAdjustmentTemplate(HttpServletResponse httpResponse) {
        try {
            String locationName = "classpath:excel/Item Adjustment Request Template V1.xlsx";
            Resource resource = resourceLoader.getResource(locationName);
            String fileName = URLEncoder.encode("Item Adjustment Request Template.xlsx", "UTF-8").replaceAll("\\+", "%20");
            httpResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpResponse.setCharacterEncoding("utf-8");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            httpResponse.getOutputStream().write(resource.getInputStream().readAllBytes());
        } catch (IOException e) {
            log.error("Failed to download item adjustment request template", e);
        }
    }


}
