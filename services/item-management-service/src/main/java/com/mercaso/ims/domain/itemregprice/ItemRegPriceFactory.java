package com.mercaso.ims.domain.itemregprice;

import com.mercaso.ims.application.command.CreateItemRegPriceCommand;
import com.mercaso.ims.application.dto.PriceDto;
import com.mercaso.ims.domain.itemregprice.enums.ItemRegPriceStatus;

public class ItemRegPriceFactory {

    private ItemRegPriceFactory() {
    }

    public static ItemRegPrice create(CreateItemRegPriceCommand command) {

        ItemRegPrice itemRegPrice = ItemRegPrice.builder()
            .itemId(command.getItemId())
            .itemRegPriceStatus(ItemRegPriceStatus.ACTIVE)
            .build();
        return itemRegPrice.updatePrice(new PriceDto(command.getRegPrice(),
            command.getPackageSize(),
            command.getCrvFlag(),
            command.getBottleSize(),
            command.getBottleUnit()));
    }
}
