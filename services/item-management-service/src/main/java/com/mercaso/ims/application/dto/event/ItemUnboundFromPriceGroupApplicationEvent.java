package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemUnboundFromPriceGroupPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemUnboundFromPriceGroupApplicationEvent extends BaseApplicationEvent<ItemUnboundFromPriceGroupPayloadDto> {

    public ItemUnboundFromPriceGroupApplicationEvent(Object source, ItemUnboundFromPriceGroupPayloadDto payload) {
        super(source, payload);
    }
}