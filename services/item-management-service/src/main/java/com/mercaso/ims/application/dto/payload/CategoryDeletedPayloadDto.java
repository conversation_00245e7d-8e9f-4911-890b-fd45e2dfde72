package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CategoryDeletedPayloadDto extends BusinessEventPayloadDto<CategoryDeletedDto> {

    private UUID categoryId;

    @Builder
    public CategoryDeletedPayloadDto(CategoryDeletedDto data, UUID categoryId) {
        super(data);
        this.categoryId = categoryId;
    }
}