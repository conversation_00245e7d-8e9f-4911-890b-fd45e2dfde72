package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyDeleteMetaFieldsResponseDto {

    @JsonProperty("data")
    private DataDto data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataDto {

        @JsonProperty("metafieldsDelete")
        private MetafieldsDeleteDto metafieldsDelete;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetafieldsDeleteDto {

        @JsonProperty("deletedMetafields")
        private List<DeletedMetafieldDto> deletedMetafields;

        @JsonProperty("userErrors")
        private List<UserErrorDto> userErrors;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeletedMetafieldDto {

        @JsonProperty("key")
        private String key;

        @JsonProperty("namespace")
        private String namespace;

        @JsonProperty("ownerId")
        private String ownerId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserErrorDto {

        @JsonProperty("field")
        private List<String> field;

        @JsonProperty("message")
        private String message;
    }
}
