package com.mercaso.ims.infrastructure.process.matcher;

import com.google.common.collect.Lists;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class DirectVendorItemMatcher implements VendorItemMatcher {

    private final VendorItemService vendorItemService;
    private final VendorService vendorService;
    private final ItemService itemService;


    @Override
    public List<VendorItemMappingDto> matchItem(ItemCostCollectionItemParsingResultDto invoiceItem, UUID vendorId) {
        List<VendorItemMappingDto> vendorItemMappings = Lists.newArrayList();

        Vendor vendor = vendorService.findById(vendorId);

        Item item = itemService.findBySku(invoiceItem.getVendorSkuNumber());
        VendorItem vendorItem = null;
        VendorItemMappingDto vendorItemMappingDto = new VendorItemMappingDto();
        if (vendor != null) {
            vendorItemMappingDto.setVendorId(vendor.getId());
            vendorItemMappingDto.setVendorName(vendor.getVendorName());
        } else {
            log.warn("Vendor not found as matchItem for id : {}", vendorId);
        }

        if (item != null) {
            vendorItem = vendorItemService.findByVendorIDAndItemId(vendor.getId(), item.getId());
            vendorItemMappingDto.setCostType(getSupportedCostType());
        } else {
            log.warn("Item not found as matchItem for sku : {}", invoiceItem.getVendorSkuNumber());
        }

        if (vendorItem == null) {
            log.warn("Vendor item not found as match DirectVendorItem for sku : {}", invoiceItem.getVendorSkuNumber());
            vendorItemMappingDto.setMatchedType(MatchedType.MISS_MATCHED);
            vendorItemMappingDto.setCostType(getSupportedCostType());
        } else {
            vendorItemMappingDto.setPreviousCost(vendorItem.getCost());
            vendorItemMappingDto.setMatchedType(MatchedType.AUTO_MATCHED_AND_UPDATED);
            vendorItemMappingDto.setItemId(vendorItem.getItemId());
            vendorItemMappingDto.setVendorItemId(vendorItem.getId());
            vendorItemMappings.add(vendorItemMappingDto);
        }

        vendorItemMappings.add(vendorItemMappingDto);

        return vendorItemMappings;
    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public String getSupportedCostType() {
        return CostType.DIRECT_COST.getCostTypeName();
    }

}
