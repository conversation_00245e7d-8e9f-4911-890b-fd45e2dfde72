package com.mercaso.ims.application.mapper.itemregprice;

import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ItemRegPriceDtoApplicationMapper extends BaseDtoApplicationMapper<ItemRegPrice, ItemRegPriceDto> {

    @Override
    @Mapping(source = "id", target = "itemRegPriceId")
    ItemRegPriceDto domainToDto(ItemRegPrice domain);


    @Mapping(source = "itemRegPriceId", target = "id")
    ItemRegPrice dtoToDomain(ItemRegPriceDto dto);

}
