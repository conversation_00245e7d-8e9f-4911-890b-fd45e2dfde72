package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.mapper.vendoritem.VendorItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.VendorItemQueryApplicationService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class VendorItemQueryApplicationServiceImpl implements VendorItemQueryApplicationService {

    private final ItemService itemService;

    private final VendorService vendorService;

    private final VendorItemService vendorItemService;

    private final VendorItemDtoApplicationMapper vendorItemMapper;

    @Override
    public VendorItemDto findById(UUID id) {
        VendorItem vendorItem = vendorItemService.findById(id);
        Item item = itemService.findById(vendorItem.getItemId());
        List<Vendor> vendors = vendorService.findAll();

        Map<UUID, Vendor> vendorMap = vendors.stream().collect(Collectors.toMap(Vendor::getId, Function.identity()));

        return createVendorItemDto(vendorItem, vendorMap, item.getSkuNumber());
    }

    @Override
    public List<VendorItemDto> findByItemId(UUID itemId) {
        Item item = itemService.findById(itemId);
        List<Vendor> vendors = vendorService.findAll();

        Map<UUID, Vendor> vendorMap = vendors.stream().collect(Collectors.toMap(Vendor::getId, Function.identity()));
        List<VendorItem> vendorItems = vendorItemService.findByItemID(itemId);
        if (vendorItems.isEmpty()) {
            log.warn("Not found the vendor item fir item :{}", itemId);
            return Collections.emptyList();
        }

        return vendorItems.stream()
            .map(vendorItem -> createVendorItemDto(vendorItem, vendorMap, item.getSkuNumber()))
            .toList();
    }

    @Override
    public List<VendorItemDto> findByVendorIdAndItemIds(UUID vendorId, List<UUID> itemIds) {
        List<Vendor> vendors = vendorService.findAll();

        Map<UUID, Vendor> vendorMap = vendors.stream().collect(Collectors.toMap(Vendor::getId, Function.identity()));

        return vendorItemService.findByVendorIdAndItemIdIn(vendorId, itemIds).stream()
            .map(vendorItem -> createVendorItemDto(vendorItem, vendorMap, null))
            .toList();
    }


    private VendorItemDto createVendorItemDto(VendorItem vendorItem, Map<UUID, Vendor> vendorMap, String skuNumber) {
        VendorItemDto vendorItemDto = vendorItemMapper.domainToDto(vendorItem);
        vendorItemDto.setItemSkuNumber(skuNumber);

        Optional.ofNullable(vendorMap.get(vendorItem.getVendorId()))
            .ifPresentOrElse(
                vendor -> {
                    vendorItemDto.setVendorName(vendor.getVendorName());
                    vendorItemDto.setVendorFinaleId(vendor.getFinaleId());
                },
                () -> log.error("Not found the vendor for sku :{}", skuNumber)
            );
        return vendorItemDto;
    }
}
