package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.CreateVendorPoAnalyzeExpenseRecordPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CreateAnalyzeExpenseRecordApplicationEvent extends BaseApplicationEvent<CreateVendorPoAnalyzeExpenseRecordPayloadDto> {

    public CreateAnalyzeExpenseRecordApplicationEvent(Object source, CreateVendorPoAnalyzeExpenseRecordPayloadDto payload) {
        super(source, payload);
    }
}
