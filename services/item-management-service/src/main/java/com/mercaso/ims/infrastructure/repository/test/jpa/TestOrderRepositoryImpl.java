package com.mercaso.ims.infrastructure.repository.test.jpa;

import com.mercaso.ims.application.query.TestOrderQuery;
import com.mercaso.ims.domain.testorder.TestOrder;
import com.mercaso.ims.domain.testorder.TestOrderRepository;
import com.mercaso.ims.infrastructure.repository.test.jpa.dataobject.TestOrderDo;
import com.mercaso.ims.infrastructure.repository.test.jpa.mapper.TestOrderDoMapper;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TestOrderRepositoryImpl implements TestOrderRepository {

    private final TestOrderDoMapper testOrderDoMapper;

    private final TestOrderJpaDao testOrderJpaDao;


    @Override
    public List<TestOrder> query(TestOrderQuery testOrderQuery) {
        return List.of();
    }

    @Override
    public TestOrder save(TestOrder domain) {
        TestOrderDo testOrderDo = testOrderDoMapper.domainToDo(domain);
        testOrderDo = testOrderJpaDao.save(testOrderDo);
        return testOrderDoMapper.doToDomain(testOrderDo);
    }

    @Override
    public TestOrder findById(UUID id) {
        TestOrderDo testOrderDo = testOrderJpaDao.findById(id).orElse(null);
        return testOrderDoMapper.doToDomain(testOrderDo);
    }

    @Override
    public TestOrder update(TestOrder domain) {
        return null;
    }

    @Override
    public TestOrder deleteById(UUID id) {
        testOrderJpaDao.deleteById(id);
        return null;
    }
}
