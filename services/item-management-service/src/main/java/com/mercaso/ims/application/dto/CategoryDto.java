package com.mercaso.ims.application.dto;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategoryDto extends BaseDto {

    private UUID categoryId;

    private String categoryName;

    private String ancestorName;

    private UUID ancestorCategoryId;

    private Integer depth;

    private Integer sortOrder;

}
