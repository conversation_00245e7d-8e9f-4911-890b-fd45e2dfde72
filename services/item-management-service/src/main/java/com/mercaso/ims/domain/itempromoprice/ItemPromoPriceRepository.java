package com.mercaso.ims.domain.itempromoprice;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

public interface ItemPromoPriceRepository extends BaseDomainRepository<ItemPromoPrice, UUID> {

    List<ItemPromoPrice> findByItemId(UUID itemId);

    List<ItemPromoPrice> findByItemIds(List<UUID> itemIds);

    List<ItemPromoPrice> findByPromoBeginTimeBetween(Instant start, Instant end);

    List<ItemPromoPrice> findByPromoEndTimeBetween(Instant start, Instant end);
}
