package com.mercaso.ims.infrastructure.process.parser;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.data.VernonItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.infrastructure.util.CsvUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class VernonItemCostCollectionParser implements ItemCostCollectionParser {

    private final ItemCostCollectionService itemCostCollectionService;
    private final VernonAdaptor vernonAdaptor;
    private final DocumentApplicationService documentApplicationService;

    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        switch (itemCostCollection.getSource()) {
            case VERNON_ORDER:
                return parseVernonOrder(itemCostCollection.getVendorCollectionNumber());
            case VERNON_DAILY_ITEM_LISTS:
                return parseVernonDailyItemList(itemCostCollection.getFileName());
            default:
                return new ArrayList<>();
        }
    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return VendorConstant.VERNON_SALES.equals(vendorName) && !sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return false;
    }

    private List<ItemCostCollectionItemParsingResultDto> parseVernonOrder(String collectionNumber) {
        String htmlContent = vernonAdaptor.getOrderDetailPage(collectionNumber);
        return parseInvoiceItem(htmlContent);
    }

    private ItemCostCollectionItemParsingResultDto convertToItemCostCollectionItemParsingResultDto(VernonItemDailyUpdatedData data) {
        return ItemCostCollectionItemParsingResultDto.builder()
            .vendorSkuNumber(data.getItemNumber())
            .vendorItemName(data.getDescription1())
            .cost(data.getPrice1())
            .packSize(data.getQtPack())
            .upc(data.getUpcNo())
            .build();
    }

    private List<ItemCostCollectionItemParsingResultDto> parseInvoiceItem(String htmlContent) {
        Document document = Jsoup.parse(htmlContent);
        Element table = document.selectFirst("table.CsSaStatusDetail");

        if (table == null) {
            log.warn("Table not found in document");
            return Collections.emptyList();
        }

        return table.select("tr").stream()
            .skip(2)
            .map(this::convertRowToDto)
            .filter(Objects::nonNull)
            .toList();
    }

    private ItemCostCollectionItemParsingResultDto convertRowToDto(Element row) {
        Elements cells = row.select("td");
        if (cells.size() < 9) {
            return null;
        }

        try {
            return ItemCostCollectionItemParsingResultDto.builder()
                .vendorSkuNumber(cells.get(1).text().trim())
                .vendorItemName(cells.get(2).text().trim())
                .packSize(Integer.parseInt(cells.get(6).text().trim()))
                .cost(new BigDecimal(cells.get(7).text().trim()))
                .build();
        } catch (NumberFormatException e) {
            log.error("Error parsing invoice row: {}", row.text(), e);
            return null;
        }
    }

    private List<ItemCostCollectionItemParsingResultDto> parseVernonDailyItemList(String fileName) {
        byte[] document = documentApplicationService.downloadDocument(fileName);
        List<List<String>> csvData = CsvUtils.readCsv(document);

        return csvData.stream()
            .skip(1)
            .map(this::convertToVernonItemDailyUpdatedData)
            .filter(Objects::nonNull)
            .map(this::convertToItemCostCollectionItemParsingResultDto)
            .toList();
    }


    private VernonItemDailyUpdatedData convertToVernonItemDailyUpdatedData(List<String> data) {
        if (data == null || data.size() < 14 || StringUtils.isBlank(data.get(0))) {
            log.warn("Invalid CSV row: {}", data);
            return null;
        }

        try {
            return VernonItemDailyUpdatedData.builder()
                .itemNumber(data.get(0))
                .description1(data.get(1))
                .picFile1(data.get(2))
                .qtCase(Integer.parseInt(data.get(3)))
                .qtPack(Integer.parseInt(data.get(4)))
                .qtPall(Integer.parseInt(data.get(5)))
                .price1(new BigDecimal(data.get(6)))
                .price5(new BigDecimal(data.get(7)))
                .upcNo(data.get(8))
                .wtCase(data.get(9))
                .voCase(data.get(10))
                .dept(data.get(11))
                .categ(data.get(12))
                .origin(data.get(13))
                .build();
        } catch (NumberFormatException e) {
            log.error("Error parsing CSV row: {}", data, e);
            return null;
        }
    }

}