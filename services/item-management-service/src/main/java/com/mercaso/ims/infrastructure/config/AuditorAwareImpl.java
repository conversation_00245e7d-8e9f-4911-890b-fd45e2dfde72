package com.mercaso.ims.infrastructure.config;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.AuditorAware;

import java.util.Optional;

@Profile("!integration")
@Configuration
public class AuditorAwareImpl implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {
        if (SecurityContextUtil.getLoginUserId() == null) {
            return Optional.of("1");

        }
        return SecurityContextUtil.getLoginUserId().describeConstable();
    }

}
