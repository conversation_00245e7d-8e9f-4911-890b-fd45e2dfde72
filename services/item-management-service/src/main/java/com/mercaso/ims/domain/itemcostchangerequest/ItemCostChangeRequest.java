package com.mercaso.ims.domain.itemcostchangerequest;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemCostChangeRequest extends BaseDomain {

    private UUID id;

    private UUID itemCostCollectionId;

    private UUID vendorId;

    private UUID itemId;

    private String skuNumber;
    private String vendorSkuNumber;

    private String vendorItemName;

    private BigDecimal previousCost;

    private BigDecimal targetCost;

    private BigDecimal tax;

    private BigDecimal crv;

    private MatchedType matchType;

    private ItemCostChangeRequestStatus status;

    private String vendorItemUpc;

    private Boolean availability;

    private String costType;

    private String aisle;
}
