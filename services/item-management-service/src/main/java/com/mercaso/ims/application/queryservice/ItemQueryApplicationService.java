package com.mercaso.ims.application.queryservice;

import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPackingInfoDto;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ItemQueryApplicationService {

    ItemDto findById(UUID id);

    ItemDto findBySku(String sku);
    
    Page<ItemDto> findPagedItems(Pageable pageable);

    List<ItemCategoryDto> findItemCategoryByIdIn(List<UUID> ids);

    List<ItemCategoryDto> findItemCategoryBySkuIn(List<String> skus);

    List<ItemPackingInfoDto> findItemsPackingInfoByIdIn(List<UUID> ids);

    Page<ItemDto> findPagedItemsByUpdateAt(Pageable pageable, Instant startTime, Instant endTime);

    List<ItemDto> findByUpcNumber(String upcNumber);

    List<ItemDto> findByIdIn(List<UUID> ids);

    ItemCategoryDto findItemCategoryByIdAndVersionNumber(UUID itemId, Integer versionNumber);

    List<ItemDto> findByCategoryId(UUID categoryId);
}
