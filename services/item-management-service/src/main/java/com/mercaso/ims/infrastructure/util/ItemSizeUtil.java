package com.mercaso.ims.infrastructure.util;

import java.util.Arrays;
import java.util.List;

public class ItemSizeUtil {

    private static final String[] validItemSizeUnitMeasure = {
            "cm",
            "ct",
            "cu ft",
            "ft",
            "g",
            "gal",
            "in",
            "kg",
            "l",
            "lb",
            "m",
            "mg",
            "ml",
            "mm",
            "oz",
            "pt",
            "qt",
            "sq ft",
            "yd",
    };

    private ItemSizeUtil() {
    }



    public static boolean isValidItemSizeUnit(String unit) {
        for (String validUnit : validItemSizeUnitMeasure) {
            if (validUnit.equalsIgnoreCase(unit)) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getAllItemSizeUnits () {
        return Arrays.asList(validItemSizeUnitMeasure);
    }


}
