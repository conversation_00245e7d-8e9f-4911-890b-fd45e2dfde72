package com.mercaso.ims.domain.item;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.ValueObject;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.EqualsBuilder;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ItemTag extends BaseDomain implements ValueObject<ItemTag> {

    private UUID id;

    private UUID itemId;

    private String tagName;


    @Override
    public boolean sameValueAs(ItemTag other) {
        return other != null && new EqualsBuilder()
            .append(itemId, other.itemId)
            .append(tagName, other.tagName)
            .isEquals();
    }
}
