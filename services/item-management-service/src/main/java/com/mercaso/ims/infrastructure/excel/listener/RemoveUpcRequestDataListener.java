package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.RemoveUpcRequestData;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RemoveUpcRequestDataListener extends ItemAdjustmentRequestDataListener<RemoveUpcRequestData> {

    public RemoveUpcRequestDataListener(UUID itemAdjustmentRequestId,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
        ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
        ItemRepository itemRepository,
        VendorRepository vendorRepository,
        VendorItemRepository vendorItemRepository,
        CategoryApplicationService categoryApplicationService,
        FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
    }


    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(RemoveUpcRequestData data) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(cleanInput(data.getSku()))
            .build();

    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(RemoveUpcRequestData data) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        Item item = itemRepository.findBySku(cleanInput(data.getSku()));
        if (item == null) {
            failureReasons.add(ItemAdjustmentFailureReason.ITEM_NOT_FOUND);
        }
        return failureReasons;
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.CLEAN_UPC;
    }

}
