package com.mercaso.ims.domain.itemadjustmentrequest;

import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestTransitionEvents;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.infrastructure.statemachine.BaseStateMachine;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequest extends
    BaseStateMachine<ItemAdjustmentRequest, ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents> {

    private final UUID id;

    private String requestFile;

    private ItemAdjustmentRequestType type;
    private String failureReason;

    private Integer createdRowCount;
    private Integer modifiedRowCount;
    private Integer deletedRowCount;
    private Integer createFailedRowCount;
    private Integer createSuccessRowCount;
    private Integer modifyFailedRowCount;
    private Integer modifySuccessRowCount;
    private Integer deleteFailedRowCount;
    private Integer deleteSuccessRowCount;
    private String detailFile;

    public void complete(Integer createSuccessRowCount, Integer modifySuccessRowCount, Integer deleteSuccessRowCount,
        Integer createFailedRowCount, Integer modifyFailedRowCount, Integer deleteFailedRowCount) {
        this.createSuccessRowCount = createSuccessRowCount;
        this.modifySuccessRowCount = modifySuccessRowCount;
        this.deleteSuccessRowCount = deleteSuccessRowCount;
        this.createFailedRowCount = createFailedRowCount;
        this.modifyFailedRowCount = modifyFailedRowCount;
        this.deleteFailedRowCount = deleteFailedRowCount;
        processEvent(ItemAdjustmentRequestTransitionEvents.COMPLETED);
    }

    public void processed() {
        processEvent(ItemAdjustmentRequestTransitionEvents.FILE_PROCESSED);
    }

    public void processedFailure(String failureReason) {
        processEvent(ItemAdjustmentRequestTransitionEvents.PROCESSED_FAILURE);
        this.failureReason = failureReason;
    }

    public ItemAdjustmentRequest addCountOfParsed(Integer createdRowCount, Integer modifiedRowCount, Integer deletedRowCount) {
        this.createdRowCount = getCreatedRowCount() + createdRowCount;
        this.modifiedRowCount = getModifiedRowCount() + modifiedRowCount;
        this.deletedRowCount = getDeletedRowCount() + deletedRowCount;
        return this;
    }

    public Integer getCreatedRowCount() {
        return createdRowCount == null ? 0 : createdRowCount;
    }

    public Integer getModifiedRowCount() {
        return modifiedRowCount == null ? 0 : modifiedRowCount;
    }

    public Integer getDeletedRowCount() {
        return deletedRowCount == null ? 0 : deletedRowCount;
    }

    public Integer getCreateFailedRowCount() {
        return createFailedRowCount == null ? 0 : createFailedRowCount;
    }

    public Integer getCreateSuccessRowCount() {
        return createSuccessRowCount == null ? 0 : createSuccessRowCount;
    }

    public Integer getModifyFailedRowCount() {
        return modifyFailedRowCount == null ? 0 : modifyFailedRowCount;
    }

    public Integer getModifySuccessRowCount() {
        return modifySuccessRowCount == null ? 0 : modifySuccessRowCount;
    }

    public Integer getDeleteFailedRowCount() {
        return deleteFailedRowCount == null ? 0 : deleteFailedRowCount;
    }

    public Integer getDeleteSuccessRowCount() {
        return deleteSuccessRowCount == null ? 0 : deleteSuccessRowCount;
    }


}
