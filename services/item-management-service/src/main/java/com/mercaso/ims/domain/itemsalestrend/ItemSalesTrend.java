package com.mercaso.ims.domain.itemsalestrend;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import lombok.*;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.UUID;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemSalesTrend extends BaseDomain {


    private UUID id;

    private String itemId;

    private String skuNumber;

    private Integer salesQuantity;

    private Date timeDim;

    private ItemSalesTrendTimeGrain timeGrain;

}
