package com.mercaso.ims.infrastructure.event.applicationevent.listener;


import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_EXCEPTION_RECORD;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.VendorItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class VendorItemApplicationEventListener {

    private final ItemApplicationService itemApplicationService;
    private final ItemService itemService;
    private final FeatureFlagsManager featureFlagsManager;
    private final VendorItemSpecification vendorItemSpecification;
    private final ExceptionRecordApplicationService exceptionRecordApplicationService;
    private final FinaleApplicationService finaleApplicationService;
    private final ItemQueryApplicationService itemQueryApplicationService;
    private final ItemPriceApplicationService itemPriceApplicationService;


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleVendorItemAmendApplicationEvent(VendorItemAmendApplicationEvent vendorItemAmendApplicationEvent) {
        VendorItemAmendPayloadDto vendorItemAmendPayloadDto = vendorItemAmendApplicationEvent.getPayload();
        log.info("handleVendorItemAmendApplicationEvent for vendorItemAmendPayloadDto={}", vendorItemAmendPayloadDto);

        if (vendorItemAmendPayloadDto.isCostChanged()) {
            checkCostException(vendorItemAmendPayloadDto, vendorItemAmendApplicationEvent.getBusinessEventId());
        }
        log.info("Refresh JIT Primary Vendor for itemId={}", vendorItemAmendPayloadDto.getData());
        itemApplicationService.refreshPrimaryBackupVendor(vendorItemAmendPayloadDto.getCurrent().getItemId());
        if (!vendorItemAmendPayloadDto.isVendorItemTypeChanged()) {
            syncVendorItemToFinale(vendorItemAmendPayloadDto.getCurrent());
        }
        updateExoticBlvdItemPrice(vendorItemAmendPayloadDto);
    }


    private void checkCostException(VendorItemAmendPayloadDto vendorItemAmendPayloadDto, UUID eventId) {
        if (!featureFlagsManager.isFeatureOn(IMS_EXCEPTION_RECORD)) {
            return;
        }
        String exception = vendorItemSpecification.getExceptionMessage(vendorItemAmendPayloadDto.getData()
                .getCurrent()
                .getItemId(),
            vendorItemAmendPayloadDto.getData().getPrevious().getCost(),
            vendorItemAmendPayloadDto.getData().getCurrent().getCost());
        String backupException = vendorItemSpecification.getExceptionMessage(vendorItemAmendPayloadDto.getData()
                .getCurrent()
                .getItemId(),
            vendorItemAmendPayloadDto.getData().getPrevious().getBackupCost(),
            vendorItemAmendPayloadDto.getData().getCurrent().getBackupCost());
        if (StringUtils.isNotBlank(exception)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(vendorItemAmendPayloadDto.getCurrent().getVendorItemId())
                .entityType(EntityType.VENDOR_ITEM)
                .exceptionType(ExceptionRecordType.PO_COST_EXCEPTION)
                .description(exception)
                .build());
        }

        if (StringUtils.isNotBlank(backupException)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(vendorItemAmendPayloadDto.getCurrent().getVendorItemId())
                .entityType(EntityType.VENDOR_ITEM)
                .exceptionType(ExceptionRecordType.JIT_COST_EXCEPTION)
                .description(exception)
                .build());
        }

    }

    private void updateExoticBlvdItemPrice(VendorItemAmendPayloadDto vendorItemAmendPayloadDto) {
        Item item = fetchItemOrThrow(vendorItemAmendPayloadDto.getCurrent().getItemId());
        ItemDto itemDto = itemQueryApplicationService.findById(item.getId());
        if (itemDto.getVendorItemDtos() != null && itemDto.getVendorItemDtos().size() == 1
            && itemDto.getVendorItemDtos().getFirst().getVendorName().equals(VendorConstant.EXOTIC_BLVD)) {
            BigDecimal price = vendorItemAmendPayloadDto.getCurrent()
                .getBackupCost()
                .divide(BigDecimal.valueOf(0.875), 2, RoundingMode.HALF_UP).subtract(BigDecimal.valueOf(0.01));
            log.info("Update ExoticBlvd item price for itemId: {}, price: {}", vendorItemAmendPayloadDto.getCurrent().getItemId(),
                price);
            UpdateItemRegPriceCommand command = new UpdateItemRegPriceCommand(vendorItemAmendPayloadDto.getCurrent().getItemId(),
                price);
            itemPriceApplicationService.updateRegPrice(command);
        }
    }

    private void syncVendorItemToFinale(VendorItemDto vendorItemDto) {
        if (isPrimaryVendorItem(vendorItemDto)) {
            ItemDto itemDto = itemQueryApplicationService.findById(vendorItemDto.getItemId());
            finaleApplicationService.syncItemToFinale(itemDto);
        }
    }

    private boolean isPrimaryVendorItem(VendorItemDto vendorItemDto) {
        Item item = fetchItemOrThrow(vendorItemDto.getItemId());
        return vendorItemDto.getVendorId().equals(item.getPrimaryVendorId()) ||
            vendorItemDto.getVendorId().equals(item.getBackupVendorId());

    }

    private Item fetchItemOrThrow(UUID id) {
        return Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND));
    }

}
