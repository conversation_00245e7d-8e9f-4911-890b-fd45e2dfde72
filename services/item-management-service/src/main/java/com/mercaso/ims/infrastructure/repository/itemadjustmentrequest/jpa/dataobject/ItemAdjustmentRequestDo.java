package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.dataobject;

import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_adjustment_request")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_adjustment_request set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestDo extends BaseDo {

    @Column(name = "request_file")
    private String requestFile;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ItemAdjustmentRequestType type;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ItemAdjustmentRequestStatus status;
    @Column(name = "created_row_count")
    private Integer createdRowCount;
    @Column(name = "modified_row_count")
    private Integer modifiedRowCount;
    @Column(name = "deleted_row_count")
    private Integer deletedRowCount;

    @Column(name = "create_failed_row_count")
    private Integer createFailedRowCount;
    @Column(name = "create_success_row_count")
    private Integer createSuccessRowCount;
    @Column(name = "modify_failed_row_count")
    private Integer modifyFailedRowCount;
    @Column(name = "modify_success_row_count")
    private Integer modifySuccessRowCount;
    @Column(name = "delete_failed_row_count")
    private Integer deleteFailedRowCount;
    @Column(name = "delete_success_row_count")
    private Integer deleteSuccessRowCount;
    @Column(name = "detail_file")
    private String detailFile;
    @Column(name = "failure_reason")
    private String failureReason;
}
