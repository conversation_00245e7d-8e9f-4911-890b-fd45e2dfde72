package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAmendApplicationEvent extends BaseApplicationEvent<ItemAmendPayloadDto> {

    public ItemAmendApplicationEvent(Object source, ItemAmendPayloadDto payload) {
        super(source, payload);
    }
}