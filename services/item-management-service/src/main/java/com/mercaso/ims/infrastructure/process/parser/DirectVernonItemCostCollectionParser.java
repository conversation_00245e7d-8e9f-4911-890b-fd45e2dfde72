package com.mercaso.ims.infrastructure.process.parser;

import static com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources.FINALE_PURCHASE_ORDER;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinalePurchaseOrderDto;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class DirectVernonItemCostCollectionParser implements ItemCostCollectionParser {

    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    private final ItemCostCollectionService itemCostCollectionService;

    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        if (!FINALE_PURCHASE_ORDER.equals(itemCostCollection.getSource())
            || !ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER.equals(itemCostCollection.getType())) {
            log.error("ItemCostCollection {} is not a valid source or type for DirectVernonItemCostCollectionParser",
                itemCostCollection);
            return new ArrayList<>();
        }

        FinalePurchaseOrderDto purchaseOrderDto = finaleExternalApiAdaptor.getPurchaseOrder(itemCostCollection.getVendorCollectionNumber());
        if (purchaseOrderDto == null) {
            log.error("FinalePurchaseOrderDto is null for ItemCostCollection {}", itemCostCollection);
            return new ArrayList<>();
        }
        return parseInvoiceItem(purchaseOrderDto);

    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return false;
    }


    private List<ItemCostCollectionItemParsingResultDto> parseInvoiceItem(FinalePurchaseOrderDto purchaseOrderDto) {
        return purchaseOrderDto.getOrderItemList().stream().map(
            item -> ItemCostCollectionItemParsingResultDto.builder()
                .vendorSkuNumber(item.getProductId())
                .cost(item.getUnitPrice())
                .build()
        ).toList();
    }


}