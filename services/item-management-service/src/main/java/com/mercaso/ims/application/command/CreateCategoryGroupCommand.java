package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateCategoryGroupCommand extends BaseCommand {

    private String department;
    private String category;
    private String subCategory;
}
