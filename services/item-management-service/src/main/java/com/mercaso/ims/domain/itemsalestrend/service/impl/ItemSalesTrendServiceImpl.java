package com.mercaso.ims.domain.itemsalestrend.service.impl;

import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.service.ItemSalesTrendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ItemSalesTrendServiceImpl implements ItemSalesTrendService {
    @Override
    public List<ItemSalesTrend> findByItemIdAndTimeGrain(String itemId, String timeGrain) {
        return List.of();
    }
}
