package com.mercaso.ims.infrastructure.schedule;

import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PromoScheduler {

    private static final Integer LOCK_KEY = "[PromoScheduler.checkAndUpdatePromos]".hashCode();
    private final ItemPromoPriceService itemPromoPriceService;
    private final ItemApplicationService itemApplicationService;

    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;

    @Scheduled(cron = "0 1 0 * * *", zone = "America/Los_Angeles")
    public void checkAndUpdatePromos() {

        log.info("Starting check and update promo task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                LOCK_KEY,
                "Check and update promo Task");
            if (isAcquired == null || !isAcquired) {
                log.warn("[PromoScheduler.checkAndUpdatePromos] is already in progress, please try again later.");
                return;
            }
            activePromo();
            expirePromo();

        } finally {
            pgAdvisoryLock.unLock(entityManager, LOCK_KEY, "unlock [PromoScheduler.checkAndUpdatePromos]");
            entityManager.close();
            log.info("Finished check and update promo task");
        }

    }

    private void activePromo() {
        Instant now = Instant.now();
        Instant yesterday = now.minus(1, ChronoUnit.DAYS);

        List<ItemPromoPrice> promos = itemPromoPriceService.findByPromoBeginTimeBetween(yesterday, now);
        log.info("Found {} promos as activePromo  yesterday :{} new :{}", promos.size(), yesterday, now);
        // active Promo
        promos.forEach(promo -> {
            if (!Boolean.TRUE.equals(promo.getPromoFlag()) && promo.getPromoPrice().compareTo(BigDecimal.ZERO) >= 0
                && promo.getPromoBeginTime().compareTo(now) <= 0
                && promo.getPromoEndTime().compareTo(now) >= 0) {
                UpdateItemPromoPriceCommand command = UpdateItemPromoPriceCommand.builder()
                    .promoBeginTime(promo.getPromoBeginTime())
                    .promoFlag(true)
                    .promoPrice(promo.getPromoPrice())
                    .promoEndTime(promo.getPromoEndTime())
                    .promoBeginTime(promo.getPromoBeginTime())
                    .itemId(promo.getItemId())
                    .build();
                itemApplicationService.updatePromoPrice(command);
                logPromoStatusChange(promo, "Activated");
            }
        });
    }


    private void expirePromo() {
        Instant now = Instant.now();
        Instant yesterday = now.minus(1, ChronoUnit.DAYS);

        List<ItemPromoPrice> promos = itemPromoPriceService.findByPromoEndTimeBetween(yesterday, now);
        log.info("Found {} promos as Deactivated  yesterday :{} new :{}", promos.size(), yesterday, now);

        //expire Promo
        promos.forEach(promo -> {
            if (Boolean.TRUE.equals(promo.getPromoFlag()) && promo.getPromoEndTime().compareTo(now) < 0) {

                UpdateItemPromoPriceCommand command = UpdateItemPromoPriceCommand.builder()
                    .promoBeginTime(promo.getPromoBeginTime())
                    .promoFlag(false)
                    .promoPrice(promo.getPromoPrice())
                    .promoEndTime(promo.getPromoEndTime())
                    .promoBeginTime(promo.getPromoBeginTime())
                    .itemId(promo.getItemId())
                    .build();
                itemApplicationService.updatePromoPrice(command);
                logPromoStatusChange(promo, "Deactivated");
            }
        });
    }


    private void logPromoStatusChange(ItemPromoPrice promo, String status) {
        log.info("Promo {} {} at {}", promo.getId(), status, Instant.now());

    }
}

