package com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.dataobject;

import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.util.Date;

@Entity
@Table(name = "item_sales_trend")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_sales_trend set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class ItemSalesTrendDo extends BaseDo {

    @Column(name = "item_id")
    private String itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "sales_quantity")
    private Integer salesQuantity;

    @Column(name = "time_dim")
    private Date timeDim;

    @Enumerated(EnumType.STRING)
    @Column(name = "time_grain")
    private ItemSalesTrendTimeGrain timeGrain;

}
