package com.mercaso.ims.application.query;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class VendorItemCostExceptionRecordQuery extends ExceptionRecordQuery {

    private String skuNumber;

    private String itemTitle;

    private UUID vendorId;

    private AvailabilityStatus itemStatus;
}
