package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_EXCEPTION_RECORD;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemBoundToPriceGroupApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemBoundToPriceGroupPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.service.AsyncItemVersionService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ShopifyApplicationService;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceSpecification;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemApplicationEventListener {

    private final ShopifyApplicationService shopifyApplicationService;
    private final ItemPriceApplicationService itemPriceApplicationService;
    private final ItemRegPriceSpecification itemRegPriceSpecification;
    private final ExceptionRecordApplicationService exceptionRecordApplicationService;
    private final FeatureFlagsManager featureFlagsManager;
    private final AsyncItemVersionService asyncItemVersionService;
    private final FinaleApplicationService finaleApplicationService;


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleItemCreatedApplicationEvent(ItemCreatedApplicationEvent itemCreatedApplicationEvent) {

        ItemCreatedPayloadDto itemCreatedPayloadDto = itemCreatedApplicationEvent.getPayload();

        createItemVersion(itemCreatedPayloadDto.getData());

        shopifyApplicationService.syncItemCreatedEvent(itemCreatedApplicationEvent);

        checkPriceException(itemCreatedPayloadDto.getData().getItemRegPrice(), itemCreatedApplicationEvent.getBusinessEventId());

        finaleApplicationService.syncItemToFinale(itemCreatedPayloadDto.getData());

        log.info("handleItemCreatedApplicationEvent for request ={}", itemCreatedPayloadDto);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleItemAmendApplicationEvent(ItemAmendApplicationEvent itemAmendApplicationEvent) {
        ItemAmendPayloadDto itemAmendPayloadDto = itemAmendApplicationEvent.getPayload();

        createItemVersion(itemAmendPayloadDto.getData().getCurrent());

        shopifyApplicationService.syncItemAmendEvent(itemAmendApplicationEvent);

        if (itemAmendPayloadDto.isPriceChanged()) {
            checkPriceException(itemAmendPayloadDto.getData().getCurrent().getItemRegPrice(),
                itemAmendApplicationEvent.getBusinessEventId());
        }
        if (itemAmendPayloadDto.isPrimaryVendorChanged() || itemAmendPayloadDto.isStatusChanged()) {
            finaleApplicationService.syncItemToFinale(itemAmendPayloadDto.getCurrent());
        }

        log.info("handleItemAmendApplicationEvent for request ={}", itemAmendPayloadDto);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleItemDeletedApplicationEvent(ItemDeletedApplicationEvent itemDeletedApplicationEvent) {
        ItemDeletedPayloadDto itemDeletedPayloadDto = itemDeletedApplicationEvent.getPayload();

        shopifyApplicationService.syncItemDeleteEvent(itemDeletedApplicationEvent);

        log.info("handleItemCreatedApplicationEvent for request ={}", itemDeletedPayloadDto);
    }


    @EventListener
    public void handleItemBoundToPriceGroupApplicationEvent(ItemBoundToPriceGroupApplicationEvent itemBoundToPriceGroupApplicationEvent) {
        ItemBoundToPriceGroupPayloadDto payload = itemBoundToPriceGroupApplicationEvent.getPayload();
        UpdateItemRegPriceCommand command = new UpdateItemRegPriceCommand(payload.getItemId(), payload.getPrice());

        List<UpdateItemRegPriceCommand> commands = List.of(command);
        itemPriceApplicationService.batchUpdateRegPrice(commands);

        log.info("handleItemBoundToPriceGroupApplicationEvent for payload ={}", payload);
    }


    private void checkPriceException(ItemRegPriceDto itemRegPrice, UUID eventId) {
        if (!featureFlagsManager.isFeatureOn(IMS_EXCEPTION_RECORD)) {
            return;
        }
        String exception = itemRegPriceSpecification.getExceptionMessage(itemRegPrice);

        if (StringUtils.isNotBlank(exception)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(itemRegPrice.getItemId())
                .entityType(EntityType.ITEM)
                .exceptionType(ExceptionRecordType.PRICE_EXCEPTION)
                .description(exception)
                .build());
        }
    }

    private void createItemVersion(ItemDto itemDto) {
        log.info("begin createItemVersion for id={}", itemDto.getId());
        asyncItemVersionService.asyncSaveItemVersion(itemDto.getId());
    }


}
