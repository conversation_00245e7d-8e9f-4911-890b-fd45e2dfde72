package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.ItemTag;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateOrUpdateItemRequestData;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class UpdateItemRequestDataListener extends ItemAdjustmentRequestDataListener<CreateOrUpdateItemRequestData> {

    private final ItemRegPriceService itemRegPriceService;
    private String currentSku;

    public UpdateItemRequestDataListener(UUID itemAdjustmentRequestId,
                                         ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                         ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                         ItemRepository itemRepository,
                                         VendorRepository vendorRepository, ItemRegPriceService itemRegPriceService,
                                         VendorItemRepository vendorItemRepository,
                                         CategoryApplicationService categoryApplicationService,
                                         FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
        this.itemRegPriceService = itemRegPriceService;
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(CreateOrUpdateItemRequestData updateItemRequestData) {
        CreateItemAdjustmentRequestDetailCommand command = CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(cleanInput(updateItemRequestData.getSku()))
            .companyId(updateItemRequestData.getCompanyId())
            .locationId(updateItemRequestData.getLocationId())
            .newDescription(updateItemRequestData.getNewDescription())
            .aisle(updateItemRequestData.getPrimaryVendorItemAisle())
            .itemStatus(updateItemRequestData.getStatus() == null ? null
                : AvailabilityStatus.fromString(updateItemRequestData.getStatus()))
            .primaryPoVendor(updateItemRequestData.getPrimaryPoVendor())
            .primaryJitVendor(updateItemRequestData.getPrimaryJitVendor())
            .title(updateItemRequestData.getItemDescription())
            .packageSize(updateItemRequestData.getPackSize())
            .itemSize(updateItemRequestData.getItemSize())
            .itemUnitMeasure(updateItemRequestData.getItemUnitMeasure())
            .brand(updateItemRequestData.getBrand())
            .regPricePackNoCrv(updateItemRequestData.getRegPricePack())
            .caseUpc(updateItemRequestData.getCaseUpc())
            .eachUpc(updateItemRequestData.getEachUpc())
            .vendorItemNumber(updateItemRequestData.getPrimaryVendorItemNumber())
            .primaryPoVendorItemCost(updateItemRequestData.getPrimaryPoVendorItemCost())
            .primaryJitVendorItemCost(updateItemRequestData.getPrimaryJitVendorItemCost())
            .promoFlag(updateItemRequestData.getPromoFlag())
            .promoPricePackNoCrv(updateItemRequestData.getPromoPrice())
            .crvFlag(updateItemRequestData.getCrvFlag())
            .imageUrl(updateItemRequestData.getImageUrl())
            .tags(getTagsString(updateItemRequestData))
            .disposition(updateItemRequestData.getNotes())
            .length(updateItemRequestData.getLength())
            .height(updateItemRequestData.getHeight())
            .width(updateItemRequestData.getWidth())
            .weight(updateItemRequestData.getWeight())
            .weightUnit(updateItemRequestData.getWeightUnit())
            .cooler(updateItemRequestData.getCooler())
            .build();

        if (updateItemRequestData.isCategoryChanged()) {
            command.setDepartment(updateItemRequestData.getDepartment());
            command.setCategory(updateItemRequestData.getCategory());
            command.setSubCategory(updateItemRequestData.getSubCategory());
            command.setClassType(updateItemRequestData.getClassType());
        }
        return command;

    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(CreateOrUpdateItemRequestData updateItemRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        this.currentSku = cleanInput(updateItemRequestData.getSku());
        ItemAdjustmentFailureReason checkItemExistResult = checkItemExist(cleanInput(updateItemRequestData.getSku()));
        if (checkItemExistResult != null) {
            failureReasons.add(checkItemExistResult);
            return failureReasons;
        }
        List<ItemAdjustmentFailureReason> newTemplateInputCheckResult = newTemplateInputCheck(updateItemRequestData);
        failureReasons.addAll(newTemplateInputCheckResult);
        ItemAdjustmentFailureReason checkItemPriceGroupResult = checkItemPriceGroup(cleanInput(updateItemRequestData.getSku()),
            updateItemRequestData.getRegPricePack());
        if (checkItemPriceGroupResult != null) {
            failureReasons.add(checkItemPriceGroupResult);
        }

        ItemAdjustmentFailureReason checkJITPrimaryVendorItem = checkJITPrimaryVendorItem(updateItemRequestData.getPrimaryJitVendor(),
            updateItemRequestData.getPrimaryJitVendorItemCost());
        if (checkJITPrimaryVendorItem != null) {
            failureReasons.add(checkJITPrimaryVendorItem);
        }

        ItemAdjustmentFailureReason checkPOPrimaryVendorItem = checkPOPrimaryVendorItem(updateItemRequestData.getPrimaryPoVendor(),
            updateItemRequestData.getPrimaryPoVendorItemCost());
        if (checkPOPrimaryVendorItem != null) {
            failureReasons.add(checkPOPrimaryVendorItem);
        }
        return failureReasons;
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.UPDATE;
    }


    @Override
    protected ItemAdjustmentFailureReason validateCategory(String department,
        String category,
        String subCategory,
        String clazz) {
        // Use feature flag to determine which validation method to use
        if (StringUtils.isBlank(department) && StringUtils.isBlank(category) && StringUtils.isBlank(subCategory)
            && StringUtils.isBlank(clazz)) {
            String sku = cleanInput(currentSku);
            Item item = itemRepository.findBySku(sku);
            if (item != null && null == item.getCategoryId()) {
                return ItemAdjustmentFailureReason.TAXONOMY_IS_REQUIRED;
            }
        }

        return super.validateCategory(department, category, subCategory, clazz);
    }

    private ItemRegPrice getItemRegPrice(String sku) {
        Item item = itemRepository.findBySku(sku);
        if (item == null) {
            return null;
        }
        return itemRegPriceService.findByItemId(item.getId());

    }


    private String getTagsString(CreateOrUpdateItemRequestData updateItemRequestData) {
        if (StringUtils.isNotEmpty(updateItemRequestData.getAppendTags())) {
            Item item = itemRepository.findBySku(cleanInput(updateItemRequestData.getSku()));
            if (item != null && CollectionUtils.isNotEmpty(item.getItemTags())) {
                String existingTags = item.getItemTags().stream()
                    .map(ItemTag::getTagName)
                    .collect(Collectors.joining(","));
                return existingTags + "," + updateItemRequestData.getAppendTags();
            }
            return updateItemRequestData.getAppendTags();
        }
        if (StringUtils.isNotEmpty(updateItemRequestData.getTags())) {
            return updateItemRequestData.getTags();
        }
        return null;
    }

    private ItemAdjustmentFailureReason checkItemPriceGroup(String sku, BigDecimal newPrice) {
        ItemRegPrice itemRegPrice = getItemRegPrice(sku);
        if (itemRegPrice != null && itemRegPrice.getItemPriceGroupId() != null && newPrice != null) {
            return ItemAdjustmentFailureReason.CANT_CHANGE_PRICE_SINCE_ITEM_IN_PRICE_GROUP;
        }
        return null;
    }

    private ItemAdjustmentFailureReason checkJITPrimaryVendorItem(String vendorName, BigDecimal vendorItemCost) {
        if (checkVendorItemCost(vendorName, vendorItemCost)) {
            return ItemAdjustmentFailureReason.PRIMARY_JIT_VENDOR_ITEM_COST_IS_REQUIRED;
        }
        return null;
    }

    private ItemAdjustmentFailureReason checkPOPrimaryVendorItem(String vendorName, BigDecimal vendorItemCost) {
        if (checkVendorItemCost(vendorName, vendorItemCost)) {
            return ItemAdjustmentFailureReason.PRIMARY_DIRECT_VENDOR_ITEM_COST_IS_REQUIRED;
        }
        return null;
    }


    private Boolean checkVendorItemCost(String vendorName, BigDecimal vendorItemCost) {
        if (StringUtils.isBlank(vendorName)) {
            return Boolean.FALSE;
        }

        Item item = itemRepository.findBySku(cleanInput(currentSku));

        Vendor vendor = vendorRepository.findByVendorName(vendorName);

        if (null == item || null == vendor) {
            return Boolean.FALSE;
        }

        VendorItem vendorItem = vendorItemRepository.findByVendorIDAndItemId(vendor.getId(), item.getId());
        if (null == vendorItem && null == vendorItemCost) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
