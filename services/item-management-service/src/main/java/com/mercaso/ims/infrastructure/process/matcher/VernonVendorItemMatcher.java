package com.mercaso.ims.infrastructure.process.matcher;

import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import org.springframework.stereotype.Component;

@Component
public class VernonVendorItemMatcher extends JitVendorItemMatcher {


    public VernonVendorItemMatcher(VendorItemService vendorItemService, VendorService vendorService) {
        super(vendorItemService, vendorService);
    }

    @Override
    String getVendorName() {
        return VendorConstant.VERNON_SALES;
    }
    
}
