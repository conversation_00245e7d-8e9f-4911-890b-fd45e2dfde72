package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinaleSupplierItemDto {

    @JsonProperty("supplierPartyUrl")
    private String supplierPartyUrl;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("supplierPrefOrderId")
    private String supplierPrefOrderId;
    @JsonProperty("quantityAvailable")
    private Long quantityAvailable;
    @JsonProperty("supplierProductId")
    private String supplierProductId;
    @JsonProperty("currencyUomId")
    private String currencyUomId;
    @JsonProperty("leadTime")
    private Integer leadTime;
    @JsonProperty("generalComments")
    private String generalComments;

    public FinaleSupplierItemDto(String accountPathComponent,
        String vendorFinaleId,
        BigDecimal cost,
        Long inventory,
        String vendorSkuNumber) {
        this.supplierPartyUrl = "/" + accountPathComponent + "/api/partygroup/" + vendorFinaleId;
        this.price = cost;
        this.quantityAvailable = inventory;
        this.supplierProductId = vendorSkuNumber;
        this.currencyUomId = "USD";
    }

    public String getVendorFinaleId() {
        return supplierPartyUrl == null ? "" : supplierPartyUrl.substring(supplierPartyUrl.lastIndexOf("/") + 1);
    }
}
