package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.DeleteItemRequestData;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class DeleteItemRequestDataListener extends ItemAdjustmentRequestDataListener<DeleteItemRequestData> {


    public DeleteItemRequestDataListener(UUID itemAdjustmentRequestId,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
        ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
        ItemRepository itemRepository,
        VendorRepository vendorRepository,
        VendorItemRepository vendorItemRepository,
        CategoryApplicationService categoryApplicationService,
        FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(DeleteItemRequestData requestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(requestData.getSku())
            .itemStatus(AvailabilityStatus.DELETED)
            .build();

    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(DeleteItemRequestData requestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        ItemAdjustmentFailureReason checkItemExistResult = checkItemExist(requestData.getSku());
        if (checkItemExistResult != null) {
            failureReasons.add(checkItemExistResult);
        }
        return failureReasons;
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.DELETE;
    }

}