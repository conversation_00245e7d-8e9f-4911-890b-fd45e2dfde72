package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InventoryItemInfoDto {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("sku")
    private String sku;

    @JsonProperty("created_at")
    private ZonedDateTime createdAt;

    @JsonProperty("updated_at")
    private ZonedDateTime updatedAt;

    @JsonProperty("requires_shipping")
    private Boolean requiresShipping;

    @JsonProperty("cost")
    private String cost;

    @JsonProperty("country_code_of_origin")
    private String countryCodeOfOrigin;

    @JsonProperty("province_code_of_origin")
    private String provinceCodeOfOrigin;

    @JsonProperty("harmonized_system_code")
    private String harmonizedSystemCode;

    @JsonProperty("tracked")
    private Boolean tracked;

    @JsonProperty("admin_graphql_api_id")
    private String adminGraphqlApiId;

}
