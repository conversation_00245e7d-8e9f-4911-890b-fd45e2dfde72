package com.mercaso.ims.application.dto;


import static com.mercaso.ims.domain.attribute.AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID;
import static com.mercaso.ims.domain.attribute.AttributeConstant.VARIANT_WEIGHT_ATTRIBUTE_ID;
import static com.mercaso.ims.domain.attribute.AttributeConstant.VOLUME_VALUE_ATTRIBUTE_ID;
import static com.mercaso.ims.domain.attribute.AttributeConstant.WEIGHT_ATTRIBUTE_ID;

import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemDto extends BaseDto {

    private UUID id;

    private String name;

    private String title;

    private String skuNumber;

    private String description;

    private String note;

    private String detail;

    private String packageType;

    private Integer packageSize;

    private String itemType;

    private String availabilityStatus;

    private String handle;

    private String shelfLife;

    private UUID primaryVendorId;

    private UUID backupVendorId;

    private UUID brandId;

    private Integer companyId;

    private Integer locationId;

    // Leaf node ID
    private UUID categoryId;

    private String categoryName;

    private CategoryDto categoryTree;

    private String priceLinking;

    private String type;

    private String bodyHtml;

    private String photoName;

    private String photoUrl;

    private BrandDto brand;

    private VendorItemDto primaryVendorItem;

    private VendorItemDto backupVendorItem;

    private String department;

    private String category;

    private String subCategory;

    private String clazz;

    private String newDescription;

    private Double length;

    private Double height;

    private Double width;

    private Integer lastVersionNumber;

    private List<ItemAttributeDto> itemAttributes;

    private List<ItemImageDto> itemImages;

    private List<ItemUPCDto> itemUPCs;

    private List<ItemTagDto> itemTags;

    private ItemRegPriceDto itemRegPrice;

    private List<ItemPromoPriceDto> itemPromoPrices;

    private List<VendorItemDto> vendorItemDtos;

    private ItemParetoGrade itemGrade;

    public String getVariantBarcode() {
        return getUpcsByType(ItemUpcType.VARIANT_BARCODE);
    }

    public String getMasterUpc() {
        return getUpcsByType(ItemUpcType.MASTER_UPC);
    }

    public String getCaseUpc() {
        return getUpcsByType(ItemUpcType.CASE_UPC);
    }

    public String getEachUpc() {
        return getUpcsByType(ItemUpcType.EACH_UPC);
    }


    private String getUpcsByType(ItemUpcType type) {
        if (CollectionUtils.isEmpty(itemUPCs)) {
            return null;
        }
        List<String> upcs = itemUPCs.stream()
            .filter(itemUPCDto -> itemUPCDto.getItemUpcType().equals(type))
            .map(ItemUPCDto::getUpcNumber)
            .toList();
        return String.join(",", upcs);
    }


    public Float getItemSize() {
        ItemAttributeDto itemSizeAttribute = getItemSizeAttribute();
        if (itemSizeAttribute != null && itemSizeAttribute.getValue() != null) {
            return Float.parseFloat(itemSizeAttribute.getValue());
        }
        return null;
    }

    public String getItemSizeUnitMeasure() {
        ItemAttributeDto itemSizeAttribute = getItemSizeAttribute();
        if (itemSizeAttribute != null) {
            return itemSizeAttribute.getUnit();
        }
        return null;
    }

    public ItemAttributeDto getItemSizeAttribute() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
            .filter(itemAttributeDto -> ITEM_SIZE_ATTRIBUTE_ID.equals(itemAttributeDto.getAttributeId()))
            .findFirst()
            .orElse(null);
    }

    public ItemAttributeDto getBottleSizeAttribute() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
            .filter(attr -> attr.getAttributeId().equals(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID))
            .findFirst()
            .orElseGet(() -> itemAttributes.stream()
                .filter(attr -> attr.getAttributeId().equals(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID))
                .findFirst()
                .orElse(null));
    }

    public ItemAttributeDto getBottleSizeAttributeV2() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
                .filter(attr -> attr.getAttributeId().equals(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID))
                .findFirst()
                .orElse(null);
    }

    public String getBottleSizeStr() {
        ItemAttributeDto bottleSizeAttribute = getBottleSizeAttribute();
        if (bottleSizeAttribute != null && bottleSizeAttribute.getValue() != null) {
            String unit = bottleSizeAttribute.getUnit() == null ? "" : bottleSizeAttribute.getUnit();
            return bottleSizeAttribute.getValue() + " " + unit;
        }
        return null;
    }

    public String getBottleSize() {
        ItemAttributeDto bottleSizeAttribute = getBottleSizeAttribute();
        if (bottleSizeAttribute != null && bottleSizeAttribute.getValue() != null) {
            return bottleSizeAttribute.getValue();
        }
        return null;
    }

    public String getBottleSizeUnit() {
        ItemAttributeDto bottleSizeAttribute = getBottleSizeAttribute();
        if (bottleSizeAttribute != null && bottleSizeAttribute.getUnit() != null) {
            return bottleSizeAttribute.getUnit();
        }
        return null;
    }

    public Double getVariantWeight() {
        ItemAttributeDto variantWeightAttribute = getVariantWeightAttribute();
        if (variantWeightAttribute != null && variantWeightAttribute.getValue() != null) {
            return Double.parseDouble(variantWeightAttribute.getValue());
        }
        return null;
    }

    public String getVariantWeightUnit() {
        ItemAttributeDto variantWeightAttribute = getVariantWeightAttribute();
        if (variantWeightAttribute != null) {
            return variantWeightAttribute.getUnit();
        }
        return null;
    }

    public ItemAttributeDto getVariantWeightAttribute() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
            .filter(itemAttributeDto -> VARIANT_WEIGHT_ATTRIBUTE_ID.equals(itemAttributeDto.getAttributeId()))
            .findFirst()
            .orElse(null);
    }

    public Double getVolumeValue() {
        ItemAttributeDto variantWeightAttribute = getVolumeValueAttribute();
        if (variantWeightAttribute != null) {
            return Double.parseDouble(variantWeightAttribute.getValue());
        }
        return null;
    }

    public String getVolumeUom() {
        ItemAttributeDto variantWeightAttribute = getVolumeValueAttribute();
        if (variantWeightAttribute != null) {
            return variantWeightAttribute.getUnit();
        }
        return null;
    }

    public ItemAttributeDto getVolumeValueAttribute() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
            .filter(itemAttributeDto -> VOLUME_VALUE_ATTRIBUTE_ID.equals(itemAttributeDto.getAttributeId()))
            .findFirst()
            .orElse(null);
    }

    public BigDecimal getIndividualPrice() {
        Boolean promoFlag = getPromoFlag();

        return Boolean.TRUE.equals(promoFlag) && null != getPromoIndividualPrice() ? getPromoIndividualPrice()
            : getRegIndividualPrice();
    }

    public BigDecimal getPrice() {
        Boolean promoFlag = getPromoFlag();

        return Boolean.TRUE.equals(promoFlag) && null != getPromoPrice() ? getPromoPrice() : getRegPrice();
    }

    public BigDecimal getPricePlusCrv() {
        Boolean promoFlag = getPromoFlag();

        return Boolean.TRUE.equals(promoFlag) && null != getPromoPricePlusCrv() ? getPromoPricePlusCrv() : getRegPricePlusCrv();
    }

    public Boolean getPromoFlag() {
        if (CollectionUtils.isEmpty(itemPromoPrices)) {
            return Boolean.FALSE;
        }
        return Optional.ofNullable(itemPromoPrices)
            .orElse(Collections.emptyList())
            .stream()
            .filter(dto -> dto != null && dto.getPromoFlag() != null)
            .map(ItemPromoPriceDto::getPromoFlag)
            .findFirst().orElse(Boolean.FALSE);
    }


    public BigDecimal getPromoIndividualPrice() {
        if (CollectionUtils.isEmpty(itemPromoPrices)) {
            return null;
        }
        return Optional.ofNullable(itemPromoPrices)
            .orElse(Collections.emptyList())
            .stream()
            .filter(dto -> dto != null && dto.getPromoPriceIndividual() != null)
            .map(ItemPromoPriceDto::getPromoPriceIndividual)
            .findFirst().orElse(null);
    }

    public BigDecimal getPromoPrice() {
        if (CollectionUtils.isEmpty(itemPromoPrices)) {
            return null;
        }
        return Optional.ofNullable(itemPromoPrices)
            .orElse(Collections.emptyList())
            .stream()
            .filter(dto -> dto != null && dto.getPromoPrice() != null)
            .map(ItemPromoPriceDto::getPromoPrice)
            .findFirst().orElse(null);
    }

    public BigDecimal getPromoPricePlusCrv() {
        if (CollectionUtils.isEmpty(itemPromoPrices)) {
            return null;
        }
        return Optional.ofNullable(itemPromoPrices)
            .orElse(Collections.emptyList())
            .stream()
            .filter(dto -> dto != null && dto.getPromoPricePlusCrv() != null)
            .map(ItemPromoPriceDto::getPromoPricePlusCrv)
            .findFirst().orElse(null);
    }

    public BigDecimal getRegIndividualPrice() {
        return Optional.ofNullable(itemRegPrice)
            .map(ItemRegPriceDto::getRegPriceIndividual)
            .orElse(null);
    }

    public BigDecimal getRegPrice() {
        return Optional.ofNullable(itemRegPrice)
            .map(ItemRegPriceDto::getRegPrice)
            .orElse(null);
    }

    public BigDecimal getRegPricePlusCrv() {
        return Optional.ofNullable(itemRegPrice)
            .map(ItemRegPriceDto::getRegPricePlusCrv)
            .orElse(null);
    }

    public Double getWeight() {
        ItemAttributeDto weightAttribute = getWeightAttribute();
        if (weightAttribute != null && weightAttribute.getValue() != null) {
            return Double.parseDouble(weightAttribute.getValue());
        }
        return null;
    }

    public String getWeightUnit() {
        ItemAttributeDto weightAttribute = getWeightAttribute();
        if (weightAttribute != null) {
            return weightAttribute.getUnit();
        }
        return null;
    }

    public ItemAttributeDto getWeightAttribute() {
        if (CollectionUtils.isEmpty(itemAttributes)) {
            return null;
        }
        return itemAttributes.stream()
            .filter(itemAttributeDto -> WEIGHT_ATTRIBUTE_ID.equals(itemAttributeDto.getAttributeId()))
            .findFirst()
            .orElse(null);
    }

    public Boolean getCrvFlag() {
        return Optional.ofNullable(itemRegPrice)
            .map(ItemRegPriceDto::getCrvFlag)
            .orElse(false);
    }

    public BigDecimal getCrvAmount() {
        return Optional.ofNullable(itemRegPrice)
            .map(ItemRegPriceDto::getCrvAmount)
            .orElse(BigDecimal.ZERO);
    }


    public void clearCategoryInfo() {
        this.setClazz(null);
        this.setSubCategory(null);
        this.setCategory(null);
        this.setDepartment(null);
    }

}