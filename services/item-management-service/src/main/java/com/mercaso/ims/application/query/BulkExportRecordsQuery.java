package com.mercaso.ims.application.query;

import com.mercaso.ims.infrastructure.repository.PageQuery;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class BulkExportRecordsQuery extends PageQuery {

    private Instant createdStartDate;

    private Instant createdEndDate;

    private String createdBy;

    private SortType sort;

    @Getter
    public enum SortType {
        CREATED_AT_DESC,
    }

}