package com.mercaso.ims.infrastructure.repository.company;

import com.mercaso.ims.domain.company.Company;
import com.mercaso.ims.domain.company.CompanyRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.company.jpa.CompanyJpaDao;
import com.mercaso.ims.infrastructure.repository.company.jpa.dataobject.CompanyDo;
import com.mercaso.ims.infrastructure.repository.company.jpa.mapper.CompanyDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CompanyRepositoryImpl implements CompanyRepository {

    private final CompanyDoMapper companyDoMapper;

    private final CompanyJpaDao companyJpaDao;


    @Override
    public Company save(Company company) {
        CompanyDo companyDo = companyDoMapper.domainToDo(company);
        companyDo = companyJpaDao.save(companyDo);
        return companyDoMapper.doToDomain(companyDo);
    }

    @Override
    public Company findById(UUID id) {
        CompanyDo companyDo = companyJpaDao.findById(id).orElse(null);
        return companyDoMapper.doToDomain(companyDo);
    }

    @Override
    public Company update(Company company) {
        CompanyDo companyDo = companyJpaDao.findById(company.getId()).orElse(null);
        if (Objects.isNull(companyDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.COMPANY_NOT_FOUND.getCode());
        }
        CompanyDo companyDoTarget = companyDoMapper.domainToDo(company);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(companyDoTarget, companyDo, ignoreProperties.toArray(new String[0]));
        CompanyDo result = companyJpaDao.save(companyDo);
        return companyDoMapper.doToDomain(result);
    }

    @Override
    public Company deleteById(UUID id) {
        CompanyDo companyDo = companyJpaDao.findById(id).orElse(null);
        if (null == companyDo) {
            return null;
        }
        companyDo.setDeletedAt(Instant.now());
        companyDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return companyDoMapper.doToDomain(companyJpaDao.save(companyDo));
    }

    @Override
    public Company findByCompanyId(Long companyId) {
        CompanyDo companyDo = companyJpaDao.findByCompanyId(companyId);
        if (null == companyDo) {
            return null;
        }
        return companyDoMapper.doToDomain(companyDo);
    }
}
