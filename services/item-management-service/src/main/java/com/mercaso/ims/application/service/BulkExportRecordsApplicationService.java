package com.mercaso.ims.application.service;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;

import java.util.UUID;

public interface BulkExportRecordsApplicationService {

    BulkExportRecordsDto save(BulkExportRecordsCommand bulkExportRecordsCommand);

    DocumentResponse getBulkExportRecordsFile(UUID id);
}
