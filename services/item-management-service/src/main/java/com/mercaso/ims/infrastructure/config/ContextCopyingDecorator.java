package com.mercaso.ims.infrastructure.config;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.core.task.TaskDecorator;
import org.springframework.security.concurrent.DelegatingSecurityContextRunnable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


@Slf4j
public class ContextCopyingDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        RequestAttributes context = RequestContextHolder.getRequestAttributes();

        SecurityContext securityContext = SecurityContextHolder.getContext();

        final RequestAttributes contextCopy = context != null
            ? new ServletRequestAttributes(((ServletRequestAttributes) context).getRequest())
            : null;

        Map<String, String> headers = CustomerRequestContext.getHeaders();
        if ((headers == null || headers.isEmpty()) && context != null) {
            headers = extractHeaders(context);
        }

        final Map<String, String> finalHeaders = headers != null ? new HashMap<>(headers) : new HashMap<>();

        return new RunnableWrapper(new DelegatingSecurityContextRunnable(() -> {
            try {

                if (context != null) {
                    RequestContextHolder.setRequestAttributes(contextCopy);
                }

                CustomerRequestContext.setHeaders(finalHeaders);

                runnable.run();
            } finally {
                if (contextCopy != null) {
                    RequestContextHolder.resetRequestAttributes();
                }
                CustomerRequestContext.clear();
            }
        }, securityContext));
    }


    private Map<String, String> extractHeaders(RequestAttributes attributes) {
        if (attributes instanceof ServletRequestAttributes requestAttributes) {
            HttpServletRequest request = requestAttributes.getRequest();
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement().toLowerCase();
                headers.put(headerName, request.getHeader(headerName));
            }
            return headers;
        }
        return Collections.emptyMap();
    }
}
