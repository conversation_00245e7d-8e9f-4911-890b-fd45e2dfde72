package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.UpdateVendorItemCostData;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Slf4j
public class UpdateVendorItemCostRequestDataListener extends ItemAdjustmentRequestDataListener<UpdateVendorItemCostData> {

    protected String vendorName;
    

    public UpdateVendorItemCostRequestDataListener(UUID itemAdjustmentRequestId,
                                                   ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                                   ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                                   ItemRepository itemRepository,
                                                   VendorRepository vendorRepository,
                                                   VendorItemRepository vendorItemRepository, String vendorName,
                                                   CategoryApplicationService categoryApplicationService,
                                                   FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
        this.vendorName = vendorName;
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(UpdateVendorItemCostData updateVendorItemCostData) {
        String sku = getSkuByVendorItemNumber(updateVendorItemCostData.getVendorItemNumber());
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(sku)
            .vendor(vendorName)
            .vendorItemNumber(updateVendorItemCostData.getVendorItemNumber())
            .poVendorItemCost(updateVendorItemCostData.getUnitCost())
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.UPDATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(UpdateVendorItemCostData updateVendorItemCostData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        String sku = getSkuByVendorItemNumber(updateVendorItemCostData.getVendorItemNumber());
        if (StringUtils.isBlank(sku)) {
            failureReasons.add(ItemAdjustmentFailureReason.ITEM_NOT_FOUND);
        }

        return failureReasons;

    }


    private String getSkuByVendorItemNumber(String vendorItemNumber) {
        if (StringUtils.isBlank(vendorItemNumber)) {
            log.warn("Vendor item number is blank");
            return "";
        }

        Vendor vendor = vendorRepository.findByVendorName(vendorName);
        if (vendor == null) {
            log.warn("Vendor not found for name: {}", vendorName);
            return "";
        }

        List<VendorItem> vendorItems = vendorItemRepository.findByVendorIDAndVendorSkuNum(vendor.getId(), vendorItemNumber);
        if (CollectionUtils.isEmpty(vendorItems) || vendorItems.getFirst().getItemId() == null) {
            log.warn("VendorItem not found or itemId is null for vendorId: {} and vendorSkuNum: {}",
                vendor.getId(), vendorItemNumber);
            return "";
        }

        Item item = itemRepository.findById(vendorItems.getFirst().getItemId());
        if (item == null) {
            log.warn("Item not found for itemId: {}", vendorItems.getFirst().getItemId());
            return "";
        }

        return item.getSkuNumber();
    }

}
