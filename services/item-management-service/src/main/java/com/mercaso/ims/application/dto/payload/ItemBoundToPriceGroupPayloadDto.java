package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemBoundToPriceGroupPayloadDto extends BusinessEventPayloadDto<AmendDto<ItemDto>> {

    private UUID itemId;

    private UUID priceGroupId;

    private String priceGroupName;
    private BigDecimal price;


    @Builder
    public ItemBoundToPriceGroupPayloadDto(ItemDto previous, ItemDto current, UUID priceGroupId,
        String priceGroupName,
        BigDecimal price, UUID itemId) {
        super(new AmendDto<>(previous, current));
        this.itemId = itemId;
        this.priceGroupId = priceGroupId;
        this.priceGroupName = priceGroupName;
        this.price = price;
    }
}