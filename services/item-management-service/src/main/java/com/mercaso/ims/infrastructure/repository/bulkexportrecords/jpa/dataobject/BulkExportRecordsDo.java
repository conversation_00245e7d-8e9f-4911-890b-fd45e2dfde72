package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.dataobject;

import com.mercaso.ims.domain.bulkexportrecords.enums.SendStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;

import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.Type;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "bulk_export_records")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update bulk_export_records set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class BulkExportRecordsDo extends BaseDo {

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "search_time")
    private Instant searchTime;

    @Column(name = "send_email_time")
    private Instant sendEmailTime;

    @Column(name = "custom_filter")
    @Type(JsonType.class)
    private String customFilter;

    @Column(name = "send_status")
    @Enumerated(EnumType.STRING)
    private SendStatus sendStatus;

}