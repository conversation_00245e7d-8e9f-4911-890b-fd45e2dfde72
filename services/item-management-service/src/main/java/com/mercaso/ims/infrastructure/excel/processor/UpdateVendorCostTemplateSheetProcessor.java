package com.mercaso.ims.infrastructure.excel.processor;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.UpdateVendorItemCostData;
import com.mercaso.ims.infrastructure.excel.listener.UpdateVendorItemCostRequestDataListener;
import java.io.ByteArrayInputStream;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpdateVendorCostTemplateSheetProcessor {

    private final ItemRepository itemRepository;

    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    private final DocumentApplicationService documentApplicationService;
    private final VendorRepository vendorRepository;
    private final VendorItemRepository vendorItemRepository;
    private final CategoryApplicationService categoryApplicationService;
    private final FeatureFlagsManager featureFlagsManager;
    private final BrandRepository brandRepository;

    public void process(ItemAdjustmentRequestDto requestDto, String vendorName) {

        UUID requestId = requestDto.getId();
        String requestFile = requestDto.getRequestFile();
        byte[] document = documentApplicationService.downloadDocument(requestFile);
        try (ExcelReader excelReader = EasyExcelFactory.read(new ByteArrayInputStream(document)).build()) {

            ReadSheet vendorItemCostSheet = EasyExcelFactory.readSheet("Item Cost")
                .head(UpdateVendorItemCostData.class)
                .registerReadListener(new UpdateVendorItemCostRequestDataListener(requestId,
                    itemAdjustmentRequestDetailApplicationService,
                    itemAdjustmentRequestApplicationService,
                    itemRepository,
                    vendorRepository, vendorItemRepository, vendorName, categoryApplicationService, featureFlagsManager,
                        brandRepository))
                .build();
            excelReader.read(vendorItemCostSheet);
            itemAdjustmentRequestApplicationService.finishProcessed(requestId);
        }

    }
}
