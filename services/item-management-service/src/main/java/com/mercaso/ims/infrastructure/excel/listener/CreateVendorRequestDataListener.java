package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType.CREATE_VENDOR;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.application.service.VendorApplicationService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateVendorRequestData;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Getter
@Slf4j
public class CreateVendorRequestDataListener implements ReadListener<CreateVendorRequestData> {

    private final UUID itemAdjustmentRequestId;
    private final VendorApplicationService vendorApplicationService;
    private final VendorRepository vendorRepository;
    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;


    public CreateVendorRequestDataListener(UUID itemAdjustmentRequestId,
        VendorApplicationService vendorApplicationService,
        VendorRepository vendorRepository,
        ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService) {
        this.itemAdjustmentRequestId = itemAdjustmentRequestId;
        this.vendorApplicationService = vendorApplicationService;
        this.vendorRepository = vendorRepository;
        this.itemAdjustmentRequestDetailApplicationService = itemAdjustmentRequestDetailApplicationService;
    }


    @Override
    public void invoke(CreateVendorRequestData data, AnalysisContext analysisContext) {
        try {
            createItemAdjustmentRequestDetail(data);
        } catch (Exception e) {
            log.error("CreateVendorRequestDataListener error", e);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("All data is read successfully");
    }

    private void createItemAdjustmentRequestDetail(CreateVendorRequestData requestData) {

        CreateItemAdjustmentRequestDetailCommand command = convertToCreateItemAdjustmentRequestDetailCommand(requestData);
        ItemAdjustmentFailureReason failureReason = validateInput(requestData);
        if (failureReason != null) {
            command.setStatus(ItemAdjustmentStatus.VALIDATION_FAILURE);
            command.setFailureReason(failureReason.getReason());
            itemAdjustmentRequestDetailApplicationService.create(command);
        } else {
            vendorApplicationService.create(CreateVendorCommand.builder().vendorName(requestData.getVendorName()).build());
            command.setStatus(ItemAdjustmentStatus.IMS_UPDATED);
            itemAdjustmentRequestDetailApplicationService.create(command);

        }

    }

    private CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(CreateVendorRequestData requestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(CREATE_VENDOR)
            .sku("N/A")
            .vendor(requestData.getVendorName())
            .build();
    }

    private ItemAdjustmentFailureReason validateInput(CreateVendorRequestData requestData) {
        if (StringUtils.isNotBlank(requestData.getVendorName()) && requestData.getVendorName().length() > 255) {
            return ItemAdjustmentFailureReason.VENDOR_NAME_TOO_LONG;
        }
        Vendor vendor = vendorRepository.findByVendorName(requestData.getVendorName());
        if (vendor != null) {
            return ItemAdjustmentFailureReason.VENDOR_ALREADY_EXISTS;
        }
        return null;
    }


}
