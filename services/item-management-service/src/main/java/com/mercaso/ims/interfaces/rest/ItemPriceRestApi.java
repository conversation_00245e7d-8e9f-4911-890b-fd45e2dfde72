package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPriceResultDto;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/item-price", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class ItemPriceRestApi {

    private final ItemPriceApplicationService itemPriceApplicationService;


    @PutMapping("/batch-update-reg-price")
    @PreAuthorize("hasAuthority('ims:write:items')")
    public BatchUpdateItemPriceResultDto batchUpdateRegPrice(@RequestBody List<UpdateItemRegPriceCommand> command) {
        log.info("[batchUpdateRegPrice] param command: {}.", command);
        return itemPriceApplicationService.batchUpdateRegPrice(command);
    }


}
