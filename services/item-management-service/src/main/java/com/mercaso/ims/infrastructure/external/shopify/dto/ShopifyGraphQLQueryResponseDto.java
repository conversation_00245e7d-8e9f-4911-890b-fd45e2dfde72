package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyGraphQLQueryResponseDto {

    @JsonProperty("data")
    private DataDto data;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataDto {
        @JsonProperty("products")
        private ProductsDto products;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductsDto {
        @JsonProperty("edges")
        private List<ProductEdgeDto> edges;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductEdgeDto {
        @JsonProperty("node")
        private ProductDto node;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductDto {
        @JsonProperty("id")
        private String id;
        @JsonProperty("tags")
        private List<String> tags;
        @JsonProperty("variants")
        private VariantsDto variants;
        @JsonProperty("images")
        private ImagesDto images;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VariantsDto {
        @JsonProperty("edges")
        private List<VariantEdgeDto> edges;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VariantEdgeDto {
        @JsonProperty("node")
        private VariantDto node;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VariantDto {
        @JsonProperty("id")
        private String id;
        @JsonProperty("title")
        private String title;
        @JsonProperty("sku")
        private String sku;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImagesDto {
        @JsonProperty("edges")
        private List<ImageEdgeDto> edges;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImageEdgeDto {
        @JsonProperty("node")
        private ImageDto node;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImageDto {
        @JsonProperty("id")
        private String id;
    }
}
