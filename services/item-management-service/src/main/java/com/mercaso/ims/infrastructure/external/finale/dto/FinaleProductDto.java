package com.mercaso.ims.infrastructure.external.finale.dto;

import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinaleProductDto {

    private String productId;

    private String internalName;

    private String productTypeId;

    private StatusId statusId;

    private String productUrl;

    private String lastUpdatedDate;

    private String createdDate;

    private String weightUomId;

    private String caseDimensionUomId;

    private String distanceUomId;

    private String actionUrlDeactivate;

    private String quantityUomId;

    private List<FinaleSupplierItemDto> supplierList;


}
