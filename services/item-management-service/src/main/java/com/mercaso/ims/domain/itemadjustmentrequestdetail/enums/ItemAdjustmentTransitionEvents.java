package com.mercaso.ims.domain.itemadjustmentrequestdetail.enums;

import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;

public enum ItemAdjustmentTransitionEvents implements StateTransitionType {
    VALIDATION_FAILURE,
    IMS_UPDATED,
    IMS_UPDATED_FAILURE,
    SHOPIFY_SYNCHRONIZED,
    SHOPIFY_SYNCHRONIZED_FAILURE,
    SHOPIFY_SYNCHRONIZED_RECOVER,
    SHOPIFY_SYNCHRONIZED_DIRECTLY,
    UNKNOWN,
    ;


}