package com.mercaso.ims.infrastructure.repository.itemgrade.jpa.dataobject;

import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_grade")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_grade set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemGradeDo extends BaseDo {

    @Column(name = "item_id", nullable = false)
    private String itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "total_revenue")
    private BigDecimal totalRevenue;

    @Column(name = "pareto_grade")
    @Enumerated(EnumType.STRING)
    private ItemParetoGrade grade;

    @Column(name = "wos_1_week")
    private Long wos1Week;

    @Column(name = "wos_4_weeks")
    private Long wos4Weeks;
}
