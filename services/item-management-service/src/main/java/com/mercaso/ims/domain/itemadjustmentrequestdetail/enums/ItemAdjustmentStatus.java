package com.mercaso.ims.domain.itemadjustmentrequestdetail.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.mercaso.ims.infrastructure.statemachine.StateType;

import java.util.Arrays;

public enum ItemAdjustmentStatus implements StateType {
    VALIDATION_FAILURE,
    PENDING,
    IMS_UPDATED,
    IMS_UPDATED_FAILURE,
    PLYTIX_SYNCHRONIZED,
    PLYTIX_SYNCHRONIZED_FAILURE,
    SHOPIFY_SYNCHRONIZED,
    SHOPIFY_SYNCHRONIZED_FAILURE,
    UNKNOWN,
    ;

    @JsonCreator
    public static ItemAdjustmentStatus fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }
}
