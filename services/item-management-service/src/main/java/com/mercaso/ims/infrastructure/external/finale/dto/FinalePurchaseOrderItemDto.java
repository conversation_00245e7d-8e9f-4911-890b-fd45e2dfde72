package com.mercaso.ims.infrastructure.external.finale.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinalePurchaseOrderItemDto {

    private String orderItemId;
    private String productId;
    private String productUrl;
    private Long quantity;
    private String reserveUrl;
    private BigDecimal unitPrice;

}
