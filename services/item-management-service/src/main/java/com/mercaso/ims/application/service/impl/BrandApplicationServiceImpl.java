package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.application.service.BrandApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.BrandFactory;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class BrandApplicationServiceImpl implements BrandApplicationService {

    private final BrandService brandService;
    private final BrandDtoApplicationMapper brandDtoApplicationMapper;

    @Override
    public BrandDto createBrand(CreateBrandCommand command) {
        if (StringUtils.isBlank(command.getName())) {
            throw new ImsBusinessException(ErrorCodeEnums.INVALID_BRAND_NAME);
        }

        Brand existBrand = brandService.findByName(command.getName().trim());
        if (null != existBrand) {
            throw new ImsBusinessException(ErrorCodeEnums.BRAND_ALREADY_EXIST);
        }

        Brand brand = brandService.save(BrandFactory.create(command));

        return brandDtoApplicationMapper.domainToDto(brand);
    }
}
