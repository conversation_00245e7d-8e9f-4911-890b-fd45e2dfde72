package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.item.enums.AttributeType;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemAttributeDto extends BaseDto {

    private UUID attributeId;

    private String attributeName;

    private String value;

    private String unit;

    private AttributeType attributeType;

    private Integer sort;

    public String getAttributeInfo() {
        if (StringUtils.isNotBlank(unit)) {
            return attributeName + ":" + value + " " + unit;
        }
        return attributeName + ":" + value;
    }

}
