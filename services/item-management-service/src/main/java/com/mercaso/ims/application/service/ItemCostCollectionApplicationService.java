package com.mercaso.ims.application.service;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import java.util.UUID;

public interface ItemCostCollectionApplicationService {

    ItemCostCollectionDto create(CreateItemCostCollectionCommand command);

    void createJetroCostCollection(byte[] fileContent);

    DocumentResponse getItemCostCollectionFile(UUID id);

    void migrateFinale(int length);

}
