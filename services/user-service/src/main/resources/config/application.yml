server:
  port: 8080
  forward-headers-strategy: framework
spring:
  profiles:
    active: local
  application:
    name: user
  cloud:
    vault:
      enabled: true
      scheme: http
      port: 8200
      host: vault.vault.svc.cluster.local
      authentication: KUBERNETES
      kubernetes:
        role: ${spring.application.name}-service
        kubernetes-path: kubernetes
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
      # Application will fail if it cannot connect to vault, remember to disable vault for envs that don't need it
      fail-fast: true

      # Need to disable generic engine so that spring cloud knows to only pull secrets from KV engine
      generic:
        enabled: false
      kv:
        enabled: true
        backend: secret
        profile-separator: '/'
        application-name: ${spring.application.name}-service
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
security:
  public-paths:
    - /v1/oauth/token

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState
    shutdown:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
  health:
    vault:
      enabled: false
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  server:
    port: 8081
springdoc:
  swagger-ui:
    enabled: false

logging:
  level:
    org.springframework.web.filter.CommonsRequestLoggingFilter: DEBUG

auth0:
  mgmt:
    domain: ${auth0_mgmt_domain}
    client-id: ${auth0_mgmt_client_id}
    client-secret: ${auth0_mgmt_client_secret}
  m2m:
    mgmt:
      domain: ${auth0_mgmt_domain}
      client-id: ${auth0_m2m_mgmt_client_id}
      client-secret: ${auth0_m2m_mgmt_client_secret}
    audience: ${auth0_m2m_audience}
    custom-domain: ${auth0_m2m_custom_domain}
