package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchAssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkCompletePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkSplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.CancelPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.ReassignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.SplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand.UpdatePickingTaskItemDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PickingTaskResourceIT extends AbstractIT {

    @Autowired
    private PickingTaskResourceApi pickingTaskResourceApi;

    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Autowired
    private PickingTaskItemRepository pickingTaskItemRepository;

    @Test
    void when_assign_picking_task_then_picking_task_is_assigned() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        AssignPickingTaskCommand assignPickingTaskCommand = AssignPickingTaskCommand.builder()
            .pickerUserId(UUID.randomUUID())
            .pickerUserName(
                RandomStringUtils.randomAlphabetic(10))
            .build();

        // when
        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.assignPickingTask(savedPickingTasks.getFirst().getId(),
            assignPickingTaskCommand);

        // then
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDto.getStatus());
        assertEquals(assignPickingTaskCommand.getPickerUserId(), pickingTaskDto.getPickerUserId());
        assertEquals(assignPickingTaskCommand.getPickerUserName(), pickingTaskDto.getPickerUserName());
    }

    @Test
    void when_batch_assign_picking_tasks_then_picking_tasks_are_assigned() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2);
        PickingTask first = pickingTasks.getFirst();
        first.setPickerUserId(UUID.randomUUID());
        first.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
        first.setStatus(PickingTaskStatus.ASSIGNED);

        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        BatchAssignPickingTaskCommand batchAssignPickingTaskCommand = BatchAssignPickingTaskCommand.builder()
            .pickerUserId(UUID.randomUUID())
            .pickerUserName(
                RandomStringUtils.randomAlphabetic(10))
            .pickingTaskIds(List.of(savedPickingTasks.getFirst().getId(), savedPickingTasks.getLast().getId()))
            .build();

        // when
        List<PickingTaskDto> pickingTaskDtos = pickingTaskResourceApi.batchAssignPickingTasks(batchAssignPickingTaskCommand);

        // then
        assertNotNull(pickingTaskDtos);
        assertEquals(2, pickingTaskDtos.size());
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDtos.getFirst().getStatus());
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDtos.getLast().getStatus());
        assertEquals(batchAssignPickingTaskCommand.getPickerUserId(), pickingTaskDtos.getFirst().getPickerUserId());
        assertEquals(batchAssignPickingTaskCommand.getPickerUserName(), pickingTaskDtos.getFirst().getPickerUserName());
        assertEquals(batchAssignPickingTaskCommand.getPickerUserId(), pickingTaskDtos.getLast().getPickerUserId());
        assertEquals(batchAssignPickingTaskCommand.getPickerUserName(), pickingTaskDtos.getLast().getPickerUserName());
    }

    @Test
    void when_reassign_picking_task_then_picking_task_is_reassigned() throws JsonProcessingException {
        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        AssignPickingTaskCommand assignPickingTaskCommand = AssignPickingTaskCommand.builder()
            .pickerUserId(UUID.randomUUID())
            .pickerUserName(
                RandomStringUtils.randomAlphabetic(10))
            .build();
        pickingTaskResourceApi.assignPickingTask(savedPickingTasks.getFirst().getId(),
            assignPickingTaskCommand);

        ReassignPickingTaskCommand reassignPickingTaskCommand = ReassignPickingTaskCommand.builder()
            .pickerUserId(UUID.randomUUID())
            .pickerUserName(
                RandomStringUtils.randomAlphabetic(10))
            .build();

        // when
        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.reassignPickingTask(savedPickingTasks.getFirst().getId(),
            reassignPickingTaskCommand);

        // then
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDto.getStatus());
        assertEquals(reassignPickingTaskCommand.getPickerUserId(), pickingTaskDto.getPickerUserId());
        assertEquals(reassignPickingTaskCommand.getPickerUserName(), pickingTaskDto.getPickerUserName());
    }

    @Test
    void when_cancel_picking_task_then_picking_task_is_cancelled() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        // when
        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.cancelPickingTask(savedPickingTasks.getFirst().getId());

        // then
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.CANCELED, pickingTaskDto.getStatus());
    }

    @Test
    void when_complete_picking_task_then_picking_task_is_completed() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.FAILED, PickingTaskType.ORDER);
        pickingTasks.getFirst().getPickingTaskItems().forEach(pickingTaskItem -> {
            pickingTaskItem.setPickedQty(pickingTaskItem.getExpectQty());
        });
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        // when
        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.completePickingTask(savedPickingTasks.getFirst().getId());

        // then
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.PARTIALLY_COMPLETED, pickingTaskDto.getStatus());
    }

    @Test
    void when_bulk_complete_picking_task_then_picking_task_is_completed() throws JsonProcessingException {
        pickingTaskRepository.deleteAll();
        // given
        Batch savedBatch = batchRepository.save(buildBatch(UUID.randomUUID()));
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.FAILED,
            PickingTaskType.ORDER);
        pickingTasks.forEach(pickingTask -> {
            pickingTask.setBatchId(savedBatch.getId());
            pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> {
                pickingTaskItem.setPickedQty(pickingTaskItem.getExpectQty());
            });
        });
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        // when
        BulkCompletePickingTaskCommand command = new BulkCompletePickingTaskCommand();
        command.setPickingTaskIds(List.of(savedPickingTasks.getFirst().getId(), savedPickingTasks.getLast().getId()));
        pickingTaskResourceApi.bulkCompletePickingTask(command);

        // then
        PickingTaskDto pickingTaskDtoFirst = pickingTaskResourceApi.getPickingTask(savedPickingTasks.getFirst().getId());
        assertEquals(PickingTaskStatus.PARTIALLY_COMPLETED, pickingTaskDtoFirst.getStatus());

        PickingTaskDto pickingTaskDtoLast = pickingTaskResourceApi.getPickingTask(savedPickingTasks.getLast().getId());
        assertEquals(PickingTaskStatus.PARTIALLY_COMPLETED, pickingTaskDtoLast.getStatus());
    }

    @Test
    void when_start_picking_task_then_picking_task_is_started() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        // given
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.ASSIGNED, PickingTaskType.ORDER);
        pickingTasks.forEach(pickingTask -> {
            pickingTask.setPickerUserId(UUID.randomUUID());
            pickingTask.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
        });
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        // when
        try {
            pickingTaskResourceApi.startPickingTask(savedPickingTasks.getFirst().getId());
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("WMS0001"));
        }
    }

    @Test
    void when_update_picking_task_then_picking_task_is_updated() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.PICKING, PickingTaskType.ORDER);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        List<UpdatePickingTaskItemDto> updatePickingTaskItemDtos = getUpdatePickingTaskItemDtos(
            savedPickingTasks);

        UpdatePickingTaskCommand updatePickingTaskCommand = new UpdatePickingTaskCommand();
        updatePickingTaskCommand.setUpdatePickingTaskItemDtos(updatePickingTaskItemDtos);

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.updatePickingTask(savedPickingTasks.getFirst().getId(),
            updatePickingTaskCommand);

        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.PICKED, pickingTaskDto.getStatus());
    }

    @Test
    void when_update_picking_task_has_error_then_picking_task_is_failed() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.PICKING, PickingTaskType.ORDER);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        List<UpdatePickingTaskItemDto> updatePickingTaskItemDtos = getUpdatePickingTaskItemDtos(
            savedPickingTasks);

        UpdatePickingTaskItemDto first = updatePickingTaskItemDtos.getFirst();
        first.setPickedQty(0);
        first.setErrorInfo("Error");

        UpdatePickingTaskCommand updatePickingTaskCommand = new UpdatePickingTaskCommand();
        updatePickingTaskCommand.setUpdatePickingTaskItemDtos(updatePickingTaskItemDtos);

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.updatePickingTask(savedPickingTasks.getFirst().getId(),
            updatePickingTaskCommand);

        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.FAILED, pickingTaskDto.getStatus());
    }

    @Test
    void when_update_picking_task_all_items_error_then_picking_task_is_failed() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.PICKING, PickingTaskType.ORDER);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        List<UpdatePickingTaskItemDto> updatePickingTaskItemDtos = getUpdatePickingTaskItemDtos(
            savedPickingTasks);

        UpdatePickingTaskItemDto first = updatePickingTaskItemDtos.getFirst();
        first.setPickedQty(0);
        first.setErrorInfo("Error");

        UpdatePickingTaskItemDto last = updatePickingTaskItemDtos.getLast();
        last.setPickedQty(0);
        last.setErrorInfo("Error");

        UpdatePickingTaskCommand updatePickingTaskCommand = new UpdatePickingTaskCommand();
        updatePickingTaskCommand.setUpdatePickingTaskItemDtos(updatePickingTaskItemDtos);

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.updatePickingTask(savedPickingTasks.getFirst().getId(),
            updatePickingTaskCommand);

        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.FAILED, pickingTaskDto.getStatus());
    }

    @Test
    void when_update_picking_task_without_error_then_picking_task_is_back_to_picking() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.FAILED, PickingTaskType.ORDER);
        pickingTasks.getFirst().getPickingTaskItems().forEach(pickingTaskItem -> {
            pickingTaskItem.setPickedQty(0);
            pickingTaskItem.setErrorInfo("Error");
        });
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        List<UpdatePickingTaskItemDto> updatePickingTaskItemDtos = getUpdatePickingTaskItemDtos(
            savedPickingTasks);

        UpdatePickingTaskItemDto first = updatePickingTaskItemDtos.getFirst();
        first.setPickedQty(1);
        first.setErrorInfo(null);

        UpdatePickingTaskCommand updatePickingTaskCommand = new UpdatePickingTaskCommand();
        updatePickingTaskCommand.setUpdatePickingTaskItemDtos(updatePickingTaskItemDtos);

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.updatePickingTask(savedPickingTasks.getFirst().getId(),
            updatePickingTaskCommand);

        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.PICKING, pickingTaskDto.getStatus());
    }

    @Test
    void when_split_picking_task_then_picking_task_is_split() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        pickingTaskItemRepository.deleteAll();
        UUID batchId = UUID.randomUUID();

        BatchItem savedBatchItem = batchItemRepository.save(BatchItem.builder()
            .expectQty(10)
            .skuNumber("test")
            .orderNumber(RandomStringUtils.randomAlphabetic(8))
            .batchId(batchId)
            .build());

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.FAILED, PickingTaskType.ORDER);
        pickingTasks.getFirst().getPickingTaskItems().forEach(pickingTaskItem -> {
            pickingTaskItem.setPickedQty(1);
            pickingTaskItem.setErrorInfo("Test Split");
            pickingTaskItem.setBatchItemId(savedBatchItem.getId());
        });

        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        SplitPickingTaskCommand splitPickingTaskCommand = new SplitPickingTaskCommand();
        splitPickingTaskCommand.setSplitItemIds(List.of(savedPickingTasks.getFirst().getPickingTaskItems().getFirst().getId()));
        splitPickingTaskCommand.setPickerUserId(UUID.randomUUID());
        splitPickingTaskCommand.setPickerUserName("Leon Liu");
        splitPickingTaskCommand.setWarehouseName("DOWNEY");

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.splitPickingTask(savedPickingTasks.getFirst().getId(),
            splitPickingTaskCommand);
        PickingTask originalPickingTask = pickingTaskRepository.findById(savedPickingTasks.getFirst().getId());
        assertNotNull(pickingTaskDto);
        assertNotNull(originalPickingTask);
        assertTrue(originalPickingTask.getPickingTaskItems()
            .stream()
            .anyMatch(pickingTaskItem -> pickingTaskItem.getExpectQty() == 1));
        assertTrue(originalPickingTask.getPickingTaskItems()
            .stream()
            .anyMatch(pickingTaskItem -> pickingTaskItem.getPickedQty() == 1));
        assertEquals(9, pickingTaskDto.getPickingTaskItems().getFirst().getExpectQty());
        assertEquals(0, pickingTaskDto.getPickingTaskItems().getFirst().getPickedQty());
        assertEquals("DOWNEY", pickingTaskDto.getSource());
        assertEquals("Leon Liu", pickingTaskDto.getPickerUserName());
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDto.getStatus());
    }

    @Test
    void when_batch_cancel_picking_task_then_picking_task_is_cancelled() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1, SourceEnum.MFC, PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        List<UUID> pickingTaskIds = List.of(savedPickingTasks.getFirst().getId());
        CancelPickingTaskCommand cancelPickingTaskCommand = CancelPickingTaskCommand.builder().build();
        cancelPickingTaskCommand.setPickingTaskIds(pickingTaskIds);

        pickingTaskResourceApi.batchCancelPickingTask(cancelPickingTaskCommand);

        PickingTask pickingTask = pickingTaskRepository.findById(pickingTaskIds.getFirst());

        assertNotNull(pickingTask);
        assertEquals(PickingTaskStatus.CANCELED, pickingTask.getStatus());
    }

    private List<UpdatePickingTaskItemDto> getUpdatePickingTaskItemDtos(List<PickingTask> savedPickingTasks) {
        List<UpdatePickingTaskItemDto> updatePickingTaskItemDtos = new ArrayList<>();
        for (PickingTaskItem pickingTaskItem : savedPickingTasks.getFirst().getPickingTaskItems()) {
            UpdatePickingTaskItemDto updatePickingTaskItemDto = new UpdatePickingTaskItemDto();
            updatePickingTaskItemDto.setId(pickingTaskItem.getId());
            updatePickingTaskItemDto.setPickedQty(pickingTaskItem.getExpectQty());
            updatePickingTaskItemDtos.add(updatePickingTaskItemDto);
        }
        return updatePickingTaskItemDtos;
    }

    @Test
    void when_bulk_split_picking_task_then_split_with_2_items() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        pickingTaskItemRepository.deleteAll();
        UUID batchId = UUID.randomUUID();

        BatchItem savedBatchItem = batchItemRepository.save(BatchItem.builder()
            .expectQty(10)
            .skuNumber("test")
            .orderNumber(RandomStringUtils.randomAlphabetic(8))
            .batchId(batchId)
            .build());

        List<PickingTask> pickingTasks = buildPickingTask(batchId,
            1,
            SourceEnum.MFC,
            PickingTaskStatus.FAILED,
            PickingTaskType.ORDER);
        PickingTaskItem pickingTaskItem = pickingTasks.getFirst().getPickingTaskItems().getFirst();
        pickingTaskItem.setPickedQty(1);
        pickingTaskItem.setErrorInfo("Test Split");
        pickingTaskItem.setBatchItemId(savedBatchItem.getId());

        PickingTaskItem pickingTaskItem1 = pickingTasks.getFirst().getPickingTaskItems().get(1);
        pickingTaskItem1.setPickedQty(pickingTaskItem1.getExpectQty());
        pickingTaskItem1.setBatchItemId(savedBatchItem.getId());

        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        BulkSplitPickingTaskCommand splitPickingTaskCommand = new BulkSplitPickingTaskCommand();
        splitPickingTaskCommand.setPickingTaskItemIds(List.of(savedPickingTasks.getFirst()
            .getPickingTaskItems()
            .getFirst()
            .getId()));
        splitPickingTaskCommand.setPickerUserId(UUID.randomUUID());
        splitPickingTaskCommand.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
        splitPickingTaskCommand.setWarehouseName("DOWNEY");

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.bulkSplitPickingTask(splitPickingTaskCommand);
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDto.getStatus());
        assertEquals(1, pickingTaskDto.getPickingTaskItems().size());

        PickingTask originalPickingTask = pickingTaskRepository.findById(savedPickingTasks.getFirst().getId());
        assertNotNull(originalPickingTask);
        assertEquals(PickingTaskStatus.COMPLETED, originalPickingTask.getStatus());
        assertEquals(2, originalPickingTask.getPickingTaskItems().size());
    }

    @Test
    void when_bulk_split_picking_task_then_split_out_picking_task_item() throws JsonProcessingException {

        pickingTaskRepository.deleteAll();
        pickingTaskItemRepository.deleteAll();
        UUID batchId = UUID.randomUUID();

        BatchItem savedBatchItem = batchItemRepository.save(BatchItem.builder()
            .expectQty(10)
            .skuNumber("test")
            .orderNumber(RandomStringUtils.randomAlphabetic(8))
            .batchId(batchId)
            .build());

        List<PickingTask> pickingTasks = buildPickingTask(batchId,
            1,
            SourceEnum.MFC,
            PickingTaskStatus.FAILED,
            PickingTaskType.ORDER);
        pickingTasks.getFirst().getPickingTaskItems().removeFirst();
        PickingTaskItem pickingTaskItem = pickingTasks.getFirst().getPickingTaskItems().getFirst();
        pickingTaskItem.setPickedQty(1);
        pickingTaskItem.setErrorInfo("Test Split");
        pickingTaskItem.setBatchItemId(savedBatchItem.getId());

        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);

        BulkSplitPickingTaskCommand splitPickingTaskCommand = new BulkSplitPickingTaskCommand();
        splitPickingTaskCommand.setPickingTaskItemIds(List.of(savedPickingTasks.getFirst()
            .getPickingTaskItems()
            .getFirst()
            .getId()));
        splitPickingTaskCommand.setPickerUserId(UUID.randomUUID());
        splitPickingTaskCommand.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
        splitPickingTaskCommand.setWarehouseName("DOWNEY");

        PickingTaskDto pickingTaskDto = pickingTaskResourceApi.bulkSplitPickingTask(splitPickingTaskCommand);
        assertNotNull(pickingTaskDto);
        assertEquals(PickingTaskStatus.ASSIGNED, pickingTaskDto.getStatus());
        assertEquals(1, pickingTaskDto.getPickingTaskItems().size());
        assertEquals(9, pickingTaskDto.getPickingTaskItems().getFirst().getExpectQty());
        assertEquals(0, pickingTaskDto.getPickingTaskItems().getFirst().getPickedQty());

        PickingTask originalPickingTask = pickingTaskRepository.findById(savedPickingTasks.getFirst().getId());
        assertNotNull(originalPickingTask);
        assertEquals(PickingTaskStatus.COMPLETED, originalPickingTask.getStatus());
        assertEquals(1, originalPickingTask.getPickingTaskItems().size());
        assertEquals(1, originalPickingTask.getPickingTaskItems().getFirst().getPickedQty());
        assertEquals(1, originalPickingTask.getPickingTaskItems().getFirst().getExpectQty());
    }

    @Test
    void when_batch_unassign_picking_tasks_then_status_is_created_and_picker_is_null() throws Exception {
        pickingTaskRepository.deleteAll();

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2);
        for (PickingTask task : pickingTasks) {
            task.setPickerUserId(UUID.randomUUID());
            task.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
            task.setStatus(PickingTaskStatus.ASSIGNED);
        }
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);
        List<UUID> pickingTaskIds = savedPickingTasks.stream().map(PickingTask::getId).toList();

        List<PickingTaskDto> result = pickingTaskResourceApi.batchUnassignPickingTasks(pickingTaskIds);

        assertNotNull(result);
        assertEquals(2, result.size());
        for (PickingTaskDto dto : result) {
            assertEquals(PickingTaskStatus.CREATED, dto.getStatus());
            assertNull(dto.getPickerUserId());
            assertNull(dto.getPickerUserName());
        }
    }

    @Test
    void when_batch_unassign_with_some_assigned_some_not_then_only_assigned_are_unassigned() throws Exception {
        pickingTaskRepository.deleteAll();
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2);
        PickingTask assigned = pickingTasks.getFirst();
        assigned.setPickerUserId(UUID.randomUUID());
        assigned.setPickerUserName(RandomStringUtils.randomAlphabetic(10));
        assigned.setStatus(PickingTaskStatus.ASSIGNED);

        PickingTask notAssigned = pickingTasks.getLast();
        notAssigned.setStatus(PickingTaskStatus.CREATED);

        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);
        List<UUID> pickingTaskIds = savedPickingTasks.stream().map(PickingTask::getId).toList();

        List<PickingTaskDto> result = pickingTaskResourceApi.batchUnassignPickingTasks(pickingTaskIds);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(PickingTaskStatus.CREATED, result.getFirst().getStatus());
        assertNull(result.getFirst().getPickerUserId());
        assertNull(result.getFirst().getPickerUserName());
    }

    @Test
    void when_batch_unassign_with_no_assigned_tasks_then_return_empty() throws Exception {
        pickingTaskRepository.deleteAll();
        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2);
        for (PickingTask task : pickingTasks) {
            task.setStatus(PickingTaskStatus.CREATED);
        }
        List<PickingTask> savedPickingTasks = pickingTaskRepository.saveAll(pickingTasks);
        List<UUID> pickingTaskIds = savedPickingTasks.stream().map(PickingTask::getId).toList();

        List<PickingTaskDto> result = pickingTaskResourceApi.batchUnassignPickingTasks(pickingTaskIds);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void when_batch_unassign_with_nonexistent_id_then_throw() {
        List<UUID> ids = List.of(UUID.randomUUID());
        Exception exception = assertThrows(Exception.class, () -> pickingTaskResourceApi.batchUnassignPickingTasks(ids));
        assertTrue(exception.getMessage().contains("Picking tasks not found"));
    }

}