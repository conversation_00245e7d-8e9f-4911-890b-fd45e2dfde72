package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.application.dto.customer.CustomerDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.slack.BuildDeliveryTaskExceptionEventDto;
import com.mercaso.wms.delivery.application.dto.slack.SlackDeliveryEventDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.infrastructure.external.slack.SlackAdaptor;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class SlackDeliveryNotificationServiceTest {

    @Mock
    private SlackAdaptor slackAdaptor;

    @Mock
    private DeliveryTaskRepository deliveryTaskRepository;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @InjectMocks
    private SlackDeliveryNotificationService slackDeliveryNotificationService;

    private static final String DELIVERY_EXTERNAL_URL = "https://delivery.example.com";
    private static final String WEBHOOK_URL = "https://hooks.slack.com/sample/webhook";
    private static final String ORDER_ISSUES_WEBHOOK_URL = "https://hooks.slack.com/sample/order-issues";
    private static final String TASK_BUILD_EXCEPTION_WEBHOOK_URL = "https://hooks.slack.com/sample/task-build-exception";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(slackDeliveryNotificationService, "deliveryPortalBaseUrl", DELIVERY_EXTERNAL_URL);
        ReflectionTestUtils.setField(slackDeliveryNotificationService, "webhookUrl", WEBHOOK_URL);
        ReflectionTestUtils.setField(slackDeliveryNotificationService, "orderIssuesWebhookUrl", ORDER_ISSUES_WEBHOOK_URL);
        ReflectionTestUtils.setField(slackDeliveryNotificationService,
            "taskBuildExceptionWebhookUrl",
            TASK_BUILD_EXCEPTION_WEBHOOK_URL);
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.TRIGGER_DELIVERY_DETAILS_WORKFLOW)).thenReturn(true);
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_valid_order_and_driver_then_send_notification() {
        // Arrange
        UUID deliveryTaskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        DeliveryOrderDto orderDto = buildDeliveryOrderDto(orderId, deliveryTaskId);

        DeliveryTask task = DeliveryTask.builder()
            .id(deliveryTaskId)
            .driverUserId(driverUserId)
            .build();

        Account driverAccount = Account.builder()
            .id(UUID.randomUUID())
            .userId(driverUserId)
            .userName("John Doe")
            .email("<EMAIL>")
            .build();

        when(deliveryTaskRepository.findById(deliveryTaskId)).thenReturn(task);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(driverAccount));

        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);

        // Assert
        ArgumentCaptor<SlackDeliveryEventDto> eventCaptor = ArgumentCaptor.forClass(SlackDeliveryEventDto.class);
        verify(slackAdaptor, times(1)).notifyDeliveryCompletion(eventCaptor.capture());

        SlackDeliveryEventDto capturedEvent = eventCaptor.getValue();
        assertEquals(orderDto.getOrderNumber(), capturedEvent.getOrderNumber());
        assertEquals(PaymentType.CREDIT.name(), capturedEvent.getPaymentMethod());
        assertEquals("Jane Smith", capturedEvent.getCustomerName());
        assertEquals("John Doe", capturedEvent.getDriverName());
        assertEquals("<EMAIL>", capturedEvent.getDriverUserEmail());
        assertEquals(DELIVERY_EXTERNAL_URL + "/delivery-orders/" + orderId, capturedEvent.getOrderDetailUrl());
        assertEquals(DELIVERY_EXTERNAL_URL + "/drivers?page=1&pageSize=20&userName=John+Doe", capturedEvent.getDriverDetail());
        assertEquals(WEBHOOK_URL, capturedEvent.getWebhookUrl());
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_null_order_then_do_not_send_notification() {
        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(null);

        // Assert
        verify(slackAdaptor, never()).notifyDeliveryCompletion(any());
        verify(deliveryTaskRepository, never()).findById(any());
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_feature_flag_disabled_then_do_not_send_notification() {
        // Arrange
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.TRIGGER_DELIVERY_DETAILS_WORKFLOW)).thenReturn(false);
        DeliveryOrderDto orderDto = buildDeliveryOrderDto(UUID.randomUUID(), UUID.randomUUID());

        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);

        // Assert
        verify(slackAdaptor, never()).notifyDeliveryCompletion(any());
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_driver_not_found_then_send_notification_without_driver_details() {
        // Arrange
        UUID deliveryTaskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        DeliveryOrderDto orderDto = buildDeliveryOrderDto(orderId, deliveryTaskId);

        DeliveryTask task = DeliveryTask.builder()
            .id(deliveryTaskId)
            .driverUserId(driverUserId)
            .build();

        when(deliveryTaskRepository.findById(deliveryTaskId)).thenReturn(task);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.empty());

        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);

        // Assert
        ArgumentCaptor<SlackDeliveryEventDto> eventCaptor = ArgumentCaptor.forClass(SlackDeliveryEventDto.class);
        verify(slackAdaptor, times(1)).notifyDeliveryCompletion(eventCaptor.capture());

        SlackDeliveryEventDto capturedEvent = eventCaptor.getValue();
        assertEquals(orderDto.getOrderNumber(), capturedEvent.getOrderNumber());
        assertNull(capturedEvent.getDriverName());
        assertNull(capturedEvent.getDriverUserEmail());
        assertNull(capturedEvent.getDriverDetail());
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_exception_then_log_error_and_continue() {
        // Arrange
        UUID deliveryTaskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();

        DeliveryOrderDto orderDto = buildDeliveryOrderDto(UUID.randomUUID(), deliveryTaskId);

        DeliveryTask task = DeliveryTask.builder()
            .id(deliveryTaskId)
            .driverUserId(driverUserId)
            .build();

        when(deliveryTaskRepository.findById(deliveryTaskId)).thenReturn(task);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.empty());
        doThrow(new RuntimeException("Test exception")).when(slackAdaptor).notifyDeliveryCompletion(any());

        // Act & Assert (no exception should propagate)
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);
    }

    @Test
    void when_batchNotifyBuildTaskException_with_valid_events_then_send_notifications_simple() {
        // Arrange
        UUID previousTaskId1 = UUID.randomUUID();
        UUID currentTaskId1 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();

        BuildDeliveryTaskExceptionEventDto event1 = BuildDeliveryTaskExceptionEventDto.builder()
            .previousTaskId(previousTaskId1)
            .currentTaskId(currentTaskId1)
            .orderId(orderId1)
            .orderNumber("ORD-123456")
            .build();

        List<BuildDeliveryTaskExceptionEventDto> events = List.of(event1);

        // Create a real DeliveryTask object using builder
        DeliveryTask task1 = DeliveryTask.builder()
            .id(previousTaskId1)
            .number("DT-*************")
            .build();

        // Mock repository to return our task
        when(deliveryTaskRepository.findByIdIn(any(Collection.class)))
            .thenReturn(List.of(task1));

        // Act - Call the method directly (it's public)
        slackDeliveryNotificationService.batchNotifyBuildTaskException(events);

        // Assert - Verify the SlackAdaptor was called
        ArgumentCaptor<BuildDeliveryTaskExceptionEventDto> eventCaptor =
            ArgumentCaptor.forClass(BuildDeliveryTaskExceptionEventDto.class);
        verify(slackAdaptor, times(1)).batchNotifyBuildTaskException(eventCaptor.capture());

        BuildDeliveryTaskExceptionEventDto capturedEvent = eventCaptor.getValue();

        // Verify event properties
        assertEquals(previousTaskId1, capturedEvent.getPreviousTaskId());
        assertEquals("DT-*************", capturedEvent.getPreviousTaskNumber());
        assertEquals(currentTaskId1, capturedEvent.getCurrentTaskId());
        assertEquals(orderId1, capturedEvent.getOrderId());
        assertEquals(TASK_BUILD_EXCEPTION_WEBHOOK_URL, capturedEvent.getWebhookUrl());
        assertEquals(DELIVERY_EXTERNAL_URL + "/delivery-orders/" + orderId1, capturedEvent.getOrderDetailUrl());
        assertEquals(DELIVERY_EXTERNAL_URL + "/delivery-tasks/" + currentTaskId1, capturedEvent.getTaskDetailUrl());
        assertEquals(DELIVERY_EXTERNAL_URL + "/delivery-tasks/" + previousTaskId1, capturedEvent.getPreviousTaskDetailUrl());
    }

    @Test
    void when_batchNotifyBuildTaskException_with_task_not_found_then_log_warning() {
        // Arrange
        UUID previousTaskId = UUID.randomUUID();
        UUID currentTaskId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        BuildDeliveryTaskExceptionEventDto event = BuildDeliveryTaskExceptionEventDto.builder()
            .previousTaskId(previousTaskId)
            .currentTaskId(currentTaskId)
            .orderId(orderId)
            .orderNumber("ORD-123456")
            .build();

        List<BuildDeliveryTaskExceptionEventDto> events = List.of(event);

        // Task not found in repository
        when(deliveryTaskRepository.findByIdIn(anyList()))
            .thenReturn(Collections.emptyList());

        // Act
        slackDeliveryNotificationService.batchNotifyBuildTaskException(events);

        // Assert
        ArgumentCaptor<BuildDeliveryTaskExceptionEventDto> eventCaptor =
            ArgumentCaptor.forClass(BuildDeliveryTaskExceptionEventDto.class);
        verify(slackAdaptor, times(1)).batchNotifyBuildTaskException(eventCaptor.capture());

        BuildDeliveryTaskExceptionEventDto capturedEvent = eventCaptor.getValue();
        assertEquals(previousTaskId, capturedEvent.getPreviousTaskId());
        assertNull(capturedEvent.getPreviousTaskNumber()); // Task number should be null as task was not found
        assertEquals(currentTaskId, capturedEvent.getCurrentTaskId());
        assertEquals(orderId, capturedEvent.getOrderId());
        assertEquals(TASK_BUILD_EXCEPTION_WEBHOOK_URL, capturedEvent.getWebhookUrl());
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_order_issues_then_use_issues_webhook() {
        // Arrange
        UUID deliveryTaskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        DeliveryOrderDto orderDto = buildDeliveryOrderDto(orderId, deliveryTaskId);
        // Add notes to trigger the order issues webhook
        orderDto.setNotes("Customer requested special handling");

        DeliveryTask task = DeliveryTask.builder()
            .id(deliveryTaskId)
            .driverUserId(driverUserId)
            .build();

        when(deliveryTaskRepository.findById(deliveryTaskId)).thenReturn(task);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.empty());

        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);

        // Assert
        ArgumentCaptor<SlackDeliveryEventDto> eventCaptor = ArgumentCaptor.forClass(SlackDeliveryEventDto.class);
        verify(slackAdaptor, times(1)).notifyDeliveryCompletion(eventCaptor.capture());

        SlackDeliveryEventDto capturedEvent = eventCaptor.getValue();
        assertEquals(ORDER_ISSUES_WEBHOOK_URL, capturedEvent.getWebhookUrl());
    }

    @Test
    void when_formatPaymentTypes_with_multiple_payment_types_then_return_comma_separated_string() throws Exception {
        // Arrange
        List<PaymentType> paymentTypes = Arrays.asList(
            PaymentType.CREDIT,
            PaymentType.CASH,
            PaymentType.CHECK
        );

        // Use reflection to access private method
        Method formatPaymentTypesMethod = SlackDeliveryNotificationService.class
            .getDeclaredMethod("formatPaymentTypes", List.class);
        formatPaymentTypesMethod.setAccessible(true);

        // Act
        String result = (String) formatPaymentTypesMethod.invoke(slackDeliveryNotificationService, paymentTypes);

        // Assert
        assertEquals("CREDIT,CASH,CHECK", result);
    }

    @Test
    void when_formatPaymentTypes_with_single_payment_type_then_return_string() throws Exception {
        // Arrange
        List<PaymentType> paymentTypes = Collections.singletonList(PaymentType.CREDIT);

        // Use reflection to access private method
        Method formatPaymentTypesMethod = SlackDeliveryNotificationService.class
            .getDeclaredMethod("formatPaymentTypes", List.class);
        formatPaymentTypesMethod.setAccessible(true);

        // Act
        String result = (String) formatPaymentTypesMethod.invoke(slackDeliveryNotificationService, paymentTypes);

        // Assert
        assertEquals("CREDIT", result);
    }

    @Test
    void when_formatPaymentTypes_with_empty_list_then_return_null() throws Exception {
        // Arrange
        List<PaymentType> paymentTypes = Collections.emptyList();

        // Use reflection to access private method
        Method formatPaymentTypesMethod = SlackDeliveryNotificationService.class
            .getDeclaredMethod("formatPaymentTypes", List.class);
        formatPaymentTypesMethod.setAccessible(true);

        // Act
        String result = (String) formatPaymentTypesMethod.invoke(slackDeliveryNotificationService, paymentTypes);

        // Assert
        assertNull(result);
    }

    @Test
    void when_formatPaymentTypes_with_null_then_return_null() throws Exception {
        // Use reflection to access private method
        Method formatPaymentTypesMethod = SlackDeliveryNotificationService.class
            .getDeclaredMethod("formatPaymentTypes", List.class);
        formatPaymentTypesMethod.setAccessible(true);

        // Act
        String result = (String) formatPaymentTypesMethod.invoke(slackDeliveryNotificationService, (Object) null);

        // Assert
        assertNull(result);
    }

    @Test
    void when_sendDeliveryCompletedNotification_with_item_issues_then_categorize_correctly() {
        // Arrange
        UUID deliveryTaskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        DeliveryOrderItemDto item1 = new DeliveryOrderItemDto();
        item1.setLine(1);
        item1.setReasonCode("{\"MISSING\":1}");

        DeliveryOrderItemDto item2 = new DeliveryOrderItemDto();
        item2.setLine(2);
        item2.setReasonCode("{\"RETURNS\":1}");

        DeliveryOrderItemDto item3 = new DeliveryOrderItemDto();
        item3.setLine(3);
        item3.setReasonCode("{\"UNPLANNED\":2}");

        DeliveryOrderItemDto item4 = new DeliveryOrderItemDto();
        item4.setLine(4);
        item4.setReasonCode("{\"DAMAGED\":1}");

        DeliveryOrderItemDto item5 = new DeliveryOrderItemDto();
        item5.setLine(5);
        item5.setReasonCode("{\"EXPIRED\":1}");

        CustomerDto customerDto = new CustomerDto();
        customerDto.setFirstName("Jane");
        customerDto.setLastName("Smith");

        DeliveryOrderDto orderDto = DeliveryOrderDto.builder()
            .id(orderId)
            .deliveryTaskId(deliveryTaskId)
            .orderNumber("ORD-123456")
            .paymentType(Collections.singletonList(PaymentType.CREDIT))
            .customer(customerDto)
            .totalPrice(new BigDecimal("99.99"))
            .notes("Customer has item issues")
            .deliveryOrderItems(Arrays.asList(item1, item2, item3, item4, item5))
            .build();

        DeliveryTask task = DeliveryTask.builder()
            .id(deliveryTaskId)
            .driverUserId(driverUserId)
            .build();

        Account driverAccount = Account.builder()
            .id(UUID.randomUUID())
            .userId(driverUserId)
            .userName("John Doe")
            .email("<EMAIL>")
            .build();

        when(deliveryTaskRepository.findById(deliveryTaskId)).thenReturn(task);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(driverAccount));

        // Act
        slackDeliveryNotificationService.sendDeliveryCompletedNotification(orderDto);

        // Assert
        ArgumentCaptor<SlackDeliveryEventDto> eventCaptor = ArgumentCaptor.forClass(SlackDeliveryEventDto.class);
        verify(slackAdaptor, times(1)).notifyDeliveryCompletion(eventCaptor.capture());

        SlackDeliveryEventDto capturedEvent = eventCaptor.getValue();
        assertEquals(ORDER_ISSUES_WEBHOOK_URL, capturedEvent.getWebhookUrl());
        assertEquals("ORD-123456", capturedEvent.getOrderNumber());
        assertEquals("John Doe", capturedEvent.getDriverName());
    }

    private DeliveryOrderDto buildDeliveryOrderDto(UUID id, UUID deliveryTaskId) {
        CustomerDto customerDto = new CustomerDto();
        customerDto.setFirstName("Jane");
        customerDto.setLastName("Smith");

        return DeliveryOrderDto.builder()
            .id(id)
            .deliveryTaskId(deliveryTaskId)
            .orderNumber("ORD-123456")
            .paymentType(Collections.singletonList(PaymentType.CREDIT))
            .customer(customerDto)
            .totalPrice(new BigDecimal("99.99"))
            .notes(null) // Set to null to avoid triggering order issues webhook
            .deliveryOrderItems(Collections.emptyList())
            .build();
    }
} 