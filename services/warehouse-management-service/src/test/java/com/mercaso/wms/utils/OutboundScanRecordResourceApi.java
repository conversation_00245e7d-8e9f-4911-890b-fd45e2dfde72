package com.mercaso.wms.utils;

import com.mercaso.wms.application.command.scanrecord.CreateOutboundScanRecordCommand;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class OutboundScanRecordResourceApi extends IntegrationTestRestUtil {

    public OutboundScanRecordResourceApi(Environment environment) {
        super(environment);
    }

    private static final String CREATE_URL = "/outbound-scan-records";

    public OutboundScanRecordDto create(CreateOutboundScanRecordCommand command) throws Exception {
        return createEntity(CREATE_URL, SerializationUtils.serialize(command), OutboundScanRecordDto.class);
    }
}
