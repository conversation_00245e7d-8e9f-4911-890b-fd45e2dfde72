package com.mercaso.wms.delivery.interfaces.query;

import static com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus.IN_TRANSIT;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.command.GpsRawCommand.GpsReportDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import com.mercaso.wms.delivery.utils.GpsRawResourceApi;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryDeliveryOrderResourceIT extends AbstractIT {

    @Autowired
    private DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    private DeliveryOrderWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    private GpsRawResourceApi gpsRawResourceApi;
    @Autowired
    private DeliveryTaskRepository deliveryTaskRepository;

    @Test
    void whenFindById_thenReturnsDeliveryOrder() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        DeliveryOrderDto deliveryOrder = deliveryOrderResourceApi.findById(deliveryOrderDo.getId());

        // then
        assertNotNull(deliveryOrder);
        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertTrue(deliveryOrder.getShopifyOrderUrl().contains(shopifyOrderDto.getId()));
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
    }

    @Test
    void whenFindByOrderNumber_thenReturnsDeliveryOrder() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        DeliveryTask deliveryTask = DeliveryTask.builder()
            .driverUserId(UUID.randomUUID())
            .status(DeliveryTaskStatus.IN_PROGRESS)
            .number(RandomStringUtils.randomAlphanumeric(8))
            .deliveryDate(LocalDate.now().toString())
            .driverUserName("TestDriver")
            .build();
        deliveryTask = deliveryTaskRepository.save(deliveryTask);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumber(shopifyOrderDto.getName());
        deliveryOrderDo.setDeliveryTaskId(deliveryTask.getId());
        deliveryOrderDo.setStatus(IN_TRANSIT);
        deliveryOrderJpaDao.save(deliveryOrderDo);

        GpsRawCommand command = GpsRawCommand.builder()
            .userId(deliveryTask.getDriverUserId())
            .gpsReportDtos(List.of(GpsReportDto.builder().latitude(31.2304).longitude(121.4737).reportAt(Instant.now()).build()))
            .build();

        gpsRawResourceApi.report(command);

        DeliveryOrderDto deliveryOrder = deliveryOrderResourceApi.findByOrderNumber(shopifyOrderDto.getName());

        // then
        assertNotNull(deliveryOrder);
        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertTrue(deliveryOrder.getShopifyOrderUrl().contains(shopifyOrderDto.getId()));
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
        assertNotNull(deliveryOrder.getLatestGps());
        assertEquals(deliveryOrder.getLatestGps().getUserId(), deliveryTask.getDriverUserId());
    }
}