package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShippingOrderRepositoryIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    ShippingOrderRepository shippingOrderRepository;

    @Test
    void test_find_rescheduled_shipping_orders() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(LocalDate.now().plusDays(20) + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Location location = locationRepository.findByName("00-01-A-1");
        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.setBreakdownLocation(location);
        shippingOrder.setStatus(ShippingOrderStatus.IN_PROGRESS);

        shippingOrderRepository.save(shippingOrder);

        List<ShippingOrder> rescheduledShippingOrders = shippingOrderRepository.findRescheduledShippingOrders(LocalDate.now()
            .plusDays(19)
            .toString());
        assertEquals(1, rescheduledShippingOrders.size());
        assertEquals(shopifyOrderDto.getName(), rescheduledShippingOrders.getFirst().getOrderNumber());
    }

}