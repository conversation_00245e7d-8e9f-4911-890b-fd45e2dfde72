package com.mercaso.wms.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.transfertask.CreateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.LoadTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.ReceiveTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.application.dto.view.SearchTransferTaskView;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class TransferTaskResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_TRANSFER_TASKS_URL = "/search/transfer-tasks";

    private static final String FIND_BY_ID_URL = "/transfer-tasks/%s";

    private static final String LOAD_TRANSFER_TASK_URL = "/transfer-tasks/%s/load";

    private static final String RECEIVE_TRANSFER_TASK_URL = "/transfer-tasks/%s/receive";

    private static final String ACTIVATE_TRANSFER_TASK_URL = "/transfer-tasks/%s/activate";

    private static final String REVERT_TO_DRAFT_TRANSFER_TASK_URL = "/transfer-tasks/%s/revert-to-draft";

    private static final String FIND_BY_PICKING_TASK_ITEM_ID_URL = "/query/transfer-tasks/picking-task-item-id/%s";

    public TransferTaskResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchTransferTaskView> searchTransferTasks(
        List<String> numbers,
        List<TransferTaskStatus> statuses,
        UUID originWarehouseId,
        UUID destinationWarehouseId,
        LocalDate deliveryDate,
        List<SortType> sortTypes) throws Exception {

        String url =
            SEARCH_TRANSFER_TASKS_URL + "?numbers=" + (CollectionUtils.isEmpty(numbers) ? "" : String.join(",", numbers)) +
                "&statuses=" + (!CollectionUtils.isEmpty(statuses) ? statuses.stream()
                .map(Enum::name)
                .collect(Collectors.joining(",")) : "") +
                "&originWarehouseId=" + (originWarehouseId != null ? originWarehouseId.toString() : "") +
                "&destinationWarehouseId=" + (destinationWarehouseId != null ? destinationWarehouseId.toString() : "") +
                "&deliveryDate=" + (deliveryDate != null ? deliveryDate.toString() : "") +
                "&sortTypes=" + (sortTypes != null ? sortTypes.stream().map(Enum::name).collect(Collectors.joining(",")) : "");

        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public TransferTaskDto getTransferTask(UUID id) {
        return getEntity(String.format(FIND_BY_ID_URL, id), TransferTaskDto.class).getBody();
    }

    public TransferTaskDto createTransferTask(CreateTransferTaskCommand command) {
        return createEntity("/transfer-tasks", command, TransferTaskDto.class);
    }

    public TransferTaskDto updateTransferTask(UpdateTransferTaskCommand command) throws Exception {
        return updateEntity("/transfer-tasks", command, TransferTaskDto.class);
    }

    public TransferTaskDto loadTransferTask(UUID id, LoadTransferTaskCommand command) throws Exception {
        return updateEntity(String.format(LOAD_TRANSFER_TASK_URL, id), command, TransferTaskDto.class);
    }

    public TransferTaskDto receiveTransferTask(UUID id, ReceiveTransferTaskCommand command) throws JsonProcessingException {
        return updateEntity(String.format(RECEIVE_TRANSFER_TASK_URL, id), command, TransferTaskDto.class);
    }

    public TransferTaskDto activateTransferTask(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(ACTIVATE_TRANSFER_TASK_URL, id), null, TransferTaskDto.class);
    }

    public TransferTaskDto revertToDraft(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(REVERT_TO_DRAFT_TRANSFER_TASK_URL, id), null, TransferTaskDto.class);
    }

    public List<TransferTaskDto> findByPickingTaskItemId(UUID pickingTaskItemId) {
        String url = String.format(FIND_BY_PICKING_TASK_ITEM_ID_URL, pickingTaskItemId);
        String body = getEntity(url, String.class).getBody();
        try {
            return SerializationUtils.readValue(body, new TypeReference<>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


} 