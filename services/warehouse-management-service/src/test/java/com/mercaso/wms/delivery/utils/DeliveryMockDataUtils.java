package com.mercaso.wms.delivery.utils;

import static com.mercaso.wms.infrastructure.utils.DateUtils.DATE_TO_STRING_FORMATTER;
import static com.mercaso.wms.infrastructure.utils.DateUtils.RM_DATE_TO_STRING_FORMATTER;

import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShippingAddressDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyCustomerDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyLineItemDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.document.Document;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoutesResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.DriverBreak;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Order;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Route;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Vehicle;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.VehicleSetting;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.OrderStepType;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.RandomStringUtils;

public class DeliveryMockDataUtils {

    public static DeliveryOrder buildDeliveryOrder() {
        return DeliveryOrder.builder()
            .warehouseId(UUID.randomUUID())
            .orderNumber(RandomStringUtils.randomAlphabetic(10))
            .status(DeliveryOrderStatus.CREATED)
            .deliveryDate(LocalDate.now().plusDays(1).toString())
            .deliveryOrderItems(buildDeliveryOrderItems(10))
            .customer(buildCustomer())
            .planDeliveryAt(Instant.now().plusSeconds(1800))
            .planArriveAt(Instant.now().plusSeconds(3600))
            .build();
    }

    public static DeliveryOrder buildDeliveryOrderWithId() {
        return DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .warehouseId(UUID.randomUUID())
            .orderNumber(RandomStringUtils.randomAlphabetic(10))
            .status(DeliveryOrderStatus.CREATED)
            .deliveryDate(LocalDate.now().plusDays(1).toString())
            .deliveryOrderItems(buildDeliveryOrderItems(10))
            .customer(buildCustomer())
            .build();
    }

    public static List<DeliveryOrderItem> buildDeliveryOrderItems(int size) {
        List<DeliveryOrderItem> deliveryOrderItems = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            deliveryOrderItems.add(DeliveryOrderItem.builder()
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .qty(BigDecimal.TEN)
                .build());
        }
        return deliveryOrderItems;
    }

    public static ShopifyOrderForDeliveryDto buildDeliveryShopifyOrderDto() {
        return ShopifyOrderForDeliveryDto.builder()
            .name("M-" + RandomStringUtils.randomAlphabetic(10))
            .id(RandomStringUtils.randomAlphabetic(10))
            .customer(ShopifyCustomerDto.builder()
                .id(RandomStringUtils.randomAlphabetic(10))
                .firstName(RandomStringUtils.randomAlphabetic(10))
                .lastName(RandomStringUtils.randomAlphabetic(10))
                .email(RandomStringUtils.randomAlphabetic(10) + "@gmail.com")
                .build())
            .shippingAddress(ShippingAddressDto.builder().address1(RandomStringUtils.randomAlphabetic(10)).city("LA").build())
            .lineItems(buildShopifyLineItemDtos(10))
            .tags("SELLER_Mercaso," + LocalDate.now())
            .fulfillmentStatus("fulfilled")
            .financialStatus("paid")
            .note("note")
            .totalPrice(BigDecimal.valueOf(1000.00))
            .currentTotalDiscounts(BigDecimal.valueOf(100.00))
            .discountApplications(List.of(
                ShopifyOrderForDeliveryDto.DiscountApplicationDto.builder()
                    .code("Test Discount")
                    .type("manual")
                    .build()))
            .build();
    }

    private static List<ShopifyLineItemDto> buildShopifyLineItemDtos(int size) {
        List<ShopifyLineItemDto> shopifyLineItemDtos = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            shopifyLineItemDtos.add(ShopifyLineItemDto.builder()
                .id(RandomStringUtils.randomAlphabetic(10))
                .sku(RandomStringUtils.randomAlphabetic(10))
                .currentQuantity(10)
                .quantity(10)
                .price(BigDecimal.TEN)
                .discountAllocations(List.of(
                    ShopifyOrderForDeliveryDto.DiscountAllocationDto.builder()
                        .amount(BigDecimal.ONE)
                        .discountApplicationIndex(0)
                        .build()))
                .build());
        }
        return shopifyLineItemDtos;
    }

    public static Customer buildCustomer() {
        return Customer.builder()
            .firstName(RandomStringUtils.randomAlphabetic(10))
            .lastName(RandomStringUtils.randomAlphabetic(10))
            .email(RandomStringUtils.randomAlphabetic(10) + "@gmail.com")
            .build();
    }

    public static ApprovedRoutesResponse buildApprovedRoutesResponse(int routeCount, LocalDate deliveryDate, boolean includeBrk) {
        List<ApprovedRoute> routes = IntStream.range(0, routeCount)
            .mapToObj(i -> buildApprovedRoute(deliveryDate, includeBrk))
            .toList();

        ApprovedRoutesResponse response = new ApprovedRoutesResponse();
        response.setApprovedRoutes(routes);
        return response;
    }

    private static ApprovedRoute buildApprovedRoute(LocalDate deliveryDate, boolean includeBrk) {
        ApprovedRoute route = new ApprovedRoute();
        route.setRoute(buildRoute(deliveryDate, includeBrk));
        route.setOrders(buildOrders());
        route.setDriver(buildDriver());
        route.setVehicle(buildVehicle(deliveryDate));
        return route;
    }

    private static Route buildRoute(LocalDate deliveryDate, boolean includeBrk) {
        Route route = new Route();
        route.setId("RT-" + RandomStringUtils.randomNumeric(6));
        route.setDate(deliveryDate.format(RM_DATE_TO_STRING_FORMATTER));
        route.setVehicleId("VH-" + RandomStringUtils.randomAlphanumeric(4).toUpperCase());
        route.setDriverId("DR-" + RandomStringUtils.randomAlphanumeric(4).toUpperCase());
        route.setSteps(buildSteps(3, includeBrk));
        return route;
    }

    private static List<Step> buildSteps(int count, boolean includeBrk) {
        return IntStream.rangeClosed(1, count)
            .mapToObj(i -> {
                Step step = new Step();
                step.setType(i == count ? "return" : "delivery");
                step.setOrderId(i == count ? null : UUID.randomUUID().toString());
                step.setDisplayLabel(i + "." + i);
                if (includeBrk && i == 1) {
                    step.setType(OrderStepType.BRK.getValue());
                    step.setStartSec(3600);
                    step.setEndSec(9600);
                }
                return step;
            })
            .toList();
    }

    public static Step buildStep(int stopIdx, String orderId, boolean isBrk) {
        Step step = new Step();
        if (isBrk) {
            step.setType(OrderStepType.BRK.getValue());
            step.setStartSec(3600);
            step.setEndSec(9600);
        } else {
            step.setType(OrderStepType.DELIVERY.getValue());
            step.setOrderId(orderId);
        }
        step.setDisplayLabel(stopIdx + "." + stopIdx);
        return step;
    }

    private static Map<UUID, Order> buildOrders() {
        return IntStream.range(0, 2)
            .mapToObj(i -> {
                UUID orderId = UUID.randomUUID();
                Order order = new Order();
                order.setId(orderId.toString());
                order.setName("O- " + RandomStringUtils.randomAlphabetic(5));
                return new AbstractMap.SimpleEntry<>(orderId, order);
            })
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public static Order buildOrder(UUID id) {
        Order order = new Order();
        order.setId(id.toString());
        order.setName("Order " + RandomStringUtils.randomAlphabetic(5));
        return order;
    }

    private static Driver buildDriver() {
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setName("Driver " + RandomStringUtils.randomAlphabetic(5));
        driver.setEmail(driver.getName() + "mercaso.com");
        return driver;
    }

    private static Vehicle buildVehicle(LocalDate deliveryDate) {
        Vehicle vehicle = new Vehicle();
        vehicle.setExternalId("VH_" + RandomStringUtils.randomNumeric(3));
        vehicle.setId(UUID.randomUUID().toString());
        Map<String, VehicleSetting> vehicleSettingMap = buildVehicleSettingMap(deliveryDate.format(RM_DATE_TO_STRING_FORMATTER));
        vehicle.setSettings(vehicleSettingMap);
        return vehicle;
    }

    public static Account buildAccount(String email) {
        return Account.builder()
            .email(email)
            .userName(RandomStringUtils.randomAlphabetic(10))
            .userId(UUID.randomUUID())
            .warehouseId(UUID.randomUUID())
            .status(AccountStatus.ACTIVE)
            .build();
    }

    public static Document buildDocument() {
        return Document.builder()
            .entityId(UUID.randomUUID())
            .entityName(EntityEnums.DELIVERY_ORDER.name())
            .fileName(RandomStringUtils.randomAlphabetic(10))
            .documentType(DocumentType.POD)
            .build();
    }

    /**
     * Create test DeliveryTaskDo object with all required fields set
     */
    public static DeliveryTaskDo buildDeliveryTaskDo(String number, LocalDate deliveryDate, String truckNumber,
        String driverName) {
        DeliveryTaskDo taskDo = new DeliveryTaskDo();

        // Set basic fields
        taskDo.setId(UUID.randomUUID());
        taskDo.setNumber(number);
        taskDo.setDeliveryDate(deliveryDate.format(DATE_TO_STRING_FORMATTER));
        taskDo.setStatus(DeliveryTaskStatus.CREATED);
        taskDo.setTruckNumber(truckNumber);
        taskDo.setDriverUserName(driverName);
        taskDo.setDriverUserId(UUID.randomUUID());

        // Set audit fields
        Instant now = Instant.now();
        taskDo.setCreatedAt(now);
        taskDo.setCreatedBy("test");
        taskDo.setUpdatedAt(now);
        taskDo.setUpdatedBy("test");

        return taskDo;
    }

    public static Map<String, VehicleSetting> buildVehicleSettingMap(String date) {
        Map<String, VehicleSetting> settings = new HashMap<>();

        VehicleSetting vehicleSetting = new VehicleSetting();

        DriverBreak driverBreak = new DriverBreak();
        driverBreak.setStartSec(3600);
        driverBreak.setEndSec(9600);
        List<DriverBreak> driverBreaks = List.of(driverBreak);
        vehicleSetting.setBreaks(driverBreaks);

        settings.put(date, vehicleSetting);
        return settings;
    }
}
