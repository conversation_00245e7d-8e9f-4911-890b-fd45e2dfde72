package com.mercaso.wms.delivery.interfaces;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderItemCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DeliveryOrderResourceIT extends AbstractIT {

    @Autowired
    DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;
    @Autowired
    DeliveryTaskJpaDao deliveryTaskJpaDao;

    @Test
    void when_update_status_then_return_delivery_order() throws Exception {
        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        Optional<DeliveryOrderDo> deliveryOrderDoOptional = deliveryOrderJpaDao.findById(deliveryOrderDo.getId());
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", LocalDate.now(), "TRUCK-001", "Driver1");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        deliveryOrderDoOptional.ifPresent(orderDo -> {
            orderDo.setStatus(DeliveryOrderStatus.ASSIGNED);
            orderDo.setDeliveryTaskId(savedTask.getId());
            deliveryOrderJpaDao.saveAndFlush(orderDo);
        });

        DeliveryOrderDto inTransited = deliveryOrderResourceApi.updateToInTransit(deliveryOrderDo.getId());

        assertNotNull(inTransited.getInTransitAt());
        assertEquals(DeliveryOrderStatus.IN_TRANSIT, inTransited.getStatus());

        DeliveryOrderDto arrived = deliveryOrderResourceApi.updateToArrived(deliveryOrderDo.getId());

        assertEquals(DeliveryOrderStatus.ARRIVED, arrived.getStatus());
        assertNotNull(arrived.getArrivedAt());

        List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos = Lists.newArrayList();
        arrived.getDeliveryOrderItems().forEach(item -> {
            updateDeliveryOrderItemDtos.add(UpdateDeliveryOrderItemCommand.builder()
                .id(item.getId())
                .deliveredQty(item.getQty())
                .build());
        });
        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(updateDeliveryOrderItemDtos)
            .build();

        DeliveryOrderDto unloaded = deliveryOrderResourceApi.updateToUnloaded(arrived.getId(), command);

        assertEquals(DeliveryOrderStatus.UNLOADED, unloaded.getStatus());
        assertEquals(unloaded.getDeliveryOrderItems()
            .stream()
            .mapToDouble(item -> {
                double price = item.getPrice().doubleValue();
                if (CollectionUtils.isNotEmpty(item.getDiscountAllocations())) {
                    double totalDiscount = item.getDiscountAllocations().stream()
                        .mapToDouble(discount -> discount.getAmount().doubleValue())
                        .sum();
                    if (Objects.equals(item.getCurrentQty(), item.getDeliveredQty())) {
                        return item.getDeliveredQty().doubleValue() * price - totalDiscount;
                    } else {
                        double discountPerUnit = totalDiscount / item.getCurrentQty().doubleValue();
                        return item.getDeliveredQty().doubleValue() * (price - BigDecimal.valueOf(discountPerUnit)
                            .setScale(2, RoundingMode.CEILING)
                            .doubleValue());
                    }
                }
                return item.getDeliveredQty().doubleValue() * price;
            })
            .sum(), unloaded.getTotalPrice().doubleValue());

        DeliveryOrderDto delivered = deliveryOrderResourceApi.updateToDelivered(deliveryOrderDo.getId(),
            DeliveryOrderDeliveredCommand.builder()
                .paymentType(List.of(PaymentType.CASH, PaymentType.OTHER))
                .notes("Test notes")
                .build());

        assertEquals(DeliveryOrderStatus.DELIVERED, delivered.getStatus());
        assertNotNull(delivered.getDeliveredAt());
        assertNotNull(delivered.getNotes());
        assert delivered.getPaymentType().contains(PaymentType.CASH);
    }

    @Test
    void when_update_delivery_order_then_return_delivery_order() throws Exception {
        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", LocalDate.now(), "TRUCK-001", "Driver1");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        deliveryOrderDo.setDeliveryTaskId(savedTask.getId());
        deliveryOrderDo.setStatus(DeliveryOrderStatus.ARRIVED);
        deliveryOrderJpaDao.save(deliveryOrderDo);

        List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos = Lists.newArrayList();
        deliveryOrderDo.getDeliveryOrderItems().forEach(item -> {
            updateDeliveryOrderItemDtos.add(UpdateDeliveryOrderItemCommand.builder()
                .id(item.getId())
                .deliveredQty(item.getQty())
                .build());
        });
        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(updateDeliveryOrderItemDtos)
            .build();

        DeliveryOrderDto updated = deliveryOrderResourceApi.updateToUnloaded(deliveryOrderDo.getId(), command);

        assertNotNull(updated);
        assertNotNull(updated.getUnloadedAt());
        assertEquals(DeliveryOrderStatus.UNLOADED, updated.getStatus());
        updated.getDeliveryOrderItems().forEach(item -> {
            assertEquals(item.getQty(), item.getDeliveredQty());
        });

        DeliveryOrderDto updateRescheduleType = deliveryOrderResourceApi.update(deliveryOrderDo.getId(),
            UpdateDeliveryOrderCommand.builder().rescheduleType(RescheduleType.LATER).build());

        assertEquals(RescheduleType.LATER.name(), updateRescheduleType.getRescheduleType());
    }

}