package com.mercaso.wms.delivery.interfaces;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.command.GpsRawCommand.GpsReportDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.gps.GpsRawRepository;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCache;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCacheRepository;
import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.dataobject.GpsRawDo;
import com.mercaso.wms.delivery.utils.GpsRawResourceApi;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class GpsRawResourceIT extends AbstractIT {

    @Autowired
    private GpsRawResourceApi gpsRawResourceApi;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private DeliveryTaskRepository deliveryTaskRepository;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Autowired
    private GpsRawRepository gpsRawRepository;

    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private LatestGpsCacheRepository latestGpsCacheRepository;

    private UUID driverUserId;
    private DeliveryTask deliveryTask;
    private Account account;
    private DeliveryOrder deliveryOrder;
    private Warehouse warehouse;

    @BeforeEach
    void setUp() {
        driverUserId = UUID.randomUUID();

        warehouse = Warehouse.builder()
            .name("Test Warehouse " + RandomStringUtils.randomAlphanumeric(8))
            .type(WarehouseType.INTERNAL)
            .status(WarehouseStatus.ACTIVE)
            .build();
        warehouse = warehouseRepository.save(warehouse);

        account = Account.builder().userId(driverUserId).userName("TestDriver").build();
        accountRepository.save(account);

        deliveryTask = DeliveryTask.builder()
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.IN_PROGRESS)
            .number(generateTaskNumber())
            .deliveryDate(LocalDate.now().toString())
            .driverUserName("TestDriver")
            .build();
        deliveryTask = deliveryTaskRepository.save(deliveryTask);

        deliveryOrder = DeliveryOrder.builder()
            .deliveryTaskId(deliveryTask.getId())
            .status(DeliveryOrderStatus.IN_TRANSIT)
            .sequence(1)
            .orderNumber(generateOrderNumber())
            .deliveryOrderItems(new ArrayList<>())
            .warehouseId(warehouse.getId())
            .deliveryDate(LocalDate.now().toString())
            .build();

        DeliveryOrderItem item = DeliveryOrderItem.builder()
            .deliveryOrderId(deliveryOrder.getId())
            .itemId(UUID.randomUUID())
            .skuNumber("TEST-SKU-001")
            .qty(BigDecimal.valueOf(1))
            .build();
        deliveryOrder.getDeliveryOrderItems().add(item);

        deliveryOrder = deliveryOrderRepository.save(deliveryOrder);
    }

    private String generateTaskNumber() {
        return "DT" + RandomStringUtils.randomNumeric(8);
    }

    private String generateOrderNumber() {
        return "DO" + RandomStringUtils.randomNumeric(8);
    }

    @Test
    void when_report_gps_with_valid_data_then_success() {
        GpsRawCommand command = GpsRawCommand.builder()
            .userId(driverUserId)
            .gpsReportDtos(List.of(GpsReportDto.builder().latitude(31.2304).longitude(121.4737).reportAt(Instant.now()).build()))
            .build();

        assertDoesNotThrow(() -> gpsRawResourceApi.report(command));

        Optional<GpsRawDo> saved = gpsRawRepository.findByUserId(driverUserId);
        assert saved.isPresent();
        assert saved.get().getUserName().equals(account.getUserName());
        double x = saved.get().getCoordinates().getCoordinates()[0].getX();
        double y = saved.get().getCoordinates().getCoordinates()[0].getY();
        assert x == command.getGpsReportDtos().getFirst().getLongitude();
        assert y == command.getGpsReportDtos().getFirst().getLatitude();

        Optional<LatestGpsCache> latestGpsCache = latestGpsCacheRepository.findByUserId(driverUserId);

        assert latestGpsCache.isPresent();
        assert latestGpsCache.get().getUserName().equals(account.getUserName());
        assert latestGpsCache.get().getCoordinates().getCoordinates()[0].getX() == x;
        assert latestGpsCache.get().getCoordinates().getCoordinates()[0].getY() == y;
    }

    @Test
    void when_report_gps_with_no_active_task_then_success() {
        UUID newDriverId = UUID.randomUUID();
        GpsRawCommand command = GpsRawCommand.builder()
            .userId(newDriverId)
            .gpsReportDtos(List.of(GpsReportDto.builder().latitude(31.2304).longitude(121.4737).reportAt(Instant.now()).build()))
            .build();

        assertDoesNotThrow(() -> gpsRawResourceApi.report(command));
    }

    @Test
    void when_report_multiple_gps_then_success() {
        GpsRawCommand command1 = GpsRawCommand.builder()
            .userId(driverUserId)
            .gpsReportDtos(List.of(GpsReportDto.builder().latitude(31.2304).longitude(121.4737).reportAt(Instant.now()).build()))
            .build();

        GpsRawCommand command2 = GpsRawCommand.builder()
            .userId(driverUserId)
            .gpsReportDtos(List.of(GpsReportDto.builder()
                .latitude(31.2304)
                .longitude(121.4737)
                .reportAt(Instant.now().plusSeconds(30))
                .build()))
            .build();

        assertDoesNotThrow(() -> {
            gpsRawResourceApi.report(command1);
            gpsRawResourceApi.report(command2);
        });
    }
} 