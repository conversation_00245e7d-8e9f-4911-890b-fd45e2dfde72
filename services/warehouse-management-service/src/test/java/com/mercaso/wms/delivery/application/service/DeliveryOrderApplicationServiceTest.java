package com.mercaso.wms.delivery.application.service;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildCustomer;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryOrder;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryOrderWithId;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.maps.model.LatLng;
import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShippingAddressDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.queryservice.DocumentQueryService;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.customer.CustomerService;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.googlemap.GoogleMapAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEvent;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ExecutionEventResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.ExecutionEventType;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeliveryOrderApplicationServiceTest {

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    @Mock
    private WarehouseRepository warehouseRepository;

    @Mock
    private ImsAdaptor imsAdaptor;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private DeliveryOrderDtoApplicationMapper mapper;

    @Mock
    private BusinessEventDispatcher businessEventDispatcher;

    @Mock
    private CustomerService customerService;

    @Mock
    private DeliveryTaskService deliveryTaskService;

    @Mock
    private RouteManagerAdaptor routeManagerAdaptor;

    @Mock
    private RmRouteRepository rmRouteRepository;

    @Mock
    private ApplicationEventDispatcher applicationEventDispatcher;

    @Mock
    private GoogleMapAdaptor googleMapAdaptor;

    @Mock
    private SlackDeliveryNotificationService slackNotificationService;

    @Captor
    private ArgumentCaptor<List<ExecutionEvent>> executionEventCaptor;

    private DeliveryOrderApplicationService deliveryOrderApplicationService;

    @Mock
    private OnesignalAdaptor onesignalAdaptor;
    @Mock
    private DocumentApplicationService documentApplicationService;
    @Mock
    private DocumentQueryService documentQueryService;
    @Mock
    private CustomerInvoiceService customerInvoiceService;

    @BeforeEach
    void setUp() {
        deliveryOrderApplicationService = new DeliveryOrderApplicationService(
            imsAdaptor,
            pgAdvisoryLock,
            deliveryOrderRepository,
            warehouseRepository,
            mapper,
            businessEventDispatcher,
            customerService,
            deliveryTaskService,
            routeManagerAdaptor,
            rmRouteRepository,
            googleMapAdaptor,
            applicationEventDispatcher,
            slackNotificationService,
            onesignalAdaptor,
            documentApplicationService,
            documentQueryService,
            customerInvoiceService);
    }

    @Test
    void arrive_shouldSendTimeInExecutionEventWhenTaskAndRouteFound() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setDeliveryTaskId(taskId);
        deliveryOrder.setRmOrderId("RM-123");
        deliveryOrder.setDeliveryDate("2023-05-15");
        doNothing().when(deliveryOrder).updateStatus(any(), any());

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setId(deliveryOrderId);
        deliveryOrderDto.setDeliveryTaskId(taskId);
        deliveryOrderDto.setRmOrderId("RM-123");
        deliveryOrderDto.setDeliveryDate("2023-05-15");
        deliveryOrderDto.setStatus(DeliveryOrderStatus.ARRIVED);
        deliveryOrderDto.setArrivedAt(Instant.now());

        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .vehicleId("VEHICLE-123")
            .routeId("ROUTE-123")
            .deliveryTaskId(taskId)
            .build();

        List<RmRoute> rmRoutes = List.of(rmRoute);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(mapper.domainToDto(any())).thenReturn(deliveryOrderDto);
        when(deliveryOrderRepository.update(any())).thenReturn(deliveryOrder);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(rmRoutes);
        when(routeManagerAdaptor.sendExecutionEvents(any())).thenReturn(new ExecutionEventResponse());
        when(businessEventDispatcher.dispatch(any())).thenReturn(mock(DispatchResponseDto.class));

        // When
        deliveryOrderApplicationService.arrive(deliveryOrderId);

        // Then
        verify(deliveryTaskService).inProgress(taskId);
        verify(rmRouteRepository).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor).sendExecutionEvents(executionEventCaptor.capture());

        List<ExecutionEvent> events = executionEventCaptor.getValue();
        assert events.size() == 1;
        ExecutionEvent event = events.get(0);
        assert event.getOrderId().equals("RM-123");
        assert event.getVehicleId().equals("VEHICLE-123");
        assert event.getDate().equals("20230515");
        assert event.getType().equals("timeIn");
    }

    @Test
    void arrive_shouldNotSendExecutionEventWhenRouteNotFound() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setDeliveryTaskId(taskId);
        doNothing().when(deliveryOrder).updateStatus(any(), any());

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setId(deliveryOrderId);
        deliveryOrderDto.setDeliveryTaskId(taskId);
        deliveryOrderDto.setStatus(DeliveryOrderStatus.ARRIVED);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(mapper.domainToDto(any())).thenReturn(deliveryOrderDto);
        when(deliveryOrderRepository.update(any())).thenReturn(deliveryOrder);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of());
        when(businessEventDispatcher.dispatch(any())).thenReturn(mock(DispatchResponseDto.class));

        // When
        deliveryOrderApplicationService.arrive(deliveryOrderId);

        // Then
        verify(deliveryTaskService).inProgress(taskId);
        verify(rmRouteRepository).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor, never()).sendExecutionEvents(any());
    }

    @Test
    void delivery_shouldSendTimeOutExecutionEventWhenTaskAndRouteFound() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setDeliveryTaskId(taskId);
        deliveryOrder.setRmOrderId("RM-123");
        deliveryOrder.setDeliveryDate("2023-05-15");
        doNothing().when(deliveryOrder).updateStatus(any(), any());

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setId(deliveryOrderId);
        deliveryOrderDto.setDeliveryTaskId(taskId);
        deliveryOrderDto.setRmOrderId("RM-123");
        deliveryOrderDto.setDeliveryDate("2023-05-15");
        deliveryOrderDto.setStatus(DeliveryOrderStatus.DELIVERED);
        deliveryOrderDto.setDeliveredAt(Instant.now());

        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .vehicleId("VEHICLE-123")
            .routeId("ROUTE-123")
            .deliveryTaskId(taskId)
            .build();

        List<RmRoute> rmRoutes = List.of(rmRoute);

        DeliveryOrderDeliveredCommand command = DeliveryOrderDeliveredCommand.builder().build();

        DispatchResponseDto responseDto = mock(DispatchResponseDto.class);
        BusinessEventDto mockEvent = mock(BusinessEventDto.class);
        when(responseDto.getEvent()).thenReturn(mockEvent);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(mapper.domainToDto(any())).thenReturn(deliveryOrderDto);
        when(deliveryOrderRepository.update(any())).thenReturn(deliveryOrder);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(rmRoutes);
        when(routeManagerAdaptor.sendExecutionEvents(any())).thenReturn(new ExecutionEventResponse());
        when(businessEventDispatcher.dispatch(any())).thenReturn(responseDto);

        // When
        deliveryOrderApplicationService.delivery(deliveryOrderId, command);

        // Then
        verify(deliveryTaskService).inProgress(taskId);
        verify(rmRouteRepository).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor).sendExecutionEvents(executionEventCaptor.capture());

        List<ExecutionEvent> events = executionEventCaptor.getValue();
        assert events.size() == 2;
        ExecutionEvent event = events.getFirst();
        assert event.getOrderId().equals("RM-123");
        assert event.getVehicleId().equals("VEHICLE-123");
        assert event.getDate().equals("20230515");
        assert event.getType().equals(ExecutionEventType.TIME_OUT.value());
        ExecutionEvent updateStatusEvent = events.getLast();
        assert updateStatusEvent.getType().equals(ExecutionEventType.STATUS_UPDATE.value());
    }

    @Test
    void delivery_shouldNotSendExecutionEventWhenRouteNotFound() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setDeliveryTaskId(taskId);
        doNothing().when(deliveryOrder).updateStatus(any(), any());

        DeliveryOrderDto deliveryOrderDto = new DeliveryOrderDto();
        deliveryOrderDto.setId(deliveryOrderId);
        deliveryOrderDto.setDeliveryTaskId(taskId);
        deliveryOrderDto.setStatus(DeliveryOrderStatus.DELIVERED);

        DeliveryOrderDeliveredCommand command = DeliveryOrderDeliveredCommand.builder().build();

        DispatchResponseDto responseDto = mock(DispatchResponseDto.class);
        BusinessEventDto mockEvent = mock(BusinessEventDto.class);
        when(responseDto.getEvent()).thenReturn(mockEvent);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(mapper.domainToDto(any())).thenReturn(deliveryOrderDto);
        when(deliveryOrderRepository.update(any())).thenReturn(deliveryOrder);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of());
        when(businessEventDispatcher.dispatch(any())).thenReturn(responseDto);

        // When
        deliveryOrderApplicationService.delivery(deliveryOrderId, command);

        // Then
        verify(deliveryTaskService).inProgress(taskId);
        verify(rmRouteRepository).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor, never()).sendExecutionEvents(any());
    }

    @Test
    void createOrUnload_NonMfcOrder_ShouldIgnore() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        shopifyOrderDto.setTags("");

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(deliveryOrderRepository, never()).findByOrderNumberAndShopifyOrderId(any(), any());
        verify(deliveryOrderRepository, never()).save(any());
    }

    @Test
    void createOrUnload_NewOrder_ShouldCreate() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(deliveryOrderRepository).findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(), shopifyOrderDto.getId());
        verify(customerService).findOrSaveCustomer(any());
        verify(warehouseRepository, times(1)).findByName(anyString());
        verify(imsAdaptor).getItemsBySkus(any());
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    void createOrUpdate_ExistingOrder_ShouldUnload() {
        DeliveryOrder deliveryOrder = buildDeliveryOrder();
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        // Given
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(deliveryOrder);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(deliveryOrder);

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(deliveryOrderRepository).findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(), shopifyOrderDto.getId());
        verify(customerService).findOrSaveCustomer(any());
        verify(warehouseRepository, never()).findByName(any());
        verify(imsAdaptor).getItemsBySkus(any());
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    void createOrUnload_ExistingCustomer_ShouldReuse() {
        DeliveryOrder deliveryOrder = buildDeliveryOrder();
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        Customer customer = buildCustomer();
        customer.setEmail(deliveryOrder.getCustomer().getEmail());
        // Given
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(customer);
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(deliveryOrder);

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(customerService).findOrSaveCustomer(shopifyOrderDto.getCustomer());
    }

    @Test
    void unload_CanceledOrder_ShouldThrowException() {
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setState(DeliveryOrderStatus.CANCELED);
        when(deliveryOrderRepository.findById(any())).thenReturn(deliveryOrder);

        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(List.of())
            .build();

        // When & Then
        try {
            deliveryOrderApplicationService.unload(deliveryOrder.getId(), command);
        } catch (WmsBusinessException e) {
            verify(deliveryOrderRepository, never()).save(any());
            verify(businessEventDispatcher, never()).dispatch(any());
        }
    }

    @Test
    void unload_NotArrivedOrder_ShouldThrowException() {
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());
        deliveryOrder.setState(DeliveryOrderStatus.ASSIGNED);
        when(deliveryOrderRepository.findById(any())).thenReturn(deliveryOrder);

        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(List.of())
            .build();

        // When & Then
        try {
            deliveryOrderApplicationService.unload(deliveryOrder.getId(), command);
        } catch (WmsBusinessException e) {
            verify(deliveryOrderRepository, never()).save(any());
            verify(businessEventDispatcher, never()).dispatch(any());
        }
    }

    @Test
    @DisplayName("When order has missing coordinates Then should call Google Maps API")
    void createOrUpdate_WithMissingCoordinates_ShouldGeocodeAddress() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setAddress1("123 Main St");
        shippingAddress.setCity("San Francisco");
        shippingAddress.setProvince("CA");
        shippingAddress.setZip("94105");
        shippingAddress.setCountry("USA");
        // Coordinates are null
        shopifyOrderDto.setShippingAddress(shippingAddress);

        LatLng mockCoordinates = new LatLng(37.7749, -122.4194);
        when(googleMapAdaptor.getCoordinatesByAddress(anyString())).thenReturn(mockCoordinates);
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor).getCoordinatesByAddress(anyString());
        assert shippingAddress.getLatitude().equals(BigDecimal.valueOf(37.7749));
        assert shippingAddress.getLongitude().equals(BigDecimal.valueOf(-122.4194));
    }

    @Test
    @DisplayName("When order has coordinates Then should not call Google Maps API")
    void createOrUpdate_WithExistingCoordinates_ShouldNotGeocodeAddress() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setAddress1("123 Main St");
        shippingAddress.setCity("San Francisco");
        shippingAddress.setProvince("CA");
        shippingAddress.setZip("94105");
        shippingAddress.setCountry("USA");
        shippingAddress.setLatitude(BigDecimal.valueOf(37.7749));
        shippingAddress.setLongitude(BigDecimal.valueOf(-122.4194));
        shopifyOrderDto.setShippingAddress(shippingAddress);

        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor, never()).getCoordinatesByAddress(anyString());
        assert shippingAddress.getLatitude().equals(BigDecimal.valueOf(37.7749));
        assert shippingAddress.getLongitude().equals(BigDecimal.valueOf(-122.4194));
    }

    @Test
    @DisplayName("When Google Maps API returns null Then should handle gracefully")
    void createOrUpdate_WithGeocodingReturningNull_ShouldHandleGracefully() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setAddress1("Invalid Address");
        shopifyOrderDto.setShippingAddress(shippingAddress);

        when(googleMapAdaptor.getCoordinatesByAddress(anyString())).thenReturn(null);
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor).getCoordinatesByAddress(anyString());
        assert shippingAddress.getLatitude() == null;
        assert shippingAddress.getLongitude() == null;
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    @DisplayName("When Google Maps API throws exception Then should handle gracefully")
    void createOrUpdate_WithGeocodingThrowingException_ShouldHandleGracefully() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setAddress1("123 Main St");
        shippingAddress.setCity("San Francisco");
        shopifyOrderDto.setShippingAddress(shippingAddress);

        when(googleMapAdaptor.getCoordinatesByAddress(anyString())).thenThrow(new RuntimeException("API Error"));
        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor).getCoordinatesByAddress(anyString());
        assert shippingAddress.getLatitude() == null;
        assert shippingAddress.getLongitude() == null;
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    @DisplayName("When shipping address is incomplete Then should not call Google Maps API")
    void createOrUpdate_WithIncompleteAddress_ShouldNotGeocodeAddress() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        // No address fields set
        shopifyOrderDto.setShippingAddress(shippingAddress);

        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor, never()).getCoordinatesByAddress(anyString());
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    @DisplayName("When shipping address is null Then should not call Google Maps API")
    void createOrUpdate_WithNullShippingAddress_ShouldNotGeocodeAddress() {
        // Given
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        shopifyOrderDto.setShippingAddress(null);

        when(deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(any(), any())).thenReturn(null);
        when(customerService.findOrSaveCustomer(any())).thenReturn(buildCustomer());
        when(warehouseRepository.findByName("MDC")).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(new ArrayList<>());
        when(deliveryOrderRepository.save(any())).thenReturn(buildDeliveryOrder());

        // When
        deliveryOrderApplicationService.createOrUpdate(shopifyOrderDto);

        // Then
        verify(googleMapAdaptor, never()).getCoordinatesByAddress(anyString());
        verify(deliveryOrderRepository).save(any());
    }

    @Test
    void updateDeliveryOrder_shouldUpdateRescheduleType() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = spy(buildDeliveryOrderWithId());

        UpdateDeliveryOrderCommand command = UpdateDeliveryOrderCommand.builder()
            .rescheduleType(RescheduleType.LATER)
            .build();

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(deliveryOrderRepository.update(any())).thenReturn(deliveryOrder);

        // When
        deliveryOrderApplicationService.update(deliveryOrderId, command);

        // Then
        verify(deliveryOrder).update(command);
        verify(businessEventDispatcher).dispatch(any());
        verify(customerInvoiceService, never()).createCustomerInvoice(any());
    }

    @Test
    void when_send_invoice_but_no_document_then_should_throw_exception() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = buildDeliveryOrderWithId();
        deliveryOrder.setStatus(DeliveryOrderStatus.DELIVERED);
        deliveryOrder.setCustomer(buildCustomer());
        deliveryOrder.setAddress(null);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(documentQueryService.findDocumentsBy(any(), any(), any())).thenReturn(new ArrayList<>());

        // When
        try {
            deliveryOrderApplicationService.sendInvoice(deliveryOrderId);
        } catch (DeliveryBusinessException e) {
            assertTrue(e.getMessage().contains("No invoice documents found for delivery order"));
        }
    }

    @Test
    void when_send_invoice_but_no_customer_email_then_should_throw_exception() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = buildDeliveryOrderWithId();
        deliveryOrder.setStatus(DeliveryOrderStatus.DELIVERED);
        deliveryOrder.setCustomer(null);
        deliveryOrder.setAddress(null);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(documentQueryService.findDocumentsBy(any(), any(), any())).thenReturn(List.of(
            DocumentDto.builder()
                .fileName("invoice.pdf")
                .build()
        ));

        // When
        try {
            deliveryOrderApplicationService.sendInvoice(deliveryOrderId);
        } catch (DeliveryBusinessException e) {
            assertTrue(e.getMessage().contains("Customer email not found for delivery order"));
        }
    }

    @Test
    void when_send_invoice_then_should_send_success() throws IOException {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        DeliveryOrder deliveryOrder = buildDeliveryOrderWithId();
        deliveryOrder.setStatus(DeliveryOrderStatus.DELIVERED);
        deliveryOrder.setCustomer(buildCustomer());
        deliveryOrder.setAddress(null);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(documentQueryService.findDocumentsBy(any(), any(), any())).thenReturn(List.of(
            DocumentDto.builder()
                .fileName("invoice.pdf")
                .build()
        ));
        when(documentApplicationService.generateInvoiceSignatureWithExpiration(anyString())).thenReturn("signedUrl");

        deliveryOrderApplicationService.sendInvoice(deliveryOrderId);

        verify(onesignalAdaptor, times(1)).sendEmail(anyString(), any());
    }
} 