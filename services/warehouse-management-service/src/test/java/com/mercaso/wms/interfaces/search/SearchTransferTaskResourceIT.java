package com.mercaso.wms.interfaces.search;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchTransferTaskView;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import static com.mercaso.wms.utils.MockDataUtils.buildTransferTask;
import com.mercaso.wms.utils.TransferTaskResourceApi;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchTransferTaskResourceIT extends AbstractIT {

    @Autowired
    private TransferTaskResourceApi transferTaskResourceApi;

    @Autowired
    private TransferTaskRepository transferTaskRepository;

    @BeforeEach
    void setUp() {
        transferTaskRepository.deleteAll();
    }

    @Test
    void searchTransferTasks_withValidParameters_returnsResult() throws Exception {
        List<TransferTask> transferTasks = buildTransferTask(5, TransferTaskStatus.RECEIVED);
        transferTaskRepository.saveAll(transferTasks);

        Result<SearchTransferTaskView> transferTaskDtoResult = transferTaskResourceApi.searchTransferTasks(
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(transferTaskDtoResult);
        assertNotNull(transferTaskDtoResult.getData());
        assertNotNull(transferTaskDtoResult.getTotalCount());
        assertEquals(5, transferTaskDtoResult.getData().size());
        assertEquals(5, transferTaskDtoResult.getTotalCount());
    }

    @Test
    void searchTransferTasks_withStatus_returnsResult() throws Exception {

        List<TransferTask> transferTasks = buildTransferTask(5, TransferTaskStatus.DRAFT);
        transferTaskRepository.saveAll(transferTasks);

        Result<SearchTransferTaskView> transferTaskDtoResult = transferTaskResourceApi.searchTransferTasks(
            null,
            List.of(TransferTaskStatus.DRAFT),
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(transferTaskDtoResult);
        assertNotNull(transferTaskDtoResult.getData());
        assertNotNull(transferTaskDtoResult.getTotalCount());
        assertEquals(5, transferTaskDtoResult.getData().size());
    }


}