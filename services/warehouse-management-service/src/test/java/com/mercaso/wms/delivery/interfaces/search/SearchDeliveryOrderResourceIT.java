package com.mercaso.wms.delivery.interfaces.search;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchDeliveryOrderResourceIT extends AbstractIT {

    @Autowired
    DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;

    @Test
    void when_search_delivery_orders_then_return_search_delivery_order_view() throws Exception {
        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        Result<SearchDeliveryOrderView> searchByOrderNumbers = deliveryOrderResourceApi.search(List.of(shopifyOrderDto.getName()),
            List.of(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.DELIVERY_DATE_ASC));
        assertEquals(1, searchByOrderNumbers.getData().size());
        SearchDeliveryOrderView deliveryOrder = searchByOrderNumbers.getData().getFirst();
        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertEquals(shopifyOrderDto.getFinancialStatus(), deliveryOrder.getPaymentStatus());
        assertEquals(shopifyOrderDto.getFulfillmentStatus(), deliveryOrder.getFulfillmentStatus());
        assertEquals(shopifyOrderDto.getNote(), deliveryOrder.getCustomerNotes());
        assertEquals(shopifyOrderDto.getShippingAddress().getAddress1(), deliveryOrder.getAddress().getAddressOne());
        assertEquals(0, shopifyOrderDto.getTotalPrice().compareTo(deliveryOrder.getOriginalTotalPrice()));

        DeliveryOrderDto deliveryOrderDto = deliveryOrderResourceApi.findById(searchByOrderNumbers.getData().getFirst().getId());

        deliveryOrderResourceApi.update(deliveryOrderDto.getId(), UpdateDeliveryOrderCommand.builder().rescheduleType(
            RescheduleType.LATER).build());

        Result<SearchDeliveryOrderView> searchByRescheduleType = deliveryOrderResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            RescheduleType.LATER.name(),
            null,
            List.of(SortType.DELIVERY_DATE_ASC));

        assertEquals(1, searchByRescheduleType.getData().size());
        assertEquals(RescheduleType.LATER.name(), searchByRescheduleType.getData().getFirst().getRescheduleType());

        Result<SearchDeliveryOrderView> searchByStatuses = deliveryOrderResourceApi.search(null,
            List.of(DeliveryOrderStatus.CREATED),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        assertEquals(1, searchByStatuses.getData().size());

        Result<SearchDeliveryOrderView> searchByDeliveryDate = deliveryOrderResourceApi.search(null,
            List.of(),
            LocalDate.now(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        assertEquals(1, searchByDeliveryDate.getData().size());

        Result<SearchDeliveryOrderView> searchByPaymentStatus = deliveryOrderResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            List.of(shopifyOrderDto.getFinancialStatus()),
            null,
            null,
            null,
            null);
        assertEquals(1, searchByPaymentStatus.getData().size());

        Result<SearchDeliveryOrderView> searchByFulfillmentStatus = deliveryOrderResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(shopifyOrderDto.getFulfillmentStatus()),
            null,
            null,
            null);
        assertEquals(1, searchByFulfillmentStatus.getData().size());

        Result<SearchDeliveryOrderView> searchByIssueOrder = deliveryOrderResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            true,
            List.of(SortType.CREATED_AT_DESC));

        assertEquals(0, searchByIssueOrder.getData().size());
    }

}