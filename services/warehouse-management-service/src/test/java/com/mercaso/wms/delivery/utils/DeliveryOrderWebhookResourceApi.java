package com.mercaso.wms.delivery.utils;

import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class DeliveryOrderWebhookResourceApi extends IntegrationTestRestUtil {

    private static final String SHOPIFY_WEBHOOK_URL = "/shopify/delivery-order/webhook";

    public DeliveryOrderWebhookResourceApi(Environment environment) {
        super(environment);
    }

    public void webhook(ShopifyOrderForDeliveryDto shopifyOrderDto) throws Exception {
        createShopifyWebhook(SHOPIFY_WEBHOOK_URL, SerializationUtils.serialize(shopifyOrderDto));
    }

    public void webhook(String payload) {
        createShopifyWebhook(SHOPIFY_WEBHOOK_URL, payload);
    }
}
