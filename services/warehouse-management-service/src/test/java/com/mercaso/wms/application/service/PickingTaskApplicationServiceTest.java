package com.mercaso.wms.application.service;


import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand.UpdatePickingTaskItemDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.mapper.pickingtask.PickingTaskDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.PickingTaskItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.builder.DataBuilder;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntity;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntityTypeEnum;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.kafka.common.errors.ResourceNotFoundException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

class PickingTaskApplicationServiceTest {

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final PickingTaskDtoApplicationMapper pickingTaskDtoApplicationMapper = mock(PickingTaskDtoApplicationMapper.class);

    private final ApplicationEventDispatcher applicationEventDispatcher = mock(ApplicationEventDispatcher.class);

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);

    private final PickingTaskItemRepository pickingTaskItemRepository = mock(PickingTaskItemRepository.class);

    private final PickingTaskItemQueryService pickingTaskItemQueryService = mock(PickingTaskItemQueryService.class);

    private final LocationCache locationCache = mock(LocationCache.class);

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final FinaleConfigProperties finaleConfigProperties = mock(FinaleConfigProperties.class);

    private final FinaleProductService finaleProductService = mock(FinaleProductService.class);

    private final ImsAdaptor imsAdaptor = mock(ImsAdaptor.class);

    private final BatchRepository batchRepository = mock(BatchRepository.class);

    private final PickingTaskApplicationService pickingTaskApplicationService = new PickingTaskApplicationService(
        pickingTaskRepository,
        businessEventDispatcher,
        pickingTaskDtoApplicationMapper,
        applicationEventDispatcher,
        batchItemRepository,
        pickingTaskItemRepository,
        imsAdaptor,
        pickingTaskItemQueryService,
        locationCache,
        locationRepository,
        finaleConfigProperties,
        finaleProductService,
        batchRepository);

    private UUID pickingTaskId;
    private PickingTask pickingTask;
    private PickingTaskDto pickingTaskDto;

    @BeforeEach
    void setUp() {
        pickingTaskId = UUID.randomUUID();
        pickingTask = mock(PickingTask.class);
        pickingTaskDto = mock(PickingTaskDto.class);

        when(pickingTaskDto.getId()).thenReturn(pickingTaskId);
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(any())).thenReturn(pickingTaskDto);
    }

    @Test
    void assignPickingTask_Success() {
        // Arrange
        AssignPickingTaskCommand command = AssignPickingTaskCommand.builder().build();
        command.setPickerUserId(UUID.randomUUID());
        command.setPickerUserName("Test User");

        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);

        // Act
        PickingTaskDto result = pickingTaskApplicationService.assignPickingTask(pickingTaskId, command);

        // Assert
        verify(pickingTask).assignTask(command.getPickerUserId(), command.getPickerUserName());
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void startPicking_WhenUserNotAuthorized_ThrowsException() {
        // Arrange
        UUID pickerUserId = UUID.randomUUID();
        when(pickingTask.getPickerUserId()).thenReturn(pickerUserId);
        try (MockedStatic<SecurityContextUtil> securityContextUtil = mockStatic(SecurityContextUtil.class)) {
            securityContextUtil.when(SecurityContextUtil::getLoginUserId)
                .thenReturn(UUID.randomUUID().toString());

            // Act & Assert
            assertThrows(WmsBusinessException.class,
                () -> pickingTaskApplicationService.startPicking(pickingTaskId));
        }
    }

    @Test
    void completePickingTask_WhenStatusIsPicked_CompletesSuccessfully() {
        // Arrange
        when(pickingTask.getStatus()).thenReturn(PickingTaskStatus.PICKED);
        when(pickingTaskDto.getStatus()).thenReturn(PickingTaskStatus.COMPLETED);
        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder().build());

        // Act
        PickingTaskDto result = pickingTaskApplicationService.completePickingTask(pickingTaskId);

        // Assert
        verify(pickingTask).completePicking();
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void cancelPickingTask_Success() {
        // Arrange
        String reason = "Test reason";
        when(pickingTaskRepository.save(any())).thenReturn(pickingTask);

        // Act
        PickingTaskDto result = pickingTaskApplicationService.cancelPickingTask(pickingTaskId, reason);

        // Assert
        verify(pickingTask).cancelTask();
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void getPickingTaskById_WhenNotFound_ThrowsException() {
        // Arrange
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        // Act & Assert
        assertThrows(ResourceNotFoundException.class,
            () -> pickingTaskApplicationService.getPickingTaskById(pickingTaskId));
    }

    @Test
    void bulkInventoryTransfer_AllTasksCompleted_Success() {
        String finaleShipmentNumber = "shipment123";
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Location destinationLocation = locations.getFirst();

        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2, SourceEnum.MDC, PickingTaskStatus.COMPLETED, PickingTaskType.ORDER);

        PickingTask task1 = pickingTasks.get(0);
        task1.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(1);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });

        PickingTask task2 = pickingTasks.get(1);
        task2.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(2);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });
        task2.setStatus(PickingTaskStatus.PARTIALLY_COMPLETED);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser(finaleShipmentNumber);
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName(any())).thenReturn(destinationLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(response);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(response);
        when(batchRepository.findById(any())).thenReturn(batch);

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(batch).setFinaleTransferShipmentNumber(finaleShipmentNumber);
        verify(pickingTaskRepository, times(1)).findByBatchId(batchId);
        verify(finaleProductService, times(1)).createTransferShipment(any());
        verify(finaleProductService, times(1)).shipTransferShipment(any());
        verify(finaleProductService, times(1)).receiveTransferShipment(any());
    }

    @Test
    void bulkInventoryTransfer_when_transferShipment_generated() {
        String finaleShipmentNumber = "shipment123";
        UUID batchId = UUID.randomUUID();
        Batch batch = mock(Batch.class);
        when(batch.getId()).thenReturn(batchId);
        FinaleEntity finaleEntity = new FinaleEntity();
        finaleEntity.setEntityType(FinaleEntityTypeEnum.TRANSFER_SHIPMENT);
        finaleEntity.setEntityId(finaleShipmentNumber);
        List<FinaleEntity> finaleEntityList = new ArrayList<>();
        finaleEntityList.add(finaleEntity);
        when(batch.getFinaleEntities()).thenReturn(SerializationUtils.toTree(new ArrayList<>(finaleEntityList)));

        List<Location> locations = DataBuilder.buildLocations(3, LocationType.BIN);
        Location destinationLocation = locations.getFirst();

        Map<UUID, Location> locationMap = locations.stream()
            .collect(Collectors.toMap(Location::getId, Function.identity()));

        List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 2, SourceEnum.MDC, PickingTaskStatus.COMPLETED, PickingTaskType.ORDER);

        PickingTask task1 = pickingTasks.get(0);
        task1.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(1);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });

        PickingTask task2 = pickingTasks.get(1);
        task2.getPickingTaskItems().forEach(taskItem -> {
            Location location = locations.get(2);
            taskItem.setLocationName(location.getName());
            taskItem.setLocationId(location.getId());
            taskItem.setExpectQty(2);
            taskItem.setPickedQty(2);
        });
        task2.setStatus(PickingTaskStatus.PARTIALLY_COMPLETED);

        FinaleTransferShipmentResponse response = buildFinaleTransferShipmentResponse();
        response.setShipmentIdUser(finaleShipmentNumber);
        response.setShipDate(Instant.now().toString());
        response.setReceiveDate(Instant.now().toString());

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(locationRepository.findByName(any())).thenReturn(destinationLocation);
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("marcaso/10000");
        when(finaleConfigProperties.getDomain()).thenReturn("marcaso");
        when(finaleProductService.createTransferShipment(any())).thenReturn(response);
        when(finaleProductService.shipTransferShipment(any())).thenReturn(response);
        when(finaleProductService.receiveTransferShipment(any())).thenReturn(response);

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(batch).setFinaleTransferShipmentNumber(finaleShipmentNumber);
        verify(pickingTaskRepository, times(1)).findByBatchId(batchId);
        verify(finaleProductService, times(1)).createTransferShipment(any());
        verify(finaleProductService, times(1)).shipTransferShipment(any());
        verify(finaleProductService, times(1)).receiveTransferShipment(any());
        verify(batchRepository, never()).save(any());
    }

    @Test
    void bulkInventoryTransfer_TasksNotCompleted_LogIncompleteCount() {
        // Arrange
        Batch batch = mock(Batch.class);
        UUID batchId = UUID.randomUUID();
        when(batch.getId()).thenReturn(batchId);

        PickingTask task1 = mock(PickingTask.class);
        PickingTask task2 = mock(PickingTask.class);
        when(task1.getStatus()).thenReturn(PickingTaskStatus.PICKING);
        when(task2.getStatus()).thenReturn(PickingTaskStatus.ASSIGNED);

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of(task1, task2));

        pickingTaskApplicationService.bulkInventoryTransfer(batch);

        verify(pickingTaskRepository).findByBatchId(batchId);
        verifyNoInteractions(finaleProductService);
    }

    @Test
    void updatePickingTask_ValidCommand_UpdatesTaskSuccessfully() {
        UUID pickingTaskId = UUID.randomUUID();
        UpdatePickingTaskCommand command = mock(UpdatePickingTaskCommand.class);
        PickingTask pickingTask = mock(PickingTask.class);
        PickingTask updatedPickingTask = mock(PickingTask.class);
        PickingTaskDto pickingTaskDto = mock(PickingTaskDto.class);
        PickingTaskStatus originalStatus = PickingTaskStatus.ASSIGNED;

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(pickingTask.getStatus()).thenReturn(originalStatus);
        when(locationCache.getLocationMap()).thenReturn(Map.of());
        when(pickingTaskRepository.update(pickingTask)).thenReturn(updatedPickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(updatedPickingTask)).thenReturn(pickingTaskDto);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTaskId, command);

        verify(pickingTask).update(command, Map.of());
        verify(pickingTaskRepository).update(pickingTask);
        verify(pickingTaskDtoApplicationMapper).domainToDto(updatedPickingTask);
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void updatePickingTask_withErrorInfo_UpdatesTaskSuccessfully() {
        List<Location> locations = DataBuilder.buildLocations(1, LocationType.BIN);
        PickingTask pickingTask = PickingTask.builder()
            .id(UUID.randomUUID())
            .batchId(UUID.randomUUID())
            .number("123456")
            .pickerUserId(UUID.randomUUID())
            .pickerUserName("test user")
            .status(PickingTaskStatus.PICKING)
            .build();

        PickingTaskItem taskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .pickingTaskId(pickingTask.getId())
            .errorInfo("Test error info")
            .locationId(locations.getFirst().getId())
            .expectQty(2)
            .pickedQty(0)
            .build();
        pickingTask.setPickingTaskItems(List.of(taskItem));

        UpdatePickingTaskCommand command = new UpdatePickingTaskCommand();
        UpdatePickingTaskItemDto updatePickingTaskItemDto = new UpdatePickingTaskItemDto();
        updatePickingTaskItemDto.setErrorInfo("Test error info");
        updatePickingTaskItemDto.setPickedQty(0);
        updatePickingTaskItemDto.setId(taskItem.getId());
        command.setUpdatePickingTaskItemDtos(List.of(updatePickingTaskItemDto));

        PickingTask mockPickingTask = mock(PickingTask.class);
        when(mockPickingTask.getId()).thenReturn(pickingTask.getId());
        when(mockPickingTask.getStatus()).thenReturn(PickingTaskStatus.FAILED);

        when(pickingTaskRepository.findById(pickingTask.getId())).thenReturn(mockPickingTask);
        when(locationCache.getLocationMap()).thenReturn(Map.of());
        when(pickingTaskRepository.update(mockPickingTask)).thenReturn(mockPickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(mockPickingTask)).thenReturn(pickingTaskDto);
        when(pickingTaskDto.getStatus()).thenReturn(PickingTaskStatus.FAILED);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTask.getId(), command);

        verify(mockPickingTask).update(eq(command), any());
        assertEquals(PickingTaskStatus.FAILED, result.getStatus());
    }

    @Test
    void updatePickingTask_TaskNotFound_ThrowsException() {
        UUID pickingTaskId = UUID.randomUUID();
        UpdatePickingTaskCommand command = mock(UpdatePickingTaskCommand.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        assertThrows(ResourceNotFoundException.class, () -> pickingTaskApplicationService.updatePickingTask(pickingTaskId, command));
    }

    @Test
    void updatePickingTask_LocationMapEmpty_UpdatesWithoutError() {
        UUID pickingTaskId = UUID.randomUUID();
        UpdatePickingTaskCommand command = mock(UpdatePickingTaskCommand.class);
        PickingTask pickingTask = mock(PickingTask.class);
        PickingTask updatedPickingTask = mock(PickingTask.class);
        PickingTaskDto pickingTaskDto = mock(PickingTaskDto.class);

        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(locationCache.getLocationMap()).thenReturn(Map.of());
        when(pickingTaskRepository.update(pickingTask)).thenReturn(updatedPickingTask);
        when(pickingTaskDtoApplicationMapper.domainToDto(updatedPickingTask)).thenReturn(pickingTaskDto);

        PickingTaskDto result = pickingTaskApplicationService.updatePickingTask(pickingTaskId, command);

        verify(pickingTask).update(command, Map.of());
        assertEquals(pickingTaskDto, result);
    }

    @Test
    void batchUnassignPickingTasks_should_unassign_only_assigned_tasks_and_dispatch_event() {
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        PickingTask assignedTask = mock(PickingTask.class);
        PickingTask notAssignedTask = mock(PickingTask.class);
        PickingTaskDto assignedDto = mock(PickingTaskDto.class);

        when(assignedTask.getStatus()).thenReturn(PickingTaskStatus.ASSIGNED);
        when(notAssignedTask.getStatus()).thenReturn(PickingTaskStatus.CREATED);
        when(pickingTaskRepository.findByIds(List.of(id1, id2))).thenReturn(List.of(assignedTask, notAssignedTask));
        when(pickingTaskRepository.saveAll(List.of(assignedTask))).thenReturn(List.of(assignedTask));
        when(pickingTaskDtoApplicationMapper.domainToDtos(List.of(assignedTask))).thenReturn(List.of(assignedDto));

        List<PickingTaskDto> result = pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1, id2));

        verify(assignedTask, times(1)).unassignTask();
        verify(notAssignedTask, never()).unassignTask();
        verify(businessEventDispatcher, times(1)).dispatch(any());
        assertEquals(1, result.size());
        Assertions.assertSame(assignedDto, result.getFirst());
    }

    @Test
    void batchUnassignPickingTasks_should_return_empty_when_no_assigned_tasks() {
        UUID id1 = UUID.randomUUID();
        PickingTask notAssignedTask = mock(PickingTask.class);
        when(notAssignedTask.getStatus()).thenReturn(PickingTaskStatus.CREATED);
        when(pickingTaskRepository.findByIds(List.of(id1))).thenReturn(List.of(notAssignedTask));

        List<PickingTaskDto> result = pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1));

        verify(notAssignedTask, never()).unassignTask();
        verify(businessEventDispatcher, never()).dispatch(any());
        assertTrue(result.isEmpty());
    }

    @Test
    void batchUnassignPickingTasks_should_throw_when_no_tasks_found() {
        UUID id1 = UUID.randomUUID();
        when(pickingTaskRepository.findByIds(List.of(id1))).thenReturn(Collections.emptyList());

        assertThrows(org.apache.kafka.common.errors.ResourceNotFoundException.class,
                () -> pickingTaskApplicationService.batchUnassignPickingTasks(List.of(id1)));
    }

    private FinaleTransferShipmentResponse buildFinaleTransferShipmentResponse() {
        return FinaleTransferShipmentResponse.builder()
            .shipmentId("218277").build();
    }

}