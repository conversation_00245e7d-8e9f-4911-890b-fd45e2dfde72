package com.mercaso.wms.delivery.interfaces.search;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryTaskView;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import com.mercaso.wms.delivery.utils.SearchDeliveryTaskResourceApi;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class SearchDeliveryTaskResourceIT extends AbstractIT {

    @Autowired
    private SearchDeliveryTaskResourceApi searchDeliveryTaskResourceApi;

    @Autowired
    private DeliveryTaskJpaDao deliveryTaskJpaDao;

    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;

    private LocalDate testDeliveryDate;

    @BeforeEach
    void setUp() {
        testDeliveryDate = LocalDate.now();
        deliveryTaskJpaDao.deleteAll();
    }

    @Test
    void should_search_delivery_tasks_by_date_successfully() throws Exception {
        // Prepare test data using direct database access
        DeliveryTaskDo taskDo1 = buildDeliveryTaskDo("TASK-001", testDeliveryDate.plusDays(1), "TRUCK-001", "Driver1");
        DeliveryTaskDo taskDo2 = buildDeliveryTaskDo("TASK-002", testDeliveryDate.plusDays(2), "TRUCK-002", "Driver2");
        DeliveryTaskDo taskDo3 = buildDeliveryTaskDo("TASK-003", testDeliveryDate, "TRUCK-003", "Driver3");

        DeliveryTaskDo savedTask1 = deliveryTaskJpaDao.save(taskDo1);
        DeliveryTaskDo savedTask2 = deliveryTaskJpaDao.save(taskDo2);
        DeliveryTaskDo savedTask3 = deliveryTaskJpaDao.save(taskDo3);

        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findAllByOrderNumberIn(Collections.singleton(shopifyOrderDto.getName()))
            .stream()
            .findFirst()
            .get();

        deliveryOrderDo.setArrivedAt(Instant.now());
        deliveryOrderDo.setPlanArriveAt(Instant.now().plus(30, ChronoUnit.MINUTES));
        deliveryOrderDo.setDeliveryTaskId(savedTask3.getId());
        deliveryOrderDo.setStatus(DeliveryOrderStatus.IN_TRANSIT);
        DeliveryOrderDo savedOrder = deliveryOrderJpaDao.save(deliveryOrderDo);

        // Test search by delivery date - individual query to isolate issue
        Result<SearchDeliveryTaskView> searchByDate = searchDeliveryTaskResourceApi.search(null,
            null,
            testDeliveryDate,
            null,
            null,
            null,
            List.of(SortType.ACTIVE_ORDER_DELAY_MINUTES_ASC, SortType.DELIVERY_DATE_ASC),
            null);
        assertEquals(1, searchByDate.getData().size());
        assert savedOrder.getDeliveryTaskId().equals(savedTask3.getId());
        assert searchByDate.getData().getFirst().getId().equals(savedTask3.getId());
        assert searchByDate.getData().getFirst().getActiveOrderId().equals(savedOrder.getId());
        assert searchByDate.getData().getFirst().getActiveOrderDelayMinutes() > -35;
        assert searchByDate.getData().getFirst().getActiveOrderDelayMinutes() < -25;

        // Try searching by task number instead
        Result<SearchDeliveryTaskView> searchByNumber = searchDeliveryTaskResourceApi.search(List.of(savedTask1.getNumber()),
            null,
            null,
            null,
            null,
            null,
            null,
            null);
        assertEquals(1, searchByNumber.getData().size());
        assert searchByNumber.getData().getFirst().getId().equals(savedTask1.getId());

        // Try searching without specifying criteria to return all tasks
        Result<SearchDeliveryTaskView> searchAll = searchDeliveryTaskResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
        log.info("Search all result: {}", searchAll);
        assertEquals(3, searchAll.getData().size());
        assert searchAll.getData().getFirst().getId().equals(savedTask2.getId());
        assert searchAll.getData().getLast().getId().equals(savedTask3.getId());

        // Check what's in the database again
        List<DeliveryTaskDo> tasksInDb = deliveryTaskJpaDao.findAll();
        log.info("Tasks in database: {}", tasksInDb);

        // Try searching without specifying criteria to return all tasks
        Result<SearchDeliveryTaskView> byDriverUserIdsResult = searchDeliveryTaskResourceApi.search(null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(taskDo1.getDriverUserId()));
        assert !byDriverUserIdsResult.getData().isEmpty();
    }
} 