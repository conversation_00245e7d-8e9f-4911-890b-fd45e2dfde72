package com.mercaso.wms.batch.service;

import static com.mercaso.wms.utils.MockDataUtils.buildInventoryStocks;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrders;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.wms.application.dto.BatchDto;
import com.mercaso.wms.application.service.BatchItemApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.batch.strategy.PopulateBreakdownStrategy;
import com.mercaso.wms.batch.writer.TemplateWriterService;
import com.mercaso.wms.builder.DataBuilder;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.infrastructure.excel.ReadExcelService;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.utils.MockDataUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class BatchDoServiceTest {

    private final PopulateBreakdownStrategy fullBreakdownPopulateStrategy = Mockito.mock(PopulateBreakdownStrategy.class);

    private final PopulateBreakdownStrategy fullBreakdownSmallPopulateStrategy = Mockito.mock(PopulateBreakdownStrategy.class);

    private final DocumentOperations documentOperations = Mockito.mock(DocumentOperations.class);

    private final PopulateStrategyService populateStrategyService = Mockito.mock(PopulateStrategyService.class);

    private final LocationRepository locationRepository = Mockito.mock(LocationRepository.class);

    private final ReadExcelService readExcelService = Mockito.mock(ReadExcelService.class);

    private final ImsAdaptor imsAdaptor = Mockito.mock(ImsAdaptor.class);

    private final TemplateWriterService templateWriterService = mock(TemplateWriterService.class);

    private final BatchItemApplicationService batchItemApplicationService = Mockito.mock(BatchItemApplicationService.class);

    private final ShippingOrderApplicationService shippingOrderApplicationService = Mockito.mock(ShippingOrderApplicationService.class);

    private final BatchRepository batchRepository = Mockito.mock(BatchRepository.class);

    private final ShippingOrderRepository shippingOrderRepository = Mockito.mock(ShippingOrderRepository.class);

    private final PickingTaskApplicationEventListener pickingTaskApplicationEventListener = Mockito.mock(
        PickingTaskApplicationEventListener.class);

    private final InventoryStockRepository inventoryStockRepository = Mockito.mock(InventoryStockRepository.class);

    private final WarehouseRepository warehouseRepository = Mockito.mock(WarehouseRepository.class);

    private final PgAdvisoryLock pgAdvisoryLock = Mockito.mock(PgAdvisoryLock.class);

    private final BatchService batchService = new BatchService(
        fullBreakdownPopulateStrategy,
        fullBreakdownSmallPopulateStrategy,
        shippingOrderApplicationService,
        documentOperations,
        populateStrategyService,
        locationRepository,
        readExcelService,
        imsAdaptor,
        templateWriterService,
        batchItemApplicationService,
        batchRepository,
        shippingOrderRepository,
        pickingTaskApplicationEventListener,
        inventoryStockRepository,
        warehouseRepository,
        pgAdvisoryLock);

    @BeforeEach
    void setUp() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any())).thenReturn(true);
    }

    @Test
    void when_shopify_order_is_empty_then_throw_exception() {
        when(shippingOrderApplicationService.findActiveShippingOrdersByDeliveryDate(any())).thenReturn(List.of());
        when(warehouseRepository.findByName(anyString())).thenReturn(buildWarehouse(UUID.randomUUID()));
        CreateBatchDto createBatchDto = new CreateBatchDto();
        createBatchDto.setTaggedWith(LocalDate.parse("2024-08-01"));
        try {
            batchService.createBatch(createBatchDto);
        } catch (Exception e) {
            assertEquals("No shopify orders or stock data found.", e.getMessage());
        }
    }

    @Test
    void when_finale_stock_is_empty_then_throw_exception() {
        when(warehouseRepository.findByName(anyString())).thenReturn(buildWarehouse(UUID.randomUUID()));
        CreateBatchDto createBatchDto = new CreateBatchDto();
        createBatchDto.setTaggedWith(LocalDate.parse("2024-08-01"));
        try {
            batchService.createBatch(createBatchDto);
        } catch (Exception e) {
            assertEquals("No shopify orders or stock data found.", e.getMessage());
        }
    }

    @Test
    void when_create_batch_and_use_ims_data_to_populate_should_success() {
        when(shippingOrderApplicationService.findActiveShippingOrdersByDeliveryDate(any())).thenReturn(buildShippingOrders(10,
            ShippingOrderStatus.OPEN));
        when(locationRepository.findAll()).thenReturn(DataBuilder.buildLocations(10, LocationType.BIN));
        BatchDto batchDo = new BatchDto();
        batchDo.setId(UUID.randomUUID());
        batchDo.setStatus(BatchStatus.CREATED);
        batchDo.setTag("2024-08-01");
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(MockDataUtils.buildItemCategoryDtoList(UUID.randomUUID().toString()));
        when(documentOperations.downloadDocument(any())).thenReturn(new byte[0]);
        when(templateWriterService.writeTemplate(any())).thenReturn(batchDo);
        when(readExcelService.isSheetExists(any(), any())).thenReturn(true);
        when(warehouseRepository.findByName(anyString())).thenReturn(buildWarehouse(UUID.randomUUID()));
        when(inventoryStockRepository.findByWarehouseIdAndLocationTypes(any(), any())).thenReturn(buildInventoryStocks(10));
        when(batchRepository.findById(any())).thenReturn(Batch.builder()
            .id(UUID.randomUUID())
            .build());

        CreateBatchDto createBatchDto = new CreateBatchDto();
        createBatchDto.setTaggedWith(LocalDate.parse("2024-08-01"));
        createBatchDto.setFileNames(List.of("1-Mercaso Pick Sheet Template_V7.8_1.xlsx"));
        Response<BatchDto> batchDto = batchService.createBatch(createBatchDto);

        assertEquals(BatchStatus.CREATED, batchDto.getData().getFirst().getStatus());
        assertEquals("2024-08-01", batchDto.getData().getFirst().getTag());
    }

    @Test
    void saveBatchItems_WhenOnlyPickingTaskDtos_ShouldSaveOnlyPickingTasks() {

        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.OPEN);
        shippingOrders.getFirst().getShippingOrderItems().getFirst().setSkuNumber("SKU1");
        // Arrange
        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(new ArrayList<>())
            .build();

        BatchDto batchDto = BatchDto.builder()
            .id(UUID.randomUUID())
            .build();

        // Act
        batchService.saveBatchItems(condition, batchDto, shippingOrders);

        // Assert
        verify(batchItemApplicationService, times(0)).createBatchItems(any());
    }

    @Test
    void saveBatchItems_WhenOnlyExcelBatchDtos_ShouldSaveOnlyRemainingBatchDtos() {
        // Arrange
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.OPEN);
        shippingOrders.getFirst().getShippingOrderItems().getFirst().setSkuNumber("SKU1");

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(createExcelBatchDto("SKU1")))
            .build();

        BatchDto batchDto = BatchDto.builder()
            .id(UUID.randomUUID())
            .build();

        // Act
        batchService.saveBatchItems(condition, batchDto, shippingOrders);

        // Assert
        verify(batchItemApplicationService, times(1)).createBatchItems(any());
    }

    @Test
    void saveBatchItems_WhenBothDtosPresent_ShouldSaveBoth() {
        // Arrange
        ExcelBatchDto pickingDto = createExcelBatchDto("SKU1");
        ExcelBatchDto excelDto = createExcelBatchDto("SKU2");
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.OPEN);
        shippingOrders.getFirst().getShippingOrderItems().getFirst().setSkuNumber("SKU1");
        shippingOrders.getLast().getShippingOrderItems().getLast().setSkuNumber("SKU2");

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(pickingDto, excelDto))
            .build();

        BatchDto batchDto = BatchDto.builder().id(UUID.randomUUID()).build();

        // Act
        batchService.saveBatchItems(condition, batchDto, shippingOrders);

        // Assert
        verify(batchItemApplicationService, times(1)).createBatchItems(any());
    }

    @Test
    void saveBatchItems_WhenEmptyCondition_ShouldNotCallCreateBatchItems() {
        // Arrange
        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(new ArrayList<>())
            .build();

        BatchDto batchDto = BatchDto.builder().id(UUID.randomUUID()).build();

        // Act
        batchService.saveBatchItems(condition, batchDto, Collections.emptyList());

        // Assert
        verify(batchItemApplicationService, never()).createBatchItems(any());
    }

    private ExcelBatchDto createExcelBatchDto(String sku) {
        return ExcelBatchDto.builder()
            .itemNumber(sku)
            .build();
    }

    @Test
    void updateShippingOrders_ShouldUpdateBreakdownLocations() {
        // Given
        List<ExcelBatchDto> excelBatchDtos = List.of(
            ExcelBatchDto.builder()
                .orderNumber("ORDER-001")
                .breakdownLocationId(UUID.randomUUID())
                .build(),
            ExcelBatchDto.builder()
                .orderNumber("ORDER-002")
                .breakdownLocationId(UUID.randomUUID())
                .build()
        );

        Location location1 = Location.builder().id(excelBatchDtos.get(0).getBreakdownLocationId()).build();

        Location location2 = Location.builder().id(excelBatchDtos.get(1).getBreakdownLocationId()).build();

        List<Location> locations = List.of(location1, location2);

        ShippingOrder order1 = ShippingOrder.builder().orderNumber("ORDER-001").build();

        ShippingOrder order2 = ShippingOrder.builder().orderNumber("ORDER-002").build();

        List<ShippingOrder> shippingOrders = List.of(order1, order2);

        Batch batch = Batch.builder().id(UUID.randomUUID()).build();

        // When
        batchService.updateShippingOrders(excelBatchDtos, shippingOrders, batch, locations);

        // Then
        verify(shippingOrderRepository, times(1)).saveAll(any());
    }

    @Test
    void updateShippingOrders_WithMissingLocation_ShouldNotSetBreakdownLocation() {
        // Given
        UUID nonExistentLocationId = UUID.randomUUID();
        List<ExcelBatchDto> excelBatchDtos = List.of(
            ExcelBatchDto.builder()
                .orderNumber("ORDER-001")
                .breakdownLocationId(nonExistentLocationId)
                .build()
        );

        List<Location> locations = List.of(); // Empty locations list

        ShippingOrder order = ShippingOrder.builder().orderNumber("ORDER-001").build();
        List<ShippingOrder> shippingOrders = List.of(order);

        Batch batch = Batch.builder().id(UUID.randomUUID()).build();

        // When
        batchService.updateShippingOrders(excelBatchDtos, shippingOrders, batch, locations);

        // Then
        verify(shippingOrderRepository, times(1)).saveAll(any());
    }

}