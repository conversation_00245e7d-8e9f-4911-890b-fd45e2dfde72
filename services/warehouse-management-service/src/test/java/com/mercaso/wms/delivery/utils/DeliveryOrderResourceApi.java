package com.mercaso.wms.delivery.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryOrderView;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class DeliveryOrderResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_DELIVERY_ORDER_URL = "/delivery/search/delivery-orders";

    private static final String GET_DELIVERY_ORDER_BY_ID_URL = "/delivery/query/delivery-orders/%s";

    private static final String UPDATE_DELIVERY_ORDER_TO_IN_TRANSIT_URL = "/delivery/delivery-orders/%s/in-transit";

    private static final String UPDATE_DELIVERY_ORDER_TO_ARRIVED_URL = "/delivery/delivery-orders/%s/arrive";

    private static final String UPDATE_DELIVERY_ORDER_TO_DELIVERED_URL = "/delivery/delivery-orders/%s/delivery";

    private static final String UPDATE_DELIVERY_ORDER_TO_UNLOAD_URL = "/delivery/delivery-orders/%s/unload";

    private static final String UPDATE_DELIVERY_ORDER_URL = "/delivery/delivery-orders/%s";

    private static final String GET_DELIVERY_ORDER_BY_ORDER_NUMBER_URL = "/delivery/query/delivery-orders/by-order-number/%s";

    public DeliveryOrderResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchDeliveryOrderView> search(
        List<String> orderNumbers,
        List<DeliveryOrderStatus> statuses,
        LocalDate deliveryDate,
        List<UUID> driverUserIds,
        List<String> truckNumbers,
        String paymentType,
        List<String> paymentStatuses,
        List<String> fulfillmentStatuses,
        String rescheduleType,
        Boolean issueOrder,
        List<SortType> sortTypes) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_DELIVERY_ORDER_URL + "?page=1&pageSize=20");
        if (CollectionUtils.isNotEmpty(orderNumbers)) {
            url.append("&orderNumbers=").append(String.join(",", orderNumbers));
        }
        if (CollectionUtils.isNotEmpty(statuses)) {
            url.append("&statuses=").append(statuses.stream().map(Enum::name).collect(Collectors.joining(",")));
        }
        if (deliveryDate != null) {
            url.append("&deliveryDate=").append(deliveryDate);
        }
        if (CollectionUtils.isNotEmpty(driverUserIds)) {
            url.append("&driverUserIds=").append(driverUserIds.stream().map(UUID::toString).collect(Collectors.joining(",")));
        }
        if (CollectionUtils.isNotEmpty(truckNumbers)) {
            url.append("&truckNumbers=").append(String.join(",", truckNumbers));
        }
        if (StringUtils.isNotEmpty(paymentType)) {
            url.append("&paymentType=").append(paymentType);
        }
        if (CollectionUtils.isNotEmpty(paymentStatuses)) {
            url.append("&paymentStatuses=").append(String.join(",", paymentStatuses));
        }
        if (CollectionUtils.isNotEmpty(fulfillmentStatuses)) {
            url.append("&fulfillmentStatuses=").append(String.join(",", fulfillmentStatuses));
        }
        if (CollectionUtils.isNotEmpty(sortTypes)) {
            url.append("&sortTypes=").append(sortTypes.stream().map(Enum::name).collect(Collectors.joining(",")));
        }
        if (rescheduleType != null) {
            url.append("&rescheduleType=").append(rescheduleType);
        }
        if (issueOrder != null) {
            url.append("&issueOrder=").append(issueOrder);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public DeliveryOrderDto findById(UUID id) {
        return getEntity(String.format(GET_DELIVERY_ORDER_BY_ID_URL, id), DeliveryOrderDto.class).getBody();
    }

    public DeliveryOrderDto updateToInTransit(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_DELIVERY_ORDER_TO_IN_TRANSIT_URL, id), null, DeliveryOrderDto.class);
    }

    public DeliveryOrderDto updateToArrived(UUID id) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_DELIVERY_ORDER_TO_ARRIVED_URL, id), null, DeliveryOrderDto.class);
    }

    public DeliveryOrderDto updateToDelivered(UUID id, DeliveryOrderDeliveredCommand command) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_DELIVERY_ORDER_TO_DELIVERED_URL, id), command, DeliveryOrderDto.class);
    }

    public DeliveryOrderDto updateToUnloaded(UUID id, DeliveryOrderUnloadCommand command) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_DELIVERY_ORDER_TO_UNLOAD_URL, id), command, DeliveryOrderDto.class);
    }

    public DeliveryOrderDto update(UUID id, UpdateDeliveryOrderCommand command) throws JsonProcessingException {
        return updateEntity(String.format(UPDATE_DELIVERY_ORDER_URL, id), command, DeliveryOrderDto.class);
    }

    public DeliveryOrderDto findByOrderNumber(String orderNumber) {
        return getEntity(String.format(GET_DELIVERY_ORDER_BY_ORDER_NUMBER_URL, orderNumber), DeliveryOrderDto.class).getBody();
    }
}
