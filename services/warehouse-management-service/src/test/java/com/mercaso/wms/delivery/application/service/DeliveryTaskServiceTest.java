package com.mercaso.wms.delivery.application.service;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildVehicleSettingMap;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoutesResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.CurrentRoutes;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Order;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Route;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.TrackingData;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Vehicle;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.VehicleSetting;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.infrastructure.utils.ManualNumberGenerate;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DeliveryTaskServiceTest {

    @Mock
    private RouteManagerAdaptor routeManagerAdaptor;

    @Mock
    private DeliveryTaskRepository deliveryTaskRepository;

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private RmRouteRepository rmRouteRepository;

    @Mock
    private BusinessEventDispatcher dispatcher;

    @Mock
    private RouteManagerService routeManagerService;

    @Mock
    private SlackDeliveryNotificationService slackDeliveryNotificationService;

    @Mock
    private ManualNumberGenerate manualNumberGenerate;

    @InjectMocks
    private DeliveryTaskService deliveryTaskService;

    @Captor
    private ArgumentCaptor<List<DeliveryOrder>> ordersCaptor;

    @Captor
    private ArgumentCaptor<RmRoute> rmRouteCaptor;

    private LocalDate testDate;
    private ApprovedRoutesResponse mockResponse;
    private List<DeliveryOrder> mockOrders;
    private List<Account> mockAccounts;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        testDate = LocalDate.now();
        setupMockData();
    }

    private void setupMockData() {
        mockResponse = new ApprovedRoutesResponse();
        List<ApprovedRoute> routes = new ArrayList<>();
        mockAccounts = new ArrayList<>();

        // Create routes with orders and steps
        ApprovedRoute route1 = createApprovedRoute("route-1", "********", "<EMAIL>");
        ApprovedRoute route2 = createApprovedRoute("route-2", "********", "<EMAIL>");
        ApprovedRoute route3 = createApprovedRoute("route-3", "********", null);
        routes.add(route1);
        routes.add(route2);
        routes.add(route3);

        mockResponse.setApprovedRoutes(routes);

        // Setup mock orders - use mocks instead of real objects to avoid state machine issues
        mockOrders = new ArrayList<>();
        for (ApprovedRoute route : routes) {
            for (Map.Entry<UUID, Order> entry : route.getOrders().entrySet()) {
                DeliveryOrder order = createMockDeliveryOrder(entry.getValue().getName());
                mockOrders.add(order);
            }
        }

        // Setup mock accounts (drivers)
        routes.forEach(route -> {
            String email = route.getDriver().getEmail();
            Account account = Account.builder()
                .userId(UUID.randomUUID())
                .email(email)
                .build();
            mockAccounts.add(account);
        });
    }

    private ApprovedRoute createApprovedRoute(String routeId, String date, String driverEmail) {
        ApprovedRoute approvedRoute = new ApprovedRoute();

        Route route = new Route();
        route.setId(routeId);
        route.setDate(date);
        route.setVehicleId("vehicle-" + routeId);

        List<Step> steps = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            Step step = new Step();
            step.setType("delivery");
            step.setOrderId("order-" + routeId + "-" + i);
            step.setDisplayLabel(i + "." + i);
            steps.add(step);
        }
        route.setSteps(steps);
        approvedRoute.setRoute(route);

        Map<UUID, Order> orders = new HashMap<>();
        for (Step step : steps) {
            UUID orderId = UUID.randomUUID();
            Order order = new Order();
            order.setId(step.getOrderId());
            String sequence = step.getDisplayLabel().split("\\.")[1];
            order.setName("order-" + routeId + "-" + sequence);
            orders.put(orderId, order);
        }
        approvedRoute.setOrders(orders);

        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setName("Driver-" + routeId);
        driver.setEmail(driverEmail);
        approvedRoute.setDriver(driver);

        Vehicle vehicle = new Vehicle();
        vehicle.setId("vehicle-" + routeId);
        vehicle.setExternalId("vehicle-" + routeId);

        Map<String, VehicleSetting> vehicleSettingMap = buildVehicleSettingMap(route.getOriginalDate());
        vehicle.setSettings(vehicleSettingMap);

        approvedRoute.setVehicle(vehicle);

        return approvedRoute;
    }

    private DeliveryOrder createMockDeliveryOrder(String orderNumber) {
        DeliveryOrder mockOrder = mock(DeliveryOrder.class);
        when(mockOrder.getOrderNumber()).thenReturn(orderNumber);
        when(mockOrder.getStatus()).thenReturn(DeliveryOrderStatus.CREATED);
        return mockOrder;
    }

    private DeliveryOrder createDeliveryOrder(String orderNumber) {
        return DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber(orderNumber)
            .status(DeliveryOrderStatus.CREATED)
            .build();
    }

    @Test
    void when_buildTasks_with_valid_routes_and_drivers_then_create_tasks_and_update_orders() {
        // Given
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);
        when(accountRepository.findAllByEmailIn(anyList())).thenReturn(mockAccounts);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(mockOrders);
        when(deliveryTaskRepository.save(any(DeliveryTask.class))).thenAnswer(i -> i.getArgument(0));
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));
        when(manualNumberGenerate.generateDeliveryTaskNumber()).thenReturn("TN-001");

        // When
        deliveryTaskService.buildTasks(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(accountRepository, times(1)).findAllByEmailIn(anyList());
        verify(deliveryTaskRepository, times(3)).save(any(DeliveryTask.class));
        verify(rmRouteRepository, times(3)).save(any(RmRoute.class));
    }

    @Test
    void when_buildTasks_but_no_drivers_found_then_pull_driver_create_tasks() {
        // Given
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);
        when(accountRepository.findAllByEmailIn(anyList())).thenReturn(new ArrayList<>());
        when(routeManagerService.pullFromRouteManager()).thenReturn(mockAccounts);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(mockOrders);
        when(deliveryTaskRepository.save(any(DeliveryTask.class))).thenAnswer(i -> i.getArgument(0));
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));
        when(routeManagerService.pullFromRouteManager()).thenReturn(mockAccounts);
        when(manualNumberGenerate.generateDeliveryTaskNumber()).thenReturn("TN-001");

        // When
        deliveryTaskService.buildTasks(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(accountRepository, times(1)).findAllByEmailIn(anyList());
        verify(deliveryTaskRepository, times(3)).save(any(DeliveryTask.class));
        verify(rmRouteRepository, times(3)).save(any(RmRoute.class));
        verify(deliveryOrderRepository, times(0)).saveAll(ordersCaptor.capture());
    }

    @Test
    void when_buildTasks_with_valid_routes_then_create_tasks_and_update_orders() {
        // Given
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);
        when(accountRepository.findAllByEmailIn(anyList())).thenReturn(mockAccounts);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(mockOrders);
        when(deliveryTaskRepository.save(any(DeliveryTask.class))).thenAnswer(i -> i.getArgument(0));
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));
        when(manualNumberGenerate.generateDeliveryTaskNumber()).thenReturn("TN-001");

        // When
        deliveryTaskService.buildTasks(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(accountRepository, times(1)).findAllByEmailIn(anyList());
        verify(deliveryTaskRepository, times(3)).save(any(DeliveryTask.class));
        verify(rmRouteRepository, times(3)).save(any(RmRoute.class));
        verify(deliveryOrderRepository, times(0)).saveAll(ordersCaptor.capture());
    }

    @Test
    void when_buildTasks_but_no_routes_found_then_do_nothing() {
        // Given
        mockResponse.setApprovedRoutes(new ArrayList<>());
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);

        // When
        try {
            deliveryTaskService.buildTasks(testDate);

        } catch (DeliveryBusinessException de) {
            assert de.getCode().equals(ErrorCodeEnums.DELIVERY_ROUTE_NOT_FOUND.getCode());
        }

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(deliveryTaskRepository, never()).save(any(DeliveryTask.class));
        verify(rmRouteRepository, never()).save(any(RmRoute.class));
        verify(deliveryOrderRepository, never()).saveAll(anyList());
    }

    @Test
    void when_buildTasks_but_orders_not_found_then_create_tasks_without_orders() {
        // Given
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);
        when(accountRepository.findAllByEmailIn(anyList())).thenReturn(mockAccounts);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(new ArrayList<>());
        when(deliveryTaskRepository.save(any(DeliveryTask.class))).thenAnswer(i -> i.getArgument(0));
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));
        when(manualNumberGenerate.generateDeliveryTaskNumber()).thenReturn("TN-001");

        // When
        deliveryTaskService.buildTasks(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(accountRepository, times(1)).findAllByEmailIn(anyList());
        verify(deliveryTaskRepository, times(3)).save(any(DeliveryTask.class));
        verify(rmRouteRepository, times(3)).save(any(RmRoute.class));
        verify(deliveryOrderRepository, never()).saveAll(anyList());
    }

    @Test
    void when_buildTasks_with_partially_matching_orders_then_update_only_matched_orders() {
        // Given
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);
        when(accountRepository.findAllByEmailIn(anyList())).thenReturn(mockAccounts);
        when(manualNumberGenerate.generateDeliveryTaskNumber()).thenReturn("TN-001");

        // Return only half of the orders
        List<DeliveryOrder> partialOrders = mockOrders.subList(0, mockOrders.size() / 2);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(partialOrders);
        when(deliveryTaskRepository.save(any(DeliveryTask.class))).thenAnswer(i -> i.getArgument(0));
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));

        // When
        deliveryTaskService.buildTasks(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(accountRepository, times(1)).findAllByEmailIn(anyList());
        verify(deliveryTaskRepository, times(3)).save(any(DeliveryTask.class));
        verify(rmRouteRepository, times(3)).save(any(RmRoute.class));
    }

    @Test
    void when_cleanupExistingTasksAndOrders_then_delete_routes_and_tasks() {
        // Given
        List<DeliveryTask> existingTasks = new ArrayList<>();
        DeliveryTask task1 = mock(DeliveryTask.class);
        DeliveryTask task2 = mock(DeliveryTask.class);
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        when(task1.getId()).thenReturn(id1);
        when(task2.getId()).thenReturn(id2);
        when(task1.getStatus()).thenReturn(DeliveryTaskStatus.CREATED);
        when(task2.getStatus()).thenReturn(DeliveryTaskStatus.CREATED);
        existingTasks.add(task1);
        existingTasks.add(task2);

        when(deliveryTaskRepository.findByDeliveryDate(any())).thenReturn(existingTasks);
        when(deliveryOrderRepository.findAllByDeliveryTaskIdIn(anyList())).thenReturn(new ArrayList<>());

        ApprovedRoutesResponse emptyResponse = new ApprovedRoutesResponse();
        emptyResponse.setApprovedRoutes(new ArrayList<>());
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(emptyResponse);

        // When
        try {
            deliveryTaskService.buildTasks(testDate);

        } catch (DeliveryBusinessException de) {
            assert de.getCode().equals(ErrorCodeEnums.DELIVERY_ROUTE_NOT_FOUND.getCode());
        }

        // Then
        verify(rmRouteRepository, times(1)).deleteAllByDeliveryTaskIdIn(any());
        verify(deliveryTaskRepository, times(1)).deleteAllByIdIn(any());
    }

    @Test
    void when_rebuildTask_with_driver_info_then_update_driver_successfully() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        String routeId = "route-1";
        String deliveryDate = "2024-04-01";
        String newDriverEmail = "<EMAIL>";

        // Setup task
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate(deliveryDate)
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.CREATED)
            .build();

        // Setup route
        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .routeId(routeId)
            .deliveryTaskId(taskId)
            .build();

        // Setup existing order
        DeliveryOrder existingOrder = mock(DeliveryOrder.class);
        when(existingOrder.getOrderNumber()).thenReturn("order-1");
        List<DeliveryOrder> existingOrders = List.of(existingOrder);

        // Create CurrentRoutes object instead of ApprovedRoute
        CurrentRoutes currentRoutes = new CurrentRoutes();

        // Set up route info
        Route route = new Route();
        route.setId(routeId);
        route.setDate(deliveryDate);

        // Set up steps
        List<Step> steps = new ArrayList<>();
        Step step = new Step();
        step.setType("delivery");
        step.setOrderId("order-id-1");
        step.setDisplayLabel("1.1");
        steps.add(step);
        route.setSteps(steps);

        // Add route to routes map
        Map<String, Route> routesMap = new HashMap<>();
        routesMap.put(routeId, route);
        currentRoutes.setRoutes(routesMap);

        // Set up orders
        Map<UUID, Order> orders = new HashMap<>();
        Order order = new Order();
        order.setId("order-id-1");
        order.setName("order-1");
        orders.put(UUID.randomUUID(), order);
        currentRoutes.setOrders(orders);

        // Set up driver
        Map<UUID, Driver> drivers = new HashMap<>();
        Driver driver = new Driver();
        driver.setEmail(newDriverEmail);
        driver.setName("New Driver");
        drivers.put(UUID.randomUUID(), driver);
        currentRoutes.setDrivers(drivers);

        // Set up vehicle
        Map<UUID, Vehicle> vehicles = new HashMap<>();
        Vehicle vehicle = new Vehicle();
        vehicle.setExternalId("vehicle-1");
        vehicles.put(UUID.randomUUID(), vehicle);
        currentRoutes.setVehicles(vehicles);

        // Setup driver accounts
        Account oldDriverAccount = Account.builder()
            .userId(driverUserId)
            .email("<EMAIL>")
            .userName("Old Driver")
            .build();

        Account newDriverAccount = Account.builder()
            .userId(UUID.randomUUID())
            .email(newDriverEmail)
            .userName("New Driver")
            .build();

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of(rmRoute));
        when(routeManagerAdaptor.getCurrentRoute(routeId)).thenReturn(currentRoutes);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(existingOrders);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(oldDriverAccount));
        when(accountRepository.findByEmail(newDriverEmail)).thenReturn(Optional.of(newDriverAccount));

        // Mock order operations (no changes)
        when(deliveryOrderRepository.updateAll(any())).thenReturn(existingOrders);

        // When
        boolean result = deliveryTaskService.rebuildTask(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(accountRepository, times(1)).findByEmail(newDriverEmail);

        // Verify result
        assert result;
    }

    @Test
    void when_rebuildTask_with_invalid_taskId_then_throw_exception() {
        // Given
        UUID taskId = UUID.randomUUID();
        when(deliveryTaskRepository.findById(taskId)).thenReturn(null);

        // When/Then
        try {
            deliveryTaskService.rebuildTask(taskId);
            assert false : "Expected exception was not thrown";
        } catch (Exception e) {
            // Expected exception
            assert e.getMessage().contains("Delivery task not found");
        }
    }

    @Test
    void when_rebuildTask_with_missing_route_then_throw_exception() {
        // Given
        UUID taskId = UUID.randomUUID();
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate("2024-04-01")
            .status(DeliveryTaskStatus.CREATED)
            .build();

        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of());

        // When/Then
        try {
            deliveryTaskService.rebuildTask(taskId);
            assert false : "Expected exception was not thrown";
        } catch (Exception e) {
            // Expected exception
            assert e.getMessage().contains("Delivery task route not found");
        }
    }

    @Test
    void when_rebuildTask_with_route_not_found_then_throw_exception() {
        // Given
        UUID taskId = UUID.randomUUID();
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate("2024-04-01")
            .status(DeliveryTaskStatus.CREATED)
            .build();

        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .routeId("non-existent-route")
            .deliveryTaskId(taskId)
            .build();

        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of(rmRoute));
        when(routeManagerAdaptor.getCurrentRoute("non-existent-route")).thenReturn(null);

        // When/Then
        try {
            deliveryTaskService.rebuildTask(taskId);
            assert false : "Expected exception was not thrown";
        } catch (Exception e) {
            // Expected exception
            assert e.getMessage().contains("Route with ID non-existent-route not found");
        }
    }

    @Test
    void when_rebuildTask_with_changes_then_update_add_and_remove_orders() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        String routeId = "route-1";
        String deliveryDate = "2024-04-01";

        // Setup task
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate(deliveryDate)
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.CREATED)
            .build();

        // Setup route
        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .routeId(routeId)
            .deliveryTaskId(taskId)
            .build();

        // Setup existing orders in task (order1, order2, order3)
        List<DeliveryOrder> existingOrders = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            DeliveryOrder order = createMockDeliveryOrder("order-" + routeId + "-" + i);
            existingOrders.add(order);
        }

        // Create CurrentRoutes object
        CurrentRoutes currentRoutes = new CurrentRoutes();

        // Setup route with updated information
        Route route = new Route();
        route.setId(routeId);
        route.setDate(deliveryDate);

        // New order set (order2, order4, order5) - order1 and order3 removed, order4 and order5 added
        Map<UUID, Order> updatedOrders = new HashMap<>();

        // Keep order2
        Order order2 = new Order();
        order2.setId("order-" + routeId + "-2");
        order2.setName("order-" + routeId + "-2");
        updatedOrders.put(UUID.randomUUID(), order2);

        // Add new orders
        Order order4 = new Order();
        order4.setId("order-" + routeId + "-4");
        order4.setName("order-" + routeId + "-4");
        updatedOrders.put(UUID.randomUUID(), order4);

        Order order5 = new Order();
        order5.setId("order-" + routeId + "-5");
        order5.setName("order-" + routeId + "-5");
        updatedOrders.put(UUID.randomUUID(), order5);

        currentRoutes.setOrders(updatedOrders);

        // Setup steps with new sequence
        List<Step> steps = new ArrayList<>();
        Step step2 = new Step();
        step2.setType("delivery");
        step2.setOrderId("order-" + routeId + "-2");
        step2.setDisplayLabel("1.2"); // New sequence 2 (was 2)
        steps.add(step2);

        Step step4 = new Step();
        step4.setType("delivery");
        step4.setOrderId("order-" + routeId + "-4");
        step4.setDisplayLabel("2.4");
        steps.add(step4);

        Step step5 = new Step();
        step5.setType("delivery");
        step5.setOrderId("order-" + routeId + "-5");
        step5.setDisplayLabel("3.5");
        steps.add(step5);

        route.setSteps(steps);

        // Add route to routes map
        Map<String, Route> routesMap = new HashMap<>();
        routesMap.put(routeId, route);
        currentRoutes.setRoutes(routesMap);

        // Add drivers
        Map<UUID, Driver> drivers = new HashMap<>();
        Driver driver = new Driver();
        driver.setEmail("<EMAIL>");
        drivers.put(UUID.randomUUID(), driver);
        currentRoutes.setDrivers(drivers);

        // Add vehicles
        Map<UUID, Vehicle> vehicles = new HashMap<>();
        Vehicle vehicle = new Vehicle();
        vehicle.setExternalId("vehicle-" + routeId);
        vehicles.put(UUID.randomUUID(), vehicle);
        currentRoutes.setVehicles(vehicles);

        // Create existing order map for mocked behavior
        Map<String, DeliveryOrder> existingOrderMap = new HashMap<>();
        for (DeliveryOrder order : existingOrders) {
            existingOrderMap.put(order.getOrderNumber(), order);
        }

        // Setup in-database orders for the new orders
        DeliveryOrder newOrder4 = createMockDeliveryOrder("order-" + routeId + "-4");
        DeliveryOrder newOrder5 = createMockDeliveryOrder("order-" + routeId + "-5");
        List<DeliveryOrder> newDbOrders = new ArrayList<>();
        newDbOrders.add(existingOrders.get(1)); // order2
        newDbOrders.add(newOrder4);
        newDbOrders.add(newOrder5);

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of(rmRoute));
        when(routeManagerAdaptor.getCurrentRoute(routeId)).thenReturn(currentRoutes);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(existingOrders);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(newDbOrders);
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));

        // Mock batch processing methods
        List<DeliveryOrderDo> savedDos = new ArrayList<>();
        savedDos.add(new DeliveryOrderDo());
        savedDos.add(new DeliveryOrderDo());
        when(deliveryOrderRepository.saveAll(any())).thenReturn(savedDos);
        when(deliveryOrderRepository.updateAll(any())).thenReturn(new ArrayList<>());

        // When
        boolean result = deliveryTaskService.rebuildTask(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(rmRouteRepository, times(1)).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor, times(1)).getCurrentRoute(routeId);
        verify(deliveryOrderRepository, times(1)).findAllByDeliveryTaskId(taskId);
        verify(deliveryOrderRepository, times(1)).findAllByOrderNumberIn(anySet());

        verify(deliveryOrderRepository, times(1)).updateAll(any());

        // Verify order1 and order3 were reverted
        for (DeliveryOrder order : existingOrders) {
            if (order.getOrderNumber().equals("order-" + routeId + "-1") ||
                order.getOrderNumber().equals("order-" + routeId + "-3")) {
                verify(order).revertToCreated(order);
            }
        }
    }

    @Test
    void when_rebuildTask_with_null_orders_then_remove_all_associations() {
        // Given
        UUID taskId = UUID.randomUUID();
        String routeId = "route-1";

        // Setup task
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate("2024-04-01")
            .status(DeliveryTaskStatus.CREATED)
            .build();

        // Setup route
        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .routeId(routeId)
            .deliveryTaskId(taskId)
            .build();

        // Setup existing orders
        List<DeliveryOrder> existingOrders = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            DeliveryOrder order = createMockDeliveryOrder("order-" + routeId + "-" + i);
            existingOrders.add(order);
        }

        // Create CurrentRoutes with empty orders
        CurrentRoutes currentRoutes = new CurrentRoutes();

        // Setup route information
        Route route = new Route();
        route.setId(routeId);
        route.setDate("********");
        route.setSteps(new ArrayList<>());

        // Add route to routes map
        Map<String, Route> routesMap = new HashMap<>();
        routesMap.put(routeId, route);
        currentRoutes.setRoutes(routesMap);

        // Use empty Map for orders
        currentRoutes.setOrders(new HashMap<>());

        // Add driver
        Map<UUID, Driver> drivers = new HashMap<>();
        Driver driver = new Driver();
        driver.setEmail("<EMAIL>");
        drivers.put(UUID.randomUUID(), driver);
        currentRoutes.setDrivers(drivers);

        // Add vehicle
        Map<UUID, Vehicle> vehicles = new HashMap<>();
        Vehicle vehicle = new Vehicle();
        vehicle.setExternalId("vehicle-" + routeId);
        Map<String, VehicleSetting> vehicleSettingMap = buildVehicleSettingMap(route.getOriginalDate());
        vehicle.setSettings(vehicleSettingMap);
        vehicles.put(UUID.randomUUID(), vehicle);
        currentRoutes.setVehicles(vehicles);

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of(rmRoute));
        when(routeManagerAdaptor.getCurrentRoute(routeId)).thenReturn(currentRoutes);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(existingOrders);
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));

        when(deliveryOrderRepository.updateAll(any())).thenReturn(existingOrders);

        // When
        boolean result = deliveryTaskService.rebuildTask(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(rmRouteRepository, times(1)).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor, times(1)).getCurrentRoute(routeId);
        verify(deliveryOrderRepository, times(1)).findAllByDeliveryTaskId(taskId);

        // Verify saveAll is not called because there are no new orders
        verify(deliveryOrderRepository, never()).saveAll(any());

        // Verify updateAll is called to remove all orders
        verify(deliveryOrderRepository, times(1)).updateAll(any());

        // Verify all existing orders are reverted
        for (DeliveryOrder order : existingOrders) {
            verify(order).revertToCreated(order);
        }

        // Verify result
        assert result;
    }

    @Test
    void when_update_delivery_task_to_in_progress_should_success() {
        // Given
        UUID taskId = UUID.randomUUID();
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .status(DeliveryTaskStatus.CREATED)
            .build();

        when(deliveryTaskRepository.findById(any())).thenReturn(task);
        task.setStatus(DeliveryTaskStatus.IN_PROGRESS);
        when(deliveryTaskRepository.update(any(DeliveryTask.class))).thenReturn(task);

        // When
        deliveryTaskService.inProgress(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        assert task.getStatus() == DeliveryTaskStatus.IN_PROGRESS;
    }

    @Test
    void when_batchCompleteTasks_with_valid_tasks_should_complete_all() {
        // Given
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();
        List<UUID> taskIds = List.of(taskId1, taskId2);

        // Setup mocked tasks
        DeliveryTask task1 = mock(DeliveryTask.class);
        when(task1.getId()).thenReturn(taskId1);
        when(task1.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        DeliveryTask task2 = mock(DeliveryTask.class);
        when(task2.getId()).thenReturn(taskId2);
        when(task2.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        // Mock orders for task1
        List<DeliveryOrder> orders1 = new ArrayList<>();
        DeliveryOrder order1 = mock(DeliveryOrder.class);
        when(order1.getStatus()).thenReturn(DeliveryOrderStatus.DELIVERED);
        orders1.add(order1);

        // Mock orders for task2
        List<DeliveryOrder> orders2 = new ArrayList<>();
        DeliveryOrder order2 = mock(DeliveryOrder.class);
        when(order2.getStatus()).thenReturn(DeliveryOrderStatus.CANCELED);
        orders2.add(order2);

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId1)).thenReturn(task1);
        when(deliveryTaskRepository.findById(taskId2)).thenReturn(task2);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId1)).thenReturn(orders1);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId2)).thenReturn(orders2);
        when(deliveryTaskRepository.saveAll(anyList())).thenReturn(List.of(task1, task2));

        // Override behavior of DeliveryTaskService to avoid dependency on mapper
        DeliveryTaskService serviceSpy = spy(deliveryTaskService);

        DeliveryTaskDto dto1 = new DeliveryTaskDto();
        dto1.setId(taskId1);
        DeliveryTaskDto dto2 = new DeliveryTaskDto();
        dto2.setId(taskId2);

        // When
        serviceSpy.batchCompleteTasks(taskIds);

        // Then - we can still verify that updateStatus was called
        verify(task1, times(1)).updateStatus(DeliveryTaskStatus.COMPLETED);
        verify(task2, times(1)).updateStatus(DeliveryTaskStatus.COMPLETED);
    }

    @Test
    void when_batchCompleteTasks_with_mixed_valid_invalid_tasks_should_complete_only_valid() {
        // Given
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();
        List<UUID> taskIds = List.of(taskId1, taskId2);

        // Setup mocked tasks
        DeliveryTask task1 = mock(DeliveryTask.class);
        when(task1.getId()).thenReturn(taskId1);
        when(task1.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        DeliveryTask task2 = mock(DeliveryTask.class);
        when(task2.getId()).thenReturn(taskId2);
        when(task2.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        // Mock orders for task1 - valid
        List<DeliveryOrder> orders1 = new ArrayList<>();
        DeliveryOrder order1 = mock(DeliveryOrder.class);
        when(order1.getStatus()).thenReturn(DeliveryOrderStatus.DELIVERED);
        orders1.add(order1);

        // Mock orders for task2 - invalid
        List<DeliveryOrder> orders2 = new ArrayList<>();
        DeliveryOrder order2 = mock(DeliveryOrder.class);
        when(order2.getStatus()).thenReturn(DeliveryOrderStatus.ASSIGNED); // Invalid status
        orders2.add(order2);

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId1)).thenReturn(task1);
        when(deliveryTaskRepository.findById(taskId2)).thenReturn(task2);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId1)).thenReturn(orders1);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId2)).thenReturn(orders2);
        when(deliveryTaskRepository.saveAll(anyList())).thenAnswer(i -> i.getArgument(0));

        // When
        deliveryTaskService.batchCompleteTasks(taskIds);

        // Then
        verify(task1, times(1)).updateStatus(DeliveryTaskStatus.COMPLETED);
        verify(task2, never()).updateStatus(any());
    }

    @Test
    void when_batchCompleteTasks_with_empty_list_should_return_empty_list() {
        // Given
        List<UUID> taskIds = new ArrayList<>();

        // When
        List<UUID> result = deliveryTaskService.batchCompleteTasks(taskIds);

        // Then
        assert result.isEmpty();
    }

    @Test
    void when_completeTask_with_valid_id_should_succeed() {
        // Given
        UUID taskId = UUID.randomUUID();
        DeliveryTask task = mock(DeliveryTask.class);
        when(task.getId()).thenReturn(taskId);
        when(task.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        // Mock orders with valid status for completion
        List<DeliveryOrder> orders = new ArrayList<>();
        DeliveryOrder order1 = mock(DeliveryOrder.class);
        when(order1.getStatus()).thenReturn(DeliveryOrderStatus.DELIVERED);
        DeliveryOrder order2 = mock(DeliveryOrder.class);
        when(order2.getStatus()).thenReturn(DeliveryOrderStatus.CANCELED);
        orders.add(order1);
        orders.add(order2);

        // Mock repository calls
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(orders);
        when(deliveryTaskRepository.update(any(DeliveryTask.class))).thenReturn(task);

        // Mock service to avoid actual calling of mapper
        DeliveryTaskService serviceSpy = spy(deliveryTaskService);

        // When
        serviceSpy.completeTask(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(deliveryOrderRepository, times(1)).findAllByDeliveryTaskId(taskId);
        verify(task, times(1)).updateStatus(DeliveryTaskStatus.COMPLETED);
        verify(deliveryTaskRepository, times(1)).update(task);
    }

    @Test
    void when_completeTask_with_orders_in_invalid_status_should_throw_exception() {
        // Given
        UUID taskId = UUID.randomUUID();
        DeliveryTask task = mock(DeliveryTask.class);
        when(task.getId()).thenReturn(taskId);
        when(task.getStatus()).thenReturn(DeliveryTaskStatus.IN_PROGRESS);

        // Mock orders with invalid status for completion
        List<DeliveryOrder> orders = new ArrayList<>();
        DeliveryOrder order1 = mock(DeliveryOrder.class);
        when(order1.getStatus()).thenReturn(DeliveryOrderStatus.DELIVERED);
        DeliveryOrder order2 = mock(DeliveryOrder.class);
        when(order2.getStatus()).thenReturn(DeliveryOrderStatus.ASSIGNED); // Invalid status
        orders.add(order1);
        orders.add(order2);

        // Mock repository calls
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(orders);

        // When/Then
        try {
            deliveryTaskService.completeTask(taskId);
            fail("Expected exception was not thrown");
        } catch (DeliveryBusinessException e) {
            // Verify that the exception has the expected message
            assertEquals(ErrorCodeEnums.DELIVERY_TASK_ORDERS_NOT_COMPLETE.getMessage(), e.getMessage());
            assertEquals(ErrorCodeEnums.DELIVERY_TASK_ORDERS_NOT_COMPLETE.getCode(), e.getCode());
        }

        // Verify
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(deliveryOrderRepository, times(1)).findAllByDeliveryTaskId(taskId);
        verify(deliveryTaskRepository, never()).update(any(DeliveryTask.class));
        verify(dispatcher, never()).dispatch(any());
    }

    @Test
    void when_extractOrders_with_rescheduled_orders_then_filter_them_out() {
        // Given
        UUID taskId = UUID.randomUUID();
        String routeId = "route-1";

        // Create ApprovedRoute with mixed orders (regular and rescheduled)
        ApprovedRoute approvedRoute = new ApprovedRoute();

        // Set up route info
        Route route = new Route();
        route.setId(routeId);

        // Create steps with tracking data
        List<Step> steps = new ArrayList<>();

        // Regular step (no reschedule)
        Step regularStep = new Step();
        regularStep.setType("delivery");
        regularStep.setOrderId("order-1");
        regularStep.setDisplayLabel("1.1");
        steps.add(regularStep);

        // Rescheduled step
        Step rescheduledStep = new Step();
        rescheduledStep.setType("delivery");
        rescheduledStep.setOrderId("order-2");
        rescheduledStep.setDisplayLabel("2.1");
        TrackingData trackingData = new TrackingData();
        trackingData.setStatus("reschedule"); // Not "reschedule", so it should be filtered out
        rescheduledStep.setTrackingData(trackingData);
        steps.add(rescheduledStep);

        // Another regular step (null tracking data)
        Step anotherRegularStep = new Step();
        anotherRegularStep.setType("delivery");
        anotherRegularStep.setOrderId("order-3");
        anotherRegularStep.setDisplayLabel("3.1");
        steps.add(anotherRegularStep);

        route.setSteps(steps);
        approvedRoute.setRoute(route);

        // Set up orders corresponding to steps
        Map<UUID, Order> orders = new HashMap<>();

        Order order1 = new Order();
        order1.setId("order-1");
        order1.setName("Regular Order 1");
        orders.put(UUID.randomUUID(), order1);

        Order order2 = new Order();
        order2.setId("order-2");
        order2.setName("Rescheduled Order");
        orders.put(UUID.randomUUID(), order2);

        Order order3 = new Order();
        order3.setId("order-3");
        order3.setName("Regular Order 2");
        orders.put(UUID.randomUUID(), order3);

        approvedRoute.setOrders(orders);

        // Create a spy to access private method
        DeliveryTaskService deliveryTaskServiceSpy = spy(deliveryTaskService);

        // When - Call private method through reflection
        Set<Order> result = null;
        try {
            Method extractOrdersMethod = DeliveryTaskService.class.getDeclaredMethod("extractOrders", ApprovedRoute.class);
            extractOrdersMethod.setAccessible(true);
            result = (Set<Order>) extractOrdersMethod.invoke(deliveryTaskServiceSpy, approvedRoute);
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify that only non-rescheduled orders remain
        boolean containsRescheduledOrder = result.stream()
            .anyMatch(order -> order.getName().equals("Rescheduled Order"));
        assertFalse(containsRescheduledOrder);

        // Verify that regular orders are included
        boolean containsRegularOrder1 = result.stream()
            .anyMatch(order -> order.getName().equals("Regular Order 1"));
        boolean containsRegularOrder2 = result.stream()
            .anyMatch(order -> order.getName().equals("Regular Order 2"));
        assertTrue(containsRegularOrder1);
        assertTrue(containsRegularOrder2);
    }

    @Test
    void when_extractRescheduledOrders_then_return_only_rescheduled_orders() {
        // Given
        String routeId = "route-1";

        // Create ApprovedRoute with mixed orders (regular and rescheduled)
        ApprovedRoute approvedRoute = new ApprovedRoute();

        // Set up route info
        Route route = new Route();
        route.setId(routeId);

        // Create steps with tracking data
        List<Step> steps = new ArrayList<>();

        // Regular step (no tracking data)
        Step regularStep = new Step();
        regularStep.setType("delivery");
        regularStep.setOrderId("order-1");
        regularStep.setDisplayLabel("1.1");
        steps.add(regularStep);

        // Regular step with tracking data not "reschedule"
        Step activeStep = new Step();
        activeStep.setType("delivery");
        activeStep.setOrderId("order-2");
        activeStep.setDisplayLabel("2.1");
        TrackingData activeTrackingData = new TrackingData();
        activeTrackingData.setStatus("active");
        activeStep.setTrackingData(activeTrackingData);
        steps.add(activeStep);

        // Rescheduled step
        Step rescheduledStep = new Step();
        rescheduledStep.setType("delivery");
        rescheduledStep.setOrderId("order-3");
        rescheduledStep.setDisplayLabel("3.1");
        TrackingData rescheduledTrackingData = new TrackingData();
        rescheduledTrackingData.setStatus("reschedule");
        rescheduledStep.setTrackingData(rescheduledTrackingData);
        steps.add(rescheduledStep);

        route.setSteps(steps);
        approvedRoute.setRoute(route);

        // Set up orders corresponding to steps
        Map<UUID, Order> orders = new HashMap<>();

        Order order1 = new Order();
        order1.setId("order-1");
        order1.setName("Regular Order No Data");
        orders.put(UUID.randomUUID(), order1);

        Order order2 = new Order();
        order2.setId("order-2");
        order2.setName("Active Order");
        orders.put(UUID.randomUUID(), order2);

        Order order3 = new Order();
        order3.setId("order-3");
        order3.setName("Rescheduled Order");
        orders.put(UUID.randomUUID(), order3);

        approvedRoute.setOrders(orders);

        // Create a spy to access private method
        DeliveryTaskService deliveryTaskServiceSpy = spy(deliveryTaskService);

        // When - Call private method through reflection
        Set<Order> result = null;
        try {
            Method extractRescheduledOrdersMethod = DeliveryTaskService.class.getDeclaredMethod("extractRescheduledOrders",
                ApprovedRoute.class);
            extractRescheduledOrdersMethod.setAccessible(true);
            result = (Set<Order>) extractRescheduledOrdersMethod.invoke(deliveryTaskServiceSpy, approvedRoute);
        } catch (Exception e) {
            fail("Failed to invoke private method: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify that only the rescheduled order is included
        boolean containsRescheduledOrder = result.stream()
            .anyMatch(order -> order.getName().equals("Rescheduled Order"));
        assertTrue(containsRescheduledOrder);

        // Verify that other orders are not included
        boolean containsRegularOrder = result.stream()
            .anyMatch(order -> order.getName().equals("Regular Order No Data"));
        boolean containsActiveOrder = result.stream()
            .anyMatch(order -> order.getName().equals("Active Order"));
        assertFalse(containsRegularOrder);
        assertFalse(containsActiveOrder);
    }

    @Test
    void when_rebuildTask_with_rescheduled_orders_then_handle_properly() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID driverUserId = UUID.randomUUID();
        String routeId = "route-1";
        String deliveryDate = "2024-04-01";

        // Setup task
        DeliveryTask task = DeliveryTask.builder()
            .id(taskId)
            .deliveryDate(deliveryDate)
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.CREATED)
            .build();

        // Setup route
        RmRoute rmRoute = RmRoute.builder()
            .id(UUID.randomUUID())
            .routeId(routeId)
            .deliveryTaskId(taskId)
            .build();

        // Setup existing orders in task
        List<DeliveryOrder> existingOrders = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            DeliveryOrder order = createMockDeliveryOrder("order-" + routeId + "-" + i);
            existingOrders.add(order);
        }

        // Create CurrentRoutes object
        CurrentRoutes currentRoutes = new CurrentRoutes();

        // Setup route
        Route route = new Route();
        route.setId(routeId);
        route.setDate(deliveryDate);

        // Setup orders (order1 normal, order2 rescheduled, order3 normal)
        Map<UUID, Order> updatedOrders = new HashMap<>();

        Order order1 = new Order();
        order1.setId("order-" + routeId + "-1");
        order1.setName("order-" + routeId + "-1");
        updatedOrders.put(UUID.randomUUID(), order1);

        Order order2 = new Order();
        order2.setId("order-" + routeId + "-2");
        order2.setName("order-" + routeId + "-2");
        updatedOrders.put(UUID.randomUUID(), order2);

        Order order3 = new Order();
        order3.setId("order-" + routeId + "-3");
        order3.setName("order-" + routeId + "-3");
        updatedOrders.put(UUID.randomUUID(), order3);

        currentRoutes.setOrders(updatedOrders);

        // Setup steps with tracking data
        List<Step> steps = new ArrayList<>();

        // Normal step
        Step step1 = new Step();
        step1.setType("delivery");
        step1.setOrderId("order-" + routeId + "-1");
        step1.setDisplayLabel("1.1");
        steps.add(step1);

        // Rescheduled step
        Step step2 = new Step();
        step2.setType("delivery");
        step2.setOrderId("order-" + routeId + "-2");
        step2.setDisplayLabel("2.1");
        TrackingData trackingData = new TrackingData();
        trackingData.setStatus("reschedule"); // Mark as rescheduled
        step2.setTrackingData(trackingData);
        steps.add(step2);

        // Normal step
        Step step3 = new Step();
        step3.setType("delivery");
        step3.setOrderId("order-" + routeId + "-3");
        step3.setDisplayLabel("3.1");
        steps.add(step3);

        route.setSteps(steps);

        // Add route to routes map
        Map<String, Route> routesMap = new HashMap<>();
        routesMap.put(routeId, route);
        currentRoutes.setRoutes(routesMap);

        // Add drivers
        Map<UUID, Driver> drivers = new HashMap<>();
        Driver driver = new Driver();
        driver.setEmail("<EMAIL>");
        drivers.put(UUID.randomUUID(), driver);
        currentRoutes.setDrivers(drivers);

        // Add vehicles
        Map<UUID, Vehicle> vehicles = new HashMap<>();
        Vehicle vehicle = new Vehicle();
        vehicle.setExternalId("vehicle-" + routeId);
        vehicles.put(UUID.randomUUID(), vehicle);
        currentRoutes.setVehicles(vehicles);

        // Mock repository responses
        when(deliveryTaskRepository.findById(taskId)).thenReturn(task);
        when(rmRouteRepository.findByDeliveryTaskId(taskId)).thenReturn(List.of(rmRoute));
        when(routeManagerAdaptor.getCurrentRoute(routeId)).thenReturn(currentRoutes);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(existingOrders);
        when(deliveryOrderRepository.findAllByOrderNumberIn(anyList())).thenReturn(existingOrders.subList(0,
            2)); // Return order1 and order3
        when(rmRouteRepository.save(any(RmRoute.class))).thenAnswer(i -> i.getArgument(0));

        // Mock batch processing methods
        when(deliveryOrderRepository.updateAll(any())).thenReturn(existingOrders);

        // When
        boolean result = deliveryTaskService.rebuildTask(taskId);

        // Then
        verify(deliveryTaskRepository, times(1)).findById(taskId);
        verify(rmRouteRepository, times(1)).findByDeliveryTaskId(taskId);
        verify(routeManagerAdaptor, times(1)).getCurrentRoute(routeId);
        verify(deliveryOrderRepository, times(1)).findAllByDeliveryTaskId(taskId);

        // Verify all order changes are processed
        verify(deliveryOrderRepository, times(1)).updateAll(any());

        // Verify order2 was reverted (because it's rescheduled)
        verify(existingOrders.get(1)).revertToCreated(existingOrders.get(1));

        // Verify result
        assertTrue(result);
    }

    @Test
    void when_automatedBuildTasks_but_no_routes_found_then_do_nothing() {
        // Given
        mockResponse.setApprovedRoutes(new ArrayList<>());
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);

        // When
        deliveryTaskService.buildTasksAutomatically(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(deliveryTaskRepository, never()).save(any(DeliveryTask.class));
        verify(rmRouteRepository, never()).save(any(RmRoute.class));
        verify(deliveryOrderRepository, never()).saveAll(anyList());
    }

    @Test
    void when_automatedBuildTasks_but_already_built_then_do_nothing() {
        // Given
        mockResponse.setApprovedRoutes(new ArrayList<>());
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(mockResponse);

        UUID taskId = UUID.randomUUID();
        DeliveryTask task = mock(DeliveryTask.class);
        when(task.getId()).thenReturn(taskId);
        when(task.getStatus()).thenReturn(DeliveryTaskStatus.CREATED);

        // Mock repository calls
        when(deliveryTaskRepository.findByDeliveryDate(testDate.format(DateUtils.DATE_TO_STRING_FORMATTER))).thenReturn(
            Collections.singletonList(task));

        // When
        deliveryTaskService.buildTasksAutomatically(testDate);

        // Then
        verify(routeManagerAdaptor, times(1)).getApprovedRoutesV2(anyList());
        verify(deliveryTaskRepository, never()).save(any(DeliveryTask.class));
        verify(rmRouteRepository, never()).save(any(RmRoute.class));
        verify(deliveryOrderRepository, never()).saveAll(anyList());
    }
}