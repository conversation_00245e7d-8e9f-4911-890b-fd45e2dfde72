<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="log.pattern"
        value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}) - [%tid] - [%X{username}] %cyan(%msg%n)"/>
    <!--  Since skywalking cannot identify the color code  -->
    <property name="skywalking.log.pattern"
        value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{10} - [%tid] - [%X{username}] %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="SKYWALKING" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${skywalking.log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="com.mercaso.businessevents" level="ERROR"/>
    <logger name="org.hibernate" level="OFF"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache" level="INFO"/>

    <springProfile name="local">
        <root level="DEBUG">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <springProfile name="integration">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>


    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <springProfile name="sat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <logger name="com.mercaso" level="INFO"/>

</configuration>