create table doc_records(
    id                  UUID            NOT NULL PRIMARY KEY,
    original            <PERSON><PERSON><PERSON><PERSON>,
    generated           J<PERSON><PERSON><PERSON>,
    created_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    created_by          <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    updated_by          <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at          TIMESTAMP,
    deleted_by          VARCHAR(255)
);

create table warehouse(
    id                  UUID            NOT NULL PRIMARY KEY,
    name                <PERSON><PERSON><PERSON><PERSON>(255)    NOT NULL,
    status              VARCHAR(255)    NOT NULL,
    created_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    created_by          <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    updated_by          <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at          TIMESTAMP,
    deleted_by          VARC<PERSON>R(255),
    unique(name)
);

create table location(
    id                  UUID            NOT NULL PRIMARY KEY,
    warehouse_id        UUID            NOT NULL,
    name                <PERSON><PERSON><PERSON><PERSON>(255)    NOT NULL,
    type                VA<PERSON><PERSON><PERSON>(255)    NOT NULL, -- bin or stock
    route_order         INTEGER         DEFAULT NULL, -- picking order
    created_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    created_by          <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at          TIMESTAMP       NOT NULL DEFAULT NOW(),
    updated_by          VARCHAR(255),
    deleted_at          TIMESTAMP,
    deleted_by          VARCHAR(255),
    unique(warehouse_id, name)
);
