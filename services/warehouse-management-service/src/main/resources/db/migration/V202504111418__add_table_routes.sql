CREATE TABLE IF NOT EXISTS da_rm_routes
(
    id               UUID        NOT NULL,
    route_id         VARCHAR(64) NOT NULL,
    vehicle_id       VARCHAR(64),
    driver_id        VARCHAR(64),
    delivery_task_id UUID,
    created_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at       TIMESTAMP,
    created_by       VARCHAR(64),
    updated_by       VARCHAR(64),
    deleted_by       VARCHAR(64),
    PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS idx_da_rm_routes_route_id ON da_rm_routes (delivery_task_id);

comment on table da_rm_routes is 'Table to store approved routes information from the route manager for delivery tasks';
comment on column da_rm_routes.route_id is 'Route ID for the delivery task';
comment on column da_rm_routes.vehicle_id is 'Vehicle ID assigned to the route';
comment on column da_rm_routes.driver_id is 'Driver ID assigned to the route';
comment on column da_rm_routes.delivery_task_id is 'Delivery task ID associated with the route';