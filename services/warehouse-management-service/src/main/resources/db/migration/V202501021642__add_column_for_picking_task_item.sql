ALTER table picking_task_items
    add COLUMN IF NOT EXISTS shipping_order_id UUID;

ALTER table picking_task_items
    add COLUMN IF NOT EXISTS shipping_order_item_id UUID;

create index IF NOT EXISTS picking_task_items_shipping_order_id_idx on picking_task_items (shipping_order_id);
create index IF NOT EXISTS picking_task_items_shipping_order_item_id_idx on picking_task_items (shipping_order_item_id);

COMMENT
    ON COLUMN picking_task_items.shipping_order_id IS 'Shopify order id';
COMMENT
    ON COLUMN picking_task_items.shipping_order_item_id IS 'Shopify order item id';