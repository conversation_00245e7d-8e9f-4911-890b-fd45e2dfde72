package com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.dataobject;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.locationtech.jts.geom.Point;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "da_latest_gps_cache")
@SQLDelete(sql = "update da_latest_gps_cache set deleted_at = current_timestamp where user_id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EntityListeners(AuditingEntityListener.class)
public class LatestGpsCacheDo {

    @Id
    @Column(name = "user_id")
    private UUID userId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "delivery_task_id")
    private UUID deliveryTaskId;

    @Column(name = "delivery_order_id")
    private UUID deliveryOrderId;

    @Column(name = "coordinates", columnDefinition = "geometry(Point,4326)")
    private Point coordinates;

    @Column(name = "accuracy", precision = 9, scale = 2)
    private BigDecimal accuracy;

    @Column(name = "speed", precision = 9, scale = 4)
    private BigDecimal speed;

    @Column(name = "heading", length = 20)
    private String heading;

    @Column(name = "report_at")
    private Instant reportAt;

    @Column(name = "created_at", updatable = false)
    @CreatedDate
    private Instant createdAt;

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    private String createdBy;

    @Version
    @Column(name = "updated_at")
    @LastModifiedDate
    private Instant updatedAt;

    @Column(name = "updated_by")
    @LastModifiedBy
    private String updatedBy;

    @Column(name = "deleted_at")
    private Instant deletedAt;

    @Column(name = "deleted_by")
    private String deletedBy;
} 