package com.mercaso.wms.infrastructure.statemachine.factory;

import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.statemachine.StateTransitionType;
import com.mercaso.wms.infrastructure.statemachine.StateType;
import com.mercaso.wms.infrastructure.statemachine.StatefulContext;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult.ResultType;
import org.springframework.statemachine.StateMachineException;
import reactor.core.publisher.Mono;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StateEventHandler {

    public static <S extends StateType, E extends StateTransitionType, T extends StatefulContext<S>>
    void processEvent(WmsStateMachineFactory<S, E, T> factory, T entity, E event) {
        StateMachine<S, E> stateMachine = factory.create(entity);

        try {
            stateMachine.sendEvent(Mono.just(MessageBuilder.withPayload(event).build()))
                .doOnNext(result -> {
                    if (result.getResultType() == ResultType.ACCEPTED) {
                        factory.updateInstanceState(stateMachine, entity);
                    } else {
                        handleErrors(entity, event.toString(), stateMachine.getInitialState().getId());
                    }
                })
                .blockLast();
        } finally {
            if (stateMachine != null) {
                stateMachine.stopReactively().subscribe();
            }
        }
    }

    private static void handleErrors(Object entity, String event, Object currentState) throws WmsBusinessException {

        log.warn("Failed to process event: {}, initState: {}, entity: {}", event, currentState, entity);

        if (entity instanceof PickingTask pickingTask && pickingTask.getStatus() == PickingTaskStatus.CANCELED) {
            throw new WmsBusinessException(ErrorCodeEnums.PICKING_TASK_CANCELED.getCode(),
                ErrorCodeEnums.PICKING_TASK_CANCELED.getMessage());
        }

        if (entity instanceof DeliveryTask deliveryTask) {

            throw new DeliveryBusinessException(ErrorCodeEnums.DELIVERY_TASK_INVALID_STATUS.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_INVALID_STATUS.getMessage().formatted(deliveryTask.getState()));
        }

        if (entity instanceof DeliveryOrder deliveryOrder) {

            throw new DeliveryBusinessException(ErrorCodeEnums.DELIVERY_TASK_INVALID_STATUS.getCode(),
                ErrorCodeEnums.DELIVERY_TASK_INVALID_STATUS.getMessage().formatted(deliveryOrder.getState()));
        }
        throw new StateMachineException(String.format("Failed to process event: %s", event));
    }
}
