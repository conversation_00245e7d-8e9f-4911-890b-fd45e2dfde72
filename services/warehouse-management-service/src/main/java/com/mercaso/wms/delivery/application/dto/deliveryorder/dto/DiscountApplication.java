package com.mercaso.wms.delivery.application.dto.deliveryorder.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DiscountApplication implements Serializable {

    private String code;
    private String type;
    private String targetType;
    private String valueType;

}
