package com.mercaso.wms.batch.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PickingTaskAssignmentProperties {

    @JsonProperty("auto-assignment")
    private Boolean autoAssignment;

    @JsonProperty("pickers")
    private List<Picker> pickers;

    @JsonProperty("smallBevragePicker")
    private Picker mfcSmallBevragePicker;

    @JsonProperty("specialBinsPicker")
    private Picker mfcSpecialBinsPicker;

    @JsonProperty("candyPickers")
    private List<Picker> mfcCandyPickers;

    private Picker mdcSmallBevragePicker;

    private Picker mdcSpecialBinsPicker;

    private List<Picker> mdcCandyPickers;

    @JsonProperty("target-qty")
    private Integer targetQty;

    @JsonProperty("tolerance")
    private Integer tolerance;

    @Getter
    @Setter
    @ToString
    public static class Picker {

        @JsonProperty("user-name")
        private String userName;
        @JsonProperty("user-id")
        private UUID userId;
    }
}