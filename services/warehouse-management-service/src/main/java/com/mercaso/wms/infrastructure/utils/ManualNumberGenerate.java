package com.mercaso.wms.infrastructure.utils;

import static com.mercaso.wms.infrastructure.utils.NumberGeneration.generateString;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ManualNumberGenerate {

    private final EntityManager entityManager;

    public String generateDeliveryTaskNumber() {
        return generateNumber("D-", "DeliveryTaskDo");
    }

    private String generateNumber(String prefix, String propertyName) {
        String sql = "SELECT count(1) FROM " + propertyName + " WHERE number = :number";
        TypedQuery<Long> query = entityManager.createQuery(sql, Long.class);
        String number;
        while (true) {
            number = generateString(prefix);
            Long result = query.setParameter("number", number).getSingleResult();
            if (result == 0) {
                break;
            } else {
                log.warn("Number already exists: {}", number);
            }
        }
        return number;
    }
}
