package com.mercaso.wms.domain.receivingtaskitem;


import com.mercaso.wms.domain.BaseDomain;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class ReceivingTaskItem extends BaseDomain {

    private final UUID id;

    private UUID receivingTaskId;

    private String orderNumber;

    private Integer line;

    private UUID batchItemId;

    private UUID itemId;

    private String department;

    private String category;

    private String skuNumber;

    private String title;

    private String locationName;

    private UUID locationId;

    private String aisleNumber;

    private Integer receivingSequence;

    private Integer expectQty;

    private Integer receivedQty;

    private String errorInfo;

    private String breakdownName;

    private UUID shippingOrderId;

    private UUID shippingOrderItemId;

    public void receive() {
        this.receivedQty = this.receivedQty + 1;
    }

}
