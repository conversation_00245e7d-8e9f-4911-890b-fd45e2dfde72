package com.mercaso.wms.application.mapper.warehouse;

import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.warehouse.Warehouse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface WarehouseDtoApplicationMapper extends BaseDtoApplicationMapper<Warehouse, WarehouseDto> {

    WarehouseDtoApplicationMapper INSTANCE = Mappers.getMapper(WarehouseDtoApplicationMapper.class);

}
