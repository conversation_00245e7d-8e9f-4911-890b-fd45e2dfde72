package com.mercaso.wms.delivery.application.dto.deliveryorder;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.application.dto.customer.CustomerDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountApplication;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryOrderDto extends BaseDto {

    private UUID id;
    private UUID warehouseId;
    private UUID deliveryTaskId;
    private Integer sequence;
    private DeliveryOrderStatus status;
    private String orderNumber;
    private String deliveryDate;
    private String deliveryTimeWindow;
    private AddressDto address;
    private CustomerDto customer;
    private List<PaymentType> paymentType;
    private String paymentStatus;
    private String fulfillmentStatus;
    private Instant planArriveAt;
    private Instant planDeliveryAt;
    private Instant inTransitAt;
    private Instant arrivedAt;
    private Instant unloadedAt;
    private Instant deliveredAt;
    private String customerNotes;
    private String notes;
    private BigDecimal originalTotalPrice;
    private BigDecimal totalPrice;
    private BigDecimal currentTotalDiscounts;
    private List<DiscountApplication> discountApplications;
    private String rescheduleType;
    private String shopifyOrderUrl;
    private List<DeliveryOrderItemDto> deliveryOrderItems;
    private LatestGpsCacheDto latestGps;
    private Instant createdAt;
    private String createdBy;
    private Instant updatedAt;
    private String updatedBy;
    private String rmOrderId;
}
