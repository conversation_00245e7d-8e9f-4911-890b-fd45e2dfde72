package com.mercaso.wms.infrastructure.config;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.AuditorAware;

@Configuration
@RequiredArgsConstructor
public class AuditorAwareImpl implements AuditorAware<String> {

    private final Environment environment;

    @Value("${account.system-user}")
    private String systemUserId;

    @Override
    public Optional<String> getCurrentAuditor() {
        String auditor;
        if (isIntegrationEnvironment()) {
            auditor = systemUserId;
        } else {
            auditor = SecurityContextUtil.getLoginUserId();
            if (auditor == null) {
                auditor = systemUserId;
            }
        }
        return Optional.of(auditor);
    }

    private boolean isIntegrationEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("integration".equals(profile)) {
                return true;
            }
        }
        return false;
    }

}
