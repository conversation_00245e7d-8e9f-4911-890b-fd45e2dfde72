package com.mercaso.wms.application.service;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.application.dto.event.ShippingOrderStartedPayloadDto;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import com.mercaso.wms.infrastructure.slackalert.FraudOrderAlert;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShippingOrderApplicationService {

    @Value("${fraud-order.ip}")
    private List<String> fraudIps;

    @Value("${fraud-order.device-id}")
    private List<String> fraudDeviceIds;

    @Value("${fraud-order.phone-number}")
    private List<String> fraudPhoneNumbers;

    private final ShippingOrderRepository shippingOrderRepository;

    private final PickingTaskRepository pickingTaskRepository;

    private final WarehouseRepository warehouseRepository;

    private final ImsAdaptor imsAdaptor;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final ReceivingTaskRepository receivingTaskRepository;

    private final ShippingOrderService shippingOrderService;

    private final FraudOrderAlert fraudOrderAlert;

    private static final String PAYMENT_TYPE_ACH = "ACH_DIRECT_DEBIT";

    @RetryableTransaction
    public void createOrUpdate(ShopifyOrderDto shopifyOrderDto) {
        boolean mfcOrder = ShippingOrder.builder().build().isMfcOrder(shopifyOrderDto.getTags());
        if (!mfcOrder) {
            log.info("Ignoring non-MFC order: {}", shopifyOrderDto.getName());
            return;
        }
        LocalDate deliveryDate = ShippingOrder.builder().build().convertDeliveryDate(shopifyOrderDto.getTags());
        if (deliveryDate != null && deliveryDate.isBefore(LocalDate.now().minusMonths(1))) {
            return;
        }
        if (shopifyOrderDto.getName() != null && shopifyOrderDto.getName().contains("M-")) {
            shopifyOrderDto.setName(shopifyOrderDto.getName().replaceFirst("M-", ""));
        }
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock((shopifyOrderDto.getName() + shopifyOrderDto.getId()).hashCode(),
            "ShippingOrderApplicationService.createOrUpdate");
        ShippingOrder shippingOrder = shippingOrderRepository.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());
        boolean updated;
        if (null == shippingOrder) {
            shippingOrder = ShippingOrder.builder().build().create(shopifyOrderDto);
            setWarehouse(shippingOrder);
            updated = false;
            // Check for fraudulent orders only if the order is not created
            detectFraudOrder(shopifyOrderDto);
        } else {
            shippingOrder.update(shopifyOrderDto);
            updated = true;
        }
        setFieldsFromIms(shippingOrder);
        shippingOrderService.setCustomerAddress(shippingOrder);
        ShippingOrderDto shippingOrderDto = shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.save(
            shippingOrder));
        shippingOrderService.saveBusinessEvent(shippingOrderDto, updated);
    }

    public void update(OutboundScanRecordDto dto) {
        ShippingOrder shippingOrder = shippingOrderRepository.findById(dto.getShippingOrderId());
        if (shippingOrder == null) {
            log.error("Shipping order not found with id: {}", dto.getShippingOrderId());
            return;
        }
        shippingOrder.picked(dto);
        shippingOrderService.saveBusinessEvent(shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.update(
            shippingOrder)), false);
    }

    @RetryableTransaction
    public void updateOrderStatusByReceivingTaskId(UUID receivingTaskId) {
        ReceivingTask receivingTask = receivingTaskRepository.findById(receivingTaskId);
        if (receivingTask == null || receivingTask.getReceivingTaskItems().isEmpty()) {
            log.error("[updateOrderStatusByReceivingTaskId] Receiving task not found with id: {}", receivingTaskId);
            return;
        }
        List<ShippingOrder> orders = shippingOrderRepository.findByOrderIds(
            receivingTask.getReceivingTaskItems().stream()
                .map(ReceivingTaskItem::getShippingOrderId)
                .distinct()
                .toList());
        if (CollectionUtils.isEmpty(orders)) {
            log.warn("[updateOrderStatusByReceivingTaskId] Shipping order not found for receiving task: {}", receivingTaskId);
            return;
        }
        orders.forEach(order -> {
            List<ReceivingTaskItem> receivedReceivingTaskItems = receivingTask.getReceivingTaskItems().stream()
                .filter(receivedItem -> receivedItem.getShippingOrderId().equals(order.getId()))
                .toList();
            order.received(receivedReceivingTaskItems);
            log.info("[updateOrderStatusByReceivingTaskId] Order status updated for order: {}, status: {}",
                order.getOrderNumber(),
                order.getStatus());
            shippingOrderService.saveBusinessEvent(shippingOrderDtoApplicationMapper.domainToDto(shippingOrderRepository.update(
                order)), false);
        });
    }

    public void updateOrderStatusByPickingTaskId(UUID pickingTaskId) {
        PickingTask pickingTask = pickingTaskRepository.findById(pickingTaskId);
        if (pickingTask == null || pickingTask.getPickingTaskItems().isEmpty()) {
            log.error("Picking task not found with id: {}", pickingTaskId);
            return;
        }
        if (pickingTask.getType() == PickingTaskType.ORDER) {
            updateOrderStatusByOrderLevelPickingTask(pickingTask);
        } else {
            updateOrderStatusByBatchLevelPickingTask(pickingTask);
        }
    }

    private void updateOrderStatusByOrderLevelPickingTask(PickingTask pickingTask) {
        log.info("[updateOrderStatusByOrderLevelPickingTask] order level picking task: {}", pickingTask.getId());
        List<ShippingOrder> shippingOrders = shippingOrderRepository.findByOrderIds(
            pickingTask.getPickingTaskItems().stream()
                .map(PickingTaskItem::getShippingOrderId)
                .distinct()
                .toList());
        if (CollectionUtils.isEmpty(shippingOrders)) {
            log.warn("[updateOrderStatusByOrderLevelPickingTask] Shipping order not found for order picking task: {}",
                pickingTask.getId());
            return;
        }
        shippingOrderService.updateSingleOrderWithRetry(shippingOrders.getFirst().getId(),
            pickingTask.getPickingTaskItems(),
            pickingTask.getId());
    }

    private void updateOrderStatusByBatchLevelPickingTask(PickingTask pickingTask) {
        log.info("[updateOrderStatusByBatchLevelPickingTask] batch level picking task: {}", pickingTask.getId());
        List<UUID> orderIds = pickingTask.getPickingTaskItems().stream()
            .map(PickingTaskItem::getShippingOrderId)
            .distinct()
            .toList();

        if (orderIds.isEmpty()) {
            log.warn("[updateOrderStatusByBatchLevelPickingTask] No orders found for batch picking task: {}",
                pickingTask.getId());
            return;
        }
        List<ShippingOrder> orders = shippingOrderRepository.findByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orders)) {
            log.warn("[updateOrderStatusByBatchLevelPickingTask] Shipping orders not found for batch picking task: {}", 
                pickingTask.getId());
            return;
        }
        for (ShippingOrder order : orders) {
            List<PickingTaskItem> pickedPickingTaskItems = pickingTask.getPickingTaskItems().stream()
                .filter(item -> item.getShippingOrderId().equals(order.getId()))
                .toList();
            if (pickedPickingTaskItems.isEmpty()) {
                continue;
            }
            try {
                shippingOrderService.updateSingleOrderWithRetry(order.getId(), pickedPickingTaskItems, pickingTask.getId());
            } catch (Exception e) {
                log.warn("[updateOrderStatusByBatchLevelPickingTask] Failed to update order status for order: {} taskId: {}",
                    order.getOrderNumber(),
                    pickingTask.getId(),
                    e);
            }
        }
    }

    private void setWarehouse(ShippingOrder shippingOrder) {
        shippingOrder.setWarehouse(warehouseRepository.findByName("MFC"));
    }

    private void setFieldsFromIms(ShippingOrder shippingOrder) {
        Set<String> skus = shippingOrder.getShippingOrderItems()
            .stream()
            .filter(shippingOrderItem -> shippingOrderItem.getItemId() == null)
            .map(ShippingOrderItem::getSkuNumber)
            .collect(Collectors.toSet());
        if (skus.isEmpty()) {
            log.warn("[createOrUpdate] No skus found for shopify order: {}", shippingOrder.getOrderNumber());
            return;
        }
        List<ItemCategoryDto> itemsBySkus = imsAdaptor.getItemsBySkus(skus.stream().toList());
        shippingOrder.getShippingOrderItems()
            .forEach(shippingOrderItem -> itemsBySkus.stream()
                .filter(itemCategory -> Objects.equals(itemCategory.getSkuNumber(), shippingOrderItem.getSkuNumber()))
                .findFirst()
                .ifPresent(itemCategoryDto -> {
                    shippingOrderItem.setItemId(itemCategoryDto.getId());
                    shippingOrderItem.setTitle(itemCategoryDto.getTitle());
                    shippingOrderItem.setDepartment(itemCategoryDto.getDepartment());
                    shippingOrderItem.setCategory(itemCategoryDto.getCategory());
                    shippingOrderItem.setSubCategory(itemCategoryDto.getSubCategory());
                    shippingOrderItem.setPrimaryVendorId(itemCategoryDto.getPrimaryVendorId());
                    shippingOrderItem.setBackupVendorId(itemCategoryDto.getBackupVendorId());
                    shippingOrderItem.setPrimaryVendorName(itemCategoryDto.getPrimaryVendorName());
                    shippingOrderItem.setBackupVendorName(itemCategoryDto.getBackupVendorName());
                    shippingOrderItem.setVersionNumber(itemCategoryDto.getVersionNumber());
                }));
    }

    public List<ShippingOrder> findActiveShippingOrdersByDeliveryDate(String deliveryDate) {
        return shippingOrderRepository.findActiveShippingOrdersByDeliveryDate(deliveryDate);
    }

    public void markInProgress(UUID batchId) {
        List<ShippingOrder> orders = shippingOrderRepository.findByBatchId(batchId);
        orders = orders.stream().filter(order -> order.getStatus() == ShippingOrderStatus.OPEN).toList();
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders.forEach(order -> {
            order.addToBatch();
            businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderStartedPayloadDto.builder()
                .shippingOrderId(order.getId())
                .data(shippingOrderDtoApplicationMapper.domainToDto(order))
                .build()));
        });
        shippingOrderRepository.saveAll(orders);
    }

    private void detectFraudOrder(ShopifyOrderDto shopifyOrderDto) {
        List<ShopifyOrderDto.NoteAttributeDto> noteAttributes = shopifyOrderDto.getNoteAttributes();
        if (CollectionUtils.isEmpty(noteAttributes)) {
            log.warn("[detectFraudOrder] Note attributes are null, shopify order: {}", shopifyOrderDto.getName());
            return;
        }

        String ip = getNoteAttributeValue(noteAttributes, "ip");
        String deviceId = getNoteAttributeValue(noteAttributes, "device_id");
        String phone = shopifyOrderDto.getShippingAddress().getPhone();
        String paymentType = getNoteAttributeValue(noteAttributes, "payment_type");

        boolean isFraudOrder = isFraudulent(ip, deviceId, phone, paymentType);

        if (isFraudOrder) {
            fraudOrderAlert.sendFraudOrderAlter(
                shopifyOrderDto.getName(), ip, deviceId, shopifyOrderDto.getId(), phone, paymentType, shopifyOrderDto.getNote()
            );
        }
    }

    private String getNoteAttributeValue(List<ShopifyOrderDto.NoteAttributeDto> noteAttributes, String attributeName) {
        return noteAttributes.stream()
            .filter(attr -> attr.getName().equals(attributeName))
            .map(ShopifyOrderDto.NoteAttributeDto::getValue)
            .findFirst()
            .orElse(null);
    }

    private boolean isFraudulent(String ip, String deviceId, String phone, String paymentType) {
        if ((StringUtils.isNotEmpty(ip) && fraudIps.contains(ip)) ||
            (StringUtils.isNotEmpty(deviceId) && fraudDeviceIds.contains(deviceId)) ||
            (StringUtils.isNotEmpty(phone) && fraudPhoneNumbers.contains(phone))) {
            log.info("[detectFraudOrder] Fraud order detected.");
            return true;
        }
        return StringUtils.isNotEmpty(paymentType) && PAYMENT_TYPE_ACH.equals(paymentType);
    }

}
