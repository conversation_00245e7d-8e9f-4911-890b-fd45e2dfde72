package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.PickingTaskDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PickingTaskUnassignedPayloadDto extends BusinessEventPayloadDto<PickingTaskDto> {

    private UUID pickingTaskId;

    @Builder
    public PickingTaskUnassignedPayloadDto(PickingTaskDto data, UUID pickingTaskId) {
        super(data);
        this.pickingTaskId = pickingTaskId;
    }
} 