package com.mercaso.wms.delivery.domain.latestgps;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface LatestGpsCacheRepository extends BaseDomainRepository<LatestGpsCache, UUID> {

    List<LatestGpsCache> findAllByRecentHour();

    Optional<LatestGpsCache> findByUserId(UUID userId);

    Optional<LatestGpsCache> findByDeliveryOrderId(UUID deliveryOrderId);
}