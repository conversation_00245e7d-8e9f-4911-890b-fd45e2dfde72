package com.mercaso.wms.delivery.domain.deliveryorder.enums;

import java.util.EnumSet;
import java.util.Set;

/*
BC = Business Closed
UTA = Unable to Access
AOG = Act of God (weather / power outage)
RET = Rejected / No longer Needed
MDW = Missed Delivery Window
NPA = No Payment Available
NRA = No Receiver Available
TI = Technical issue
PNO =（Pay Next Order）
SAO =（Same Address Order）
 */
public enum RescheduleType {
    LATER,
    NEXT_DAY,
    BC,
    UTA,
    AOG,
    RET,
    MDW,
    NPA,
    NRA,
    TI,
    PNO,
    SAO;

    public static final Set<RescheduleType> NEED_REMOVE = EnumSet.of(LATER,
        NEXT_DAY,
        BC,
        UTA,
        AOG,
        RET,
        MDW,
        NRA,
        TI,
        SAO
    );
}
