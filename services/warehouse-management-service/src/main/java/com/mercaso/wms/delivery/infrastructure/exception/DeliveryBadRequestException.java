package com.mercaso.wms.delivery.infrastructure.exception;

import lombok.Getter;

@Getter
public class DeliveryBadRequestException extends RuntimeException {

    private final String code;

    private final String message;

    public DeliveryBadRequestException(String message) {
        this.code = null;
        this.message = message;
    }

    public DeliveryBadRequestException(String code, String message) {
        super(String.format(message));
        this.code = code;
        this.message = message;
    }

    public DeliveryBadRequestException(String message, String code, Object... args) {
        super(String.format(message, args));
        this.code = code;
        this.message = message;
    }

    public DeliveryBadRequestException(String message, Throwable cause) {
        super(message, cause);
        this.code = null;
        this.message = message;
    }

}
