package com.mercaso.wms.delivery.domain.latestgps;

import com.mercaso.wms.delivery.domain.gps.GpsRaw;
import com.mercaso.wms.domain.BaseDomain;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class LatestGpsCache extends BaseDomain {

    private final UUID userId;
    private String userName;
    private UUID deliveryTaskId;
    private UUID deliveryOrderId;
    private Point coordinates;
    private BigDecimal accuracy;
    private BigDecimal speed;
    private String heading;
    private Instant reportAt;

    public LatestGpsCache create(GpsRaw gpsRaw) {
        return LatestGpsCache.builder()
            .userId(gpsRaw.getUserId())
            .userName(gpsRaw.getUserName())
            .deliveryTaskId(gpsRaw.getDeliveryTaskId())
            .deliveryOrderId(gpsRaw.getDeliveryOrderId())
            .coordinates(gpsRaw.getCoordinates())
            .accuracy(gpsRaw.getAccuracy())
            .speed(gpsRaw.getSpeed())
            .heading(gpsRaw.getHeading())
            .reportAt(gpsRaw.getReportAt())
            .build();
    }

    public LatestGpsCache update(GpsRaw gpsRaw) {
        this.userName = gpsRaw.getUserName();
        this.coordinates = gpsRaw.getCoordinates();
        this.accuracy = gpsRaw.getAccuracy();
        this.speed = gpsRaw.getSpeed();
        this.heading = gpsRaw.getHeading();
        this.reportAt = gpsRaw.getReportAt();
        this.deliveryTaskId = gpsRaw.getDeliveryTaskId();
        this.deliveryOrderId = gpsRaw.getDeliveryOrderId();
        return this;
    }
}
