package com.mercaso.wms.delivery.domain.deliveryorder;

import com.mercaso.wms.domain.BaseDomain;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class DeliveryOrderItem extends BaseDomain {

    private final UUID id;
    private UUID deliveryOrderId;
    private String shopifyOrderItemId;
    private UUID itemId;
    private String skuNumber;
    private String title;
    private BigDecimal qty;
    private BigDecimal currentQty;
    private BigDecimal deliveredQty;
    private Integer line;
    private BigDecimal price;
    private String discountAllocations;
    private BigDecimal crvPrice;
    private Integer packageSize;
    private boolean containsNicotine;
    private String reasonCode;
} 