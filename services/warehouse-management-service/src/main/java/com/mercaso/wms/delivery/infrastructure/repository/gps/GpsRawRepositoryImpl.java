package com.mercaso.wms.delivery.infrastructure.repository.gps;

import com.mercaso.wms.delivery.domain.gps.GpsRaw;
import com.mercaso.wms.delivery.domain.gps.GpsRawRepository;
import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.GpsRawJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.dataobject.GpsRawDo;
import com.mercaso.wms.delivery.infrastructure.repository.gps.jpa.mapper.GpsRawDoMapper;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class GpsRawRepositoryImpl implements GpsRawRepository {

    private final GpsRawDoMapper mapper;
    private final GpsRawJpaDao jpaDao;

    @Override
    public GpsRaw save(GpsRaw domain) {
        GpsRawDo gpsRawDo = mapper.domainToDo(domain);
        return mapper.doToDomain(jpaDao.save(gpsRawDo));
    }

    @Override
    public GpsRaw findById(UUID id) {
        Optional<GpsRawDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public GpsRaw update(GpsRaw domain) {
        GpsRawDo gpsRawDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == gpsRawDo) {
            throw new WmsBusinessException("GPS data not found.");
        }
        GpsRawDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, gpsRawDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(gpsRawDo));
    }

    @Override
    public Optional<GpsRawDo> findByUserId(UUID userId) {

        return jpaDao.findByUserId(userId);
    }

    @Override
    public List<GpsRaw> saveAll(List<GpsRaw> gpsRaws) {
        return mapper.doToDomains(jpaDao.saveAll(mapper.domainToDos(gpsRaws)));
    }

    @Override
    public List<GpsRaw> findByDeliveryTaskId(UUID deliveryTaskId) {
        return mapper.doToDomains(jpaDao.findByDeliveryTaskIdOrderByReportAtAsc(deliveryTaskId));
    }
}