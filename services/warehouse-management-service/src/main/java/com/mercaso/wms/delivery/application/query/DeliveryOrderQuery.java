package com.mercaso.wms.delivery.application.query;

import com.mercaso.wms.application.query.SortType;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class DeliveryOrderQuery {

    private List<String> orderNumbers;

    private List<String> deliveryTaskNumbers;

    private String deliveryDate;

    private List<String> statuses;

    private List<UUID> driverUserIds;

    private List<String> truckNumbers;

    private String paymentType;

    private List<String> paymentStatuses;

    private List<String> fulfillmentStatuses;

    private String rescheduleType;

    private Boolean issueOrder;

    private List<SortType> sortTypes;

    @Min(value = 1, message = "Page number must be greater than 0")
    private int page;

    @Min(value = 1, message = "Page size must be greater than 0")
    private int pageSize;
}
