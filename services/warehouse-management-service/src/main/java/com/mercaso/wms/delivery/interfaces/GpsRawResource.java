package com.mercaso.wms.delivery.interfaces;

import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.service.GpsRawService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "GPS Raw")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/gps-raw")
@RequiredArgsConstructor
public class GpsRawResource {

    private final GpsRawService gpsRawService;

    @SingleDeviceLoginCheck
    @PreAuthorize("hasAuthority('da:write:gps-raw')")
    @PostMapping("/report")
    public void report(@Valid @RequestBody GpsRawCommand command) {
        gpsRawService.report(command);
    }
}
