package com.mercaso.wms.delivery.domain.document;

import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface DocumentRepository extends BaseDomainRepository<Document, UUID> {

    List<Document> findByEntityIdAndEntityNameAndDocumentTypes(UUID entityId,
        String entityName,
        List<DocumentType> documentTypes);

    List<Document> findByEntityIds(List<UUID> entityIds);

} 