package com.mercaso.wms.delivery.application.command.account;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Command object for updating an account.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAccountCommand extends BaseCommand {

    private UUID warehouseId;

    private String userName;

    private AccountStatus status;
}