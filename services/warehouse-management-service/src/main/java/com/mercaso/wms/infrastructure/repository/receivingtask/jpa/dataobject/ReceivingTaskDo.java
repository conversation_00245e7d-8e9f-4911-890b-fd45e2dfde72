package com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject;

import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.infrastructure.annotation.NumberGenerator;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.receivingtaskitem.jpa.dataobject.ReceivingTaskItemDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "receivingTaskItems")
@Table(name = "receiving_task")
@SQLDelete(sql = "update receiving_task set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class ReceivingTaskDo extends BaseDo {

    @Column(name = "batch_id", nullable = false)
    private UUID batchId;

    @NumberGenerator
    private String number;

    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(value = EnumType.STRING)
    private ReceivingTaskStatus status;

    @Column(name = "receive_start_time")
    private Instant receiveStartTime;

    @Column(name = "received_time")
    private Instant receivedTime;

    @Column(name = "receive_user_id")
    private UUID receiveUserId;

    @Column(name = "receive_user_name", length = 50)
    private String receiveUserName;

    @Column(name = "vendor")
    private String vendor;

    @Column(name = "type")
    @Enumerated(value = EnumType.STRING)
    private ReceivingTaskType type;

    @Column(name = "created_user_name")
    private String createdUserName;

    @OneToMany(mappedBy = "receivingTask", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @OrderBy("receivingSequence ASC")
    private List<ReceivingTaskItemDo> receivingTaskItems;
}