package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import static com.mercaso.wms.infrastructure.utils.SqlLikeConditionBuilder.buildIlikeOrCondition;

import com.mercaso.wms.delivery.application.query.DeliveryOrderQuery;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

@Data
@Builder
public class DeliveryOrderSearchSqlBuilder {

    private final DeliveryOrderQuery query;
    private final Pageable pageable;
    private final MapSqlParameterSource params;

    public String buildSelectSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ")
            .append("ddo.id, ")
            .append("ddo.order_number, ")
            .append("ddo.status, ")
            .append("ddo.delivery_date, ")
            .append("ddo.delivery_time_window, ")
            .append("ddo.payment_type, ")
            .append("ddo.payment_status, ")
            .append("ddo.fulfillment_status, ")
            .append("ddo.customer_notes, ")
            .append("ddo.notes, ")
            .append("ddo.address, ")
            .append("ddo.sequence, ")
            .append("ddo.plan_arrive_at, ")
            .append("ddo.plan_delivery_at, ")
            .append("ddo.arrived_at, ")
            .append("ddo.unloaded_at, ")
            .append("ddo.delivered_at, ")
            .append("ddo.reschedule_type, ")
            .append("ddo.total_price, ")
            .append("ddo.original_total_price, ")
            .append("dt.id as delivery_task_id, ")
            .append("dt.number as delivery_task_number, ")
            .append("dt.driver_user_name, ")
            .append("dt.truck_number, ")
            .append("SUM(doi.current_qty) as currentQty, ")
            .append("SUM(doi.delivered_qty) as deliveredQty ")
            .append("FROM da_delivery_order ddo ")
            .append("LEFT JOIN da_delivery_task dt ON ddo.delivery_task_id = dt.id ")
            .append("LEFT JOIN da_delivery_order_items doi ON ddo.id = doi.delivery_order_id ")
            .append("WHERE ddo.deleted_at IS NULL ");

        appendConditions(sql);
        appendGroupBy(sql);
        appendOrderBy(sql);
        appendPagination(sql);

        return sql.toString();
    }

    public String buildCountSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(distinct ddo.id) FROM da_delivery_order ddo ")
            .append("LEFT JOIN da_delivery_task dt ON ddo.delivery_task_id = dt.id ")
            .append("LEFT JOIN da_delivery_order_items doi ON ddo.id = doi.delivery_order_id ")
            .append("WHERE ddo.deleted_at IS NULL ");

        appendConditions(sql);
        return sql.toString();
    }

    private void appendConditions(StringBuilder sql) {
        if (query == null) {
            return;
        }

        if (CollectionUtils.isNotEmpty(query.getOrderNumbers())) {
            sql.append(buildIlikeOrCondition("ddo.order_number", query.getOrderNumbers(), "orderNumber", params));
        }
        if (CollectionUtils.isNotEmpty(query.getDeliveryTaskNumbers())) {
            sql.append(buildIlikeOrCondition("dt.number", query.getDeliveryTaskNumbers(), "deliveryTaskNumber", params));
        }
        if (CollectionUtils.isNotEmpty(query.getStatuses())) {
            sql.append("AND ddo.status in (:status) ");
            params.addValue("status", query.getStatuses());
        }
        if (query.getDeliveryDate() != null) {
            sql.append("AND ddo.delivery_date = :deliveryDate ");
            params.addValue("deliveryDate", query.getDeliveryDate());
        }
        if (CollectionUtils.isNotEmpty(query.getDriverUserIds())) {
            sql.append("AND dt.driver_user_id in (:driverUserId) ");
            params.addValue("driverUserId", query.getDriverUserIds());
        }
        if (CollectionUtils.isNotEmpty(query.getTruckNumbers())) {
            sql.append("AND dt.truck_number in (:truckNumber) ");
            params.addValue("truckNumber", query.getTruckNumbers());
        }
        if (StringUtils.isNotEmpty(query.getPaymentType())) {
            sql.append("AND ddo.payment_type ilike CONCAT('%', :paymentType, '%') ");
            params.addValue("paymentType", query.getPaymentType());
        }
        if (CollectionUtils.isNotEmpty(query.getPaymentStatuses())) {
            sql.append("AND ddo.payment_status in (:paymentStatus) ");
            params.addValue("paymentStatus", query.getPaymentStatuses());
        }
        if (CollectionUtils.isNotEmpty(query.getFulfillmentStatuses())) {
            sql.append("AND ddo.fulfillment_status in (:fulfillmentStatus) ");
            params.addValue("fulfillmentStatus", query.getFulfillmentStatuses());
        }
        if (query.getRescheduleType() != null) {
            sql.append("AND ddo.reschedule_type = :rescheduleType ");
            params.addValue("rescheduleType", query.getRescheduleType());
        }
        if (query.getIssueOrder() != null) {
            if (Boolean.TRUE.equals(query.getIssueOrder())) {
                sql.append("AND (ddo.notes IS not null or doi.reason_code IS not null) ");
            } else {
                sql.append("AND (ddo.notes IS null or doi.reason_code IS null) ");
            }
        }
    }

    private void appendGroupBy(StringBuilder sql) {
        sql.append("GROUP BY ddo.id, dt.id, ddo.created_at ");
    }

    private void appendOrderBy(StringBuilder sql) {
        if (pageable.getSort().isSorted()) {
            sql.append("ORDER BY ");
            pageable.getSort().forEach(order ->
                sql.append(order.getProperty())
                    .append(" ")
                    .append(order.getDirection().name())
                    .append(", "));
            sql.setLength(sql.length() - 2);
        } else {
            sql.append("ORDER BY ddo.created_at DESC");
        }
    }

    private void appendPagination(StringBuilder sql) {
        sql.append(" LIMIT :limit OFFSET :offset");
        params.addValue("limit", pageable.getPageSize());
        params.addValue("offset", pageable.getOffset());
    }
} 