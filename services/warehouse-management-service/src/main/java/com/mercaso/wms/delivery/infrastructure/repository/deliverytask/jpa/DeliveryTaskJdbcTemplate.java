package com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa;

import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryTaskView;
import com.mercaso.wms.delivery.application.query.DeliveryTaskQuery;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryTaskJdbcTemplate {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public Page<SearchDeliveryTaskView> search(DeliveryTaskQuery query, Pageable pageable) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        DeliveryTaskSearchSqlBuilder sqlBuilder = DeliveryTaskSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        String selectSql = sqlBuilder.buildSelectSql();
        String countSql = sqlBuilder.buildCountSql();

        log.debug("Delivery Task executing SQL: {}, params: {}", selectSql, params.getValues());

        Long total = jdbcTemplate.queryForObject(countSql, params, Long.class);
        long effectiveTotal = total != null ? total : 0L;

        List<SearchDeliveryTaskView> content = jdbcTemplate.query(selectSql, params, this::convert);

        return new PageImpl<>(content, pageable, effectiveTotal);
    }

    private SearchDeliveryTaskView convert(ResultSet rs, int rowNum) throws SQLException {
        try {
            int totalOrders = rs.getInt("total_orders");
            int deliveredOrders = rs.getInt("delivered_orders");

            return SearchDeliveryTaskView.builder()
                .id(rs.getObject("id", UUID.class))
                .taskNumber(rs.getString("task_number"))
                .status(DeliveryTaskStatus.valueOf(rs.getString("status")))
                .deliveryDate(rs.getString("delivery_date"))
                .truckNumber(rs.getString("truck_number"))
                .driverName(rs.getString("driver_user_name"))
                .clockIn(getInstantOrNull(rs, "clock_in"))
                .dispatchAt(getInstantOrNull(rs, "dispatch_at"))
                .breakStartAt(getInstantOrNull(rs, "break_start_at"))
                .breakEndAt(getInstantOrNull(rs, "break_end_at"))
                .completedAt(getInstantOrNull(rs, "completed_at"))
                .createdAt(rs.getTimestamp("created_at").toInstant())
                .updatedAt(rs.getTimestamp("updated_at").toInstant())
                .totalOrderCount(totalOrders)
                .deliveredOrderCount(deliveredOrders)
                .hasRescheduledOrders(rs.getInt("total_reschedule_orders") > 0)
                .activeOrderId(rs.getObject("active_order_id", UUID.class))
                .activeOrderDelayMinutes(rs.getInt("active_order_delay_minutes"))
                .gpsReported(rs.getBoolean("gps_reported"))
                .build();
        } catch (Exception e) {
            log.error("Error converting query result: {}", e.getMessage(), e);
            throw e;
        }
    }

    private Instant getInstantOrNull(ResultSet rs, String columnName) throws SQLException {
        java.sql.Timestamp timestamp = rs.getTimestamp(columnName);
        return timestamp != null ? timestamp.toInstant() : null;
    }
} 