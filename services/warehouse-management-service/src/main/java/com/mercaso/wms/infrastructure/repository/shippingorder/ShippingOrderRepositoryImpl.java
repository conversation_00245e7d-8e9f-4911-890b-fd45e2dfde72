package com.mercaso.wms.infrastructure.repository.shippingorder;

import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.ShippingOrderJdbcTemplate;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.ShippingOrderJpaDao;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.dataobject.ShippingOrderDo;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.mapper.ShippingOrderDoMapper;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ShippingOrderRepositoryImpl implements ShippingOrderRepository {

    private final ShippingOrderJpaDao jpaDao;

    private final ShippingOrderDoMapper mapper;

    private final ShippingOrderJdbcTemplate jdbcTemplate;


    @Override
    public ShippingOrder save(ShippingOrder domain) {
        ShippingOrderDo shippingOrderDo = mapper.domainToDo(domain);
        shippingOrderDo.getShippingOrderItems()
            .forEach(shippingOrderItemDo -> shippingOrderItemDo.setShippingOrder(shippingOrderDo));
        return mapper.doToDomain(jpaDao.save(shippingOrderDo));
    }

    @Override
    public ShippingOrder findById(UUID id) {
        return jpaDao.findById(id).map(mapper::doToDomain).orElse(null);
    }

    @Override
    public ShippingOrder update(ShippingOrder domain) {
        ShippingOrderDo shippingOrderDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == shippingOrderDo) {
            throw new WmsBusinessException("ShippingOrder not found.");
        }
        ShippingOrderDo target = mapper.domainToDo(domain);
        target.getShippingOrderItems().forEach(shippingOrderItemDo -> shippingOrderItemDo.setShippingOrder(target));
        BeanUtils.copyProperties(target, shippingOrderDo, "createdBy", "createdAt");
        return mapper.doToDomain(jpaDao.save(shippingOrderDo));
    }

    @Override
    public ShippingOrder findByNumber(String number) {
        return mapper.doToDomain(jpaDao.findByOrderNumber(number));
    }

    @Override
    public List<ShippingOrder> findByOrderNumbers(List<String> orderNumbers) {
        return mapper.doToDomains(jpaDao.findByOrderNumberIn(orderNumbers));
    }

    @Override
    public List<ShippingOrder> findByOrderIds(List<UUID> ids) {
        return mapper.doToDomains(jpaDao.findAllById(ids));
    }

    @Override
    public Page<ShippingOrder> findShippingOrderList(ShippingOrderSearchCriteria criteria, Pageable pageable) {
        Page<ShippingOrderDo> shippingOrderList = jpaDao.findShippingOrderList(criteria, pageable);
        return shippingOrderList.map(mapper::doToDomain);
    }

    @Override
    public void deleteAll() {
        jpaDao.deleteAll();
    }

    @Override
    public List<ShippingOrder> saveAll(List<ShippingOrder> shippingOrders) {
        List<ShippingOrderDo> shippingOrderDos = mapper.domainToDos(shippingOrders);
        shippingOrderDos.forEach(shippingOrderDo ->
            shippingOrderDo.getShippingOrderItems()
                .forEach(shippingOrderItemDo -> shippingOrderItemDo.setShippingOrder(shippingOrderDo)));
        return mapper.doToDomains(jpaDao.saveAll(shippingOrderDos));
    }

    @Override
    public List<ShippingOrder> findActiveShippingOrdersByDeliveryDate(String deliveryDate) {
        return mapper.doToDomains(jpaDao.findActiveShippingOrdersByDeliveryDate(deliveryDate));
    }

    @Override
    public List<ShippingOrder> findByBatchId(UUID batchId) {
        return mapper.doToDomains(jpaDao.findByBatchId(batchId));
    }

    @Override
    public List<SkuCountByDeliveryDate> skuCountByDeliveryDate(String deliveryDate) {
        return jdbcTemplate.skuCountByDeliveryDate(deliveryDate);
    }

    @Override
    public ShippingOrder findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId) {
        return mapper.doToDomain(jpaDao.findByOrderNumberAndShopifyOrderId(orderNumber, shopifyOrderId));
    }

    @Override
    public List<ShippingOrder> findRescheduledShippingOrders(String deliveryDate) {
        return mapper.doToDomains(jpaDao.findRescheduledShippingOrders(deliveryDate));
    }
}
