package com.mercaso.wms.domain.pickingtaskitem;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface PickingTaskItemRepository extends BaseDomainRepository<PickingTaskItem, UUID> {

    List<PickingTaskItem> findFiledPickingTaskItemBy(String deliveryDate, List<String> pickingTaskNumbers);

    void deleteAll();

    List<PickingTaskItem> findByIds(List<UUID> pickingTaskItemIds);

    void deleteByIds(List<UUID> pickingTaskItemIds);

}
