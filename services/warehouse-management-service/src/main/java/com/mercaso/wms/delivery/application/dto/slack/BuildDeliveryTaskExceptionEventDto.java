package com.mercaso.wms.delivery.application.dto.slack;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuildDeliveryTaskExceptionEventDto implements SlackNotificationEvent {

    @JsonProperty("previous_task_id")
    private UUID previousTaskId;

    @JsonProperty("previous_task_number")
    private String previousTaskNumber;

    @JsonProperty("current_task_id")
    private UUID currentTaskId;

    @JsonProperty("current_task_number")
    private String currentTaskNumber;

    @JsonProperty("order_id")
    private UUID orderId;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("order_status")
    private String orderStatus;

    @JsonProperty("order_detail_url")
    private String orderDetailUrl;

    @JsonProperty("task_detail_url")
    private String taskDetailUrl;

    @JsonProperty("previous_task_detail_url")
    private String previousTaskDetailUrl;

    @JsonProperty("delivery_date")
    private String deliveryDate;

    @JsonProperty("plan_delivery_date")
    private String planDeliveryDate;

    private String webhookUrl;
} 