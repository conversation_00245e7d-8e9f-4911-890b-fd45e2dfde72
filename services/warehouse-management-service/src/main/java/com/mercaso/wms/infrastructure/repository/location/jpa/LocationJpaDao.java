package com.mercaso.wms.infrastructure.repository.location.jpa;

import com.mercaso.wms.infrastructure.repository.location.jpa.dataobject.LocationDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface LocationJpaDao extends JpaRepository<LocationDo, UUID> {

    @Query("SELECT l FROM LocationDo l WHERE (:name IS NULL OR l.name LIKE CONCAT(:name, '%'))")
    Page<LocationDo> findByName(@Param("name") String name, Pageable pageable);

    LocationDo findByName(String name);

    List<LocationDo> findByWarehouseId(UUID warehouseId);
}
