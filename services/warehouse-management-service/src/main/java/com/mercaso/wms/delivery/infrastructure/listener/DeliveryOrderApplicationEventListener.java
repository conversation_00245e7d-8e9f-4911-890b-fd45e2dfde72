package com.mercaso.wms.delivery.infrastructure.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.application.dto.customer.CustomerDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredApplicationEvent;
import com.mercaso.wms.delivery.application.service.DocumentApplicationService;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.utils.GenerateInvoiceService;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryOrderApplicationEventListener {

    private final GenerateInvoiceService generateInvoiceService;

    private final DocumentApplicationService documentApplicationService;

    private final OnesignalAdaptor onesignalAdaptor;

    private final FeatureFlagsManager featureFlagsManager;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    public void handleDeliveredEvent(DeliveryOrderDeliveredApplicationEvent applicationEvent) {
        log.info("[handleDeliveredEvent] delivery order {} has been delivered",
            applicationEvent.getPayload().getDeliveryOrderId());
        try {
            DeliveryOrderDto deliveryOrderDto = applicationEvent.getPayload().getData();
            //generate invoice
            DocumentDto documentDto = generateInvoiceService.generateInvoice(deliveryOrderDto);
            log.info("[handleDeliveredEvent] invoice generated for delivery order {} {}",
                deliveryOrderDto.getOrderNumber(),
                documentDto.getFileName());
            CustomerDto customer = deliveryOrderDto.getCustomer();
            if (customer == null || customer.getEmail() == null) {
                log.info("[handleDeliveredEvent] customer is null for delivery order {}", deliveryOrderDto.getOrderNumber());
                return;
            }
            if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.SEND_INVOICE_EMAIL_TO_CUSTOMER)) {
                //send email
                onesignalAdaptor.sendEmail(
                    customer.getEmail(),
                    Map.of("orderNumber", deliveryOrderDto.getOrderNumber(),
                        "customerName", customer.getFirstName().concat(" ").concat(customer.getLastName()),
                        "invoiceUrl",
                        documentApplicationService.generateInvoiceSignatureWithExpiration(documentDto.getFileName()),
                        "deliveryDate",
                        deliveryOrderDto.getDeliveryDate(),
                        "totalAmount",
                        deliveryOrderDto.getTotalPrice() != null ? deliveryOrderDto.getTotalPrice().toString() : "0.0"));
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("[handleDeliveredEvent] error generating invoice", e);
        }
    }

}
