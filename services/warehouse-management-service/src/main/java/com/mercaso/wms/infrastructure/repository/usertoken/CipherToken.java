package com.mercaso.wms.infrastructure.repository.usertoken;

import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.Instant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "cipher_token")
@SQLDelete(sql = "update cipher_token set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class CipherToken extends BaseDo {

    @Column(name = "cipher_access_token")
    private String cipherAccessToken;

    private String scope;

    @Column(name = "expire_date")
    private Instant expireDate;

    @Column(name = "token_type")
    private String tokenType;

}
