package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.application.mapper.transfertask.TransferTaskDtoApplicationMapper;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class TransferTaskQueryService {

    private final TransferTaskRepository transferTaskRepository;
    private final TransferTaskDtoApplicationMapper transferTaskDtoApplicationMapper;

    public List<TransferTaskDto> findByPickingTaskItemId(UUID pickingTaskItemId) {
        return transferTaskDtoApplicationMapper.domainToDtos(transferTaskRepository.findByPickingTaskItemId(pickingTaskItemId));
    }

}