package com.mercaso.wms.batch.writer.impl;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(2)
@Slf4j
public class MissionOrderFormSheetWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        Map<String, List<ExcelBatchDto>> sourceAndListMap = condition.getSourceAndListMap();

        List<ExcelBatchDto> excelBatchDtos = sourceAndListMap.get(GeneratedDocNameEnum.MISSION.getValue());
        if (CollectionUtils.isEmpty(excelBatchDtos)) {
            excelBatchDtos = Lists.newArrayList();
        }
        Map<String, Integer> itemNumberAndQtyMap = excelBatchDtos.stream()
            .collect(Collectors.toMap(ExcelBatchDto::getItemNumber, ExcelBatchDto::getQuantity, Integer::sum));
        excelWriter.fill(itemNumberAndQtyMap, writerSheet(GeneratedDocNameEnum.MISSION_ORDER_FORM.getValue()).build());
    }
}
