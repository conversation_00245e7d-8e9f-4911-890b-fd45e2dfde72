package com.mercaso.wms.infrastructure.statemachine.config;

import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderTransitionEvents;
import com.mercaso.wms.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

@Configuration
@EnableStateMachineFactory(contextEvents = false, name = "deliveryOrderTransitionEventsStateMachineFactory")
public class DeliveryOrderStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<DeliveryOrderStatus, DeliveryOrderTransitionEvents> {

    @Override
    public void configure(StateMachineStateConfigurer<DeliveryOrderStatus, DeliveryOrderTransitionEvents> states)
        throws Exception {
        states
            .withStates()
            .initial(DeliveryOrderStatus.CREATED)
            .states(EnumSet.allOf(DeliveryOrderStatus.class))
        ;
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<DeliveryOrderStatus, DeliveryOrderTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(DeliveryOrderStatus.CREATED).target(DeliveryOrderStatus.ASSIGNED)
            .event(DeliveryOrderTransitionEvents.ASSIGN)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.CREATED).target(DeliveryOrderStatus.CANCELED)
            .event(DeliveryOrderTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ASSIGNED).target(DeliveryOrderStatus.CANCELED)
            .event(DeliveryOrderTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.IN_TRANSIT).target(DeliveryOrderStatus.CANCELED)
            .event(DeliveryOrderTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ARRIVED).target(DeliveryOrderStatus.UNLOADED)
            .event(DeliveryOrderTransitionEvents.UNLOAD)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ARRIVED).target(DeliveryOrderStatus.CANCELED)
            .event(DeliveryOrderTransitionEvents.CANCEL)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ASSIGNED).target(DeliveryOrderStatus.IN_TRANSIT)
            .event(DeliveryOrderTransitionEvents.IN_TRANSIT)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.IN_TRANSIT).target(DeliveryOrderStatus.ARRIVED)
            .event(DeliveryOrderTransitionEvents.ARRIVE)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.UNLOADED).target(DeliveryOrderStatus.DELIVERED)
            .event(DeliveryOrderTransitionEvents.COMPLETE)
            // --- Revert delivery order to CREATED state ---
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ASSIGNED).target(DeliveryOrderStatus.CREATED)
            .event(DeliveryOrderTransitionEvents.REVERT_TO_CREATED)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.IN_TRANSIT).target(DeliveryOrderStatus.CREATED)
            .event(DeliveryOrderTransitionEvents.REVERT_TO_CREATED)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.ARRIVED).target(DeliveryOrderStatus.CREATED)
            .event(DeliveryOrderTransitionEvents.REVERT_TO_CREATED)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.UNLOADED).target(DeliveryOrderStatus.CREATED)
            .event(DeliveryOrderTransitionEvents.REVERT_TO_CREATED)
            .and()
            .withExternal()
            .source(DeliveryOrderStatus.DELIVERED).target(DeliveryOrderStatus.CREATED)
            .event(DeliveryOrderTransitionEvents.REVERT_TO_CREATED);
    }

    @Bean
    @StatemachineFactory(domainClass = DeliveryOrder.class)
    public WmsStateMachineFactory<DeliveryOrderStatus, DeliveryOrderTransitionEvents, DeliveryOrder> deliveryOrderStateMachineAdapter(
        StateMachineFactory<DeliveryOrderStatus, DeliveryOrderTransitionEvents> deliveryOrderTransitionEventsStateMachineFactory,
        StateMachinePersister<DeliveryOrderStatus, DeliveryOrderTransitionEvents, DeliveryOrder> deliveryOrderStateMachinePersister) {
        return new WmsStateMachineFactory<>(deliveryOrderTransitionEventsStateMachineFactory, deliveryOrderStateMachinePersister);
    }

    @Bean
    public StateMachinePersister<DeliveryOrderStatus, DeliveryOrderTransitionEvents, DeliveryOrder> deliveryOrderStateMachinePersister(
        StateMachinePersist<DeliveryOrderStatus, DeliveryOrderTransitionEvents, DeliveryOrder> deliveryOrderStateMachinePersist) {
        return new DefaultStateMachinePersister<>(deliveryOrderStateMachinePersist);
    }

}
