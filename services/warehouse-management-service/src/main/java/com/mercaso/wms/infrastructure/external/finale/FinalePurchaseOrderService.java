package com.mercaso.wms.infrastructure.external.finale;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.VendorDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.CreatePurchaseOrderDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntity;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntityTypeEnum;
import com.mercaso.wms.infrastructure.external.finale.dto.PurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderDto;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.utils.NumberGeneration;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class FinalePurchaseOrderService {

    private static final String FACILITY_URL_PREFIX = "/api/facility/";
    private static final String PURCHASE_ORDER_URL_PREFIX = "https://app.finaleinventory.com/mercaso/sc2/?order/purchase/order/";
    private final FinaleProductService finaleProductService;
    private final PickingTaskRepository pickingTaskRepository;
    private final ReceivingTaskRepository receivingTaskRepository;
    private final ImsAdaptor imsAdaptor;
    private final FinaleConfigProperties finaleConfigProperties;
    private final BatchRepository batchRepository;
    private final LocationRepository locationRepository;


    public void createPurchaseOrder(SourceEnum source, Batch batch) {

        VendorDto vendor = imsAdaptor.getVendorByName(source.getVendorName());
        if (vendor == null) {
            log.error("[createPurchaseOrder] Vendor not found for supplier name: {}", source.getVendorName());
            return;
        }
        PurchaseOrderResponse response;
        if (isOnlineSupplier(source)) {
            response = processReceivingTasks(vendor, batch.getId(), batch.getTag(), source);
        } else {
            response = processPickingTasks(vendor, batch.getId(), batch.getTag(), source);
        }
        if (response != null){
            Batch byId = batchRepository.findById(batch.getId());
            updateFinaleEntities(byId, response.getOrderId(), source);
        }
    }

    public PurchaseOrderResponse processReceivingTasks(VendorDto vendor, UUID batchId, String deliveryDate, SourceEnum source) {
        List<ReceivingTask> receivingTaskList = receivingTaskRepository.findByBatchId(batchId);
        if (CollectionUtils.isEmpty(receivingTaskList)) return null;

        List<ReceivingTask> receivingTasks = receivingTaskList.stream()
            .filter(task -> task.getVendor().equals(source.name()))
            .filter(receivingTask -> receivingTask.getStatus() != ReceivingTaskStatus.CANCELED)
            .toList();

        boolean allTaskReceived = receivingTasks.stream().allMatch(task -> task.getStatus() == ReceivingTaskStatus.RECEIVED);

        if (allTaskReceived) {
            List<UUID> itemIds = receivingTasks.stream()
                .flatMap(task -> task.getReceivingTaskItems().stream())
                .map(ReceivingTaskItem::getItemId)
                .distinct()
                .toList();
            if (CollectionUtils.isEmpty(itemIds)) return null;

            List<ItemCategoryDto> items = imsAdaptor.getItemsByIds(itemIds);
            Map<String, BigDecimal> itemCostMap = getItemCostMap(items);

            List<ReceivingTaskItem> allItems = receivingTasks.stream()
                .flatMap(rt -> rt.getReceivingTaskItems().stream())
                .filter(receivingTaskItem -> receivingTaskItem.getReceivedQty() > 0)
                .collect(Collectors.toList());

            return createAndProcessOrder(vendor, allItems, deliveryDate, itemCostMap);
        } else {
            log.info("[createPurchaseOrder] Not all receiving tasks are completed for vendor: {}, batchId: {}", vendor.getVendorName(), batchId);
        }
        return null;
    }

    public PurchaseOrderResponse processPickingTasks(VendorDto vendor, UUID batchId, String deliveryDate, SourceEnum source) {
        List<PickingTask> pickingTaskList = pickingTaskRepository.findByBatchId(batchId);
        if (CollectionUtils.isEmpty(pickingTaskList)) return null;

        List<PickingTask> pickingTasks = pickingTaskList.stream()
            .filter(task -> task.getSource().name().equals(source.name()))
            .filter(pickingTask -> pickingTask.getStatus() != PickingTaskStatus.CANCELED)
            .toList();

        boolean allTaskCompleted = pickingTasks.stream().allMatch(task -> task.getStatus() == PickingTaskStatus.COMPLETED
            || task.getStatus() == PickingTaskStatus.PARTIALLY_COMPLETED);

        if (allTaskCompleted) {
            List<UUID> itemIds = pickingTasks.stream()
                .flatMap(task -> task.getPickingTaskItems().stream())
                .map(PickingTaskItem::getItemId)
                .distinct()
                .toList();
            if (CollectionUtils.isEmpty(itemIds)) return null;

            List<ItemCategoryDto> items = imsAdaptor.getItemsByIds(itemIds);
            Map<String, BigDecimal> itemCostMap = getItemCostMap(items);

            List<PickingTaskItem> allItems = pickingTasks.stream()
                .flatMap(pt -> pt.getPickingTaskItems().stream())
                .collect(Collectors.toList());

            return createAndProcessOrder(vendor, allItems, deliveryDate, itemCostMap);
        } else {
            log.info("[createPurchaseOrder] Not all picking tasks are completed for vendor: {}, batchId: {}", vendor.getVendorName(), batchId);
        }
        return null;
    }

    private <T> PurchaseOrderResponse createAndProcessOrder(VendorDto vendor, List<T> items, String deliveryDate,
                                          Map<String, BigDecimal> itemCostMap) {
        PurchaseOrderResponse response = handlePurchaseOrder(vendor, items, deliveryDate, itemCostMap);

        if (response != null) {
            log.info("[createPurchaseOrder] Purchase order created successfully for vendor: {}", vendor.getVendorName());
        } else {
            log.error("[createPurchaseOrder] Failed to create purchase order for vendor: {}", vendor.getVendorName());
        }

        return response;
    }

    private <T> PurchaseOrderResponse handlePurchaseOrder(VendorDto vendor, List<T> taskItems,
                                                         String deliveryDate, Map<String, BigDecimal> itemCostMap) {
        CreatePurchaseOrderDto createDto = buildCreatePurchaseOrderDto(vendor, taskItems, deliveryDate, itemCostMap);
        PurchaseOrderResponse purchaseOrder = finaleProductService.createPurchaseOrder(createDto);

        if (purchaseOrder != null) {
            log.info("[createPurchaseOrder] Purchase order created successfully: {}", purchaseOrder.getOrderId());
            ReceivePurchaseOrderDto receiveDto = buildReceivePurchaseOrderDto(purchaseOrder);
            log.info("[createPurchaseOrder] Receive purchase order: {}", receiveDto.getPrimaryOrderUrl());
            ReceivePurchaseOrderResponse response = finaleProductService.receivePurchaseOrder(receiveDto);

            if (response != null) {
                log.info("[createPurchaseOrder] Purchase order received successfully: {}", response.getPrimaryOrderUrl());
                PurchaseOrderResponse purchaseOrderResponse = finaleProductService.completePurchaseOrder(purchaseOrder.getOrderId());
                log.info("[createPurchaseOrder] Purchase order completed successfully: {}", purchaseOrderResponse.getOrderId());
                return purchaseOrderResponse;
            }
        }
        log.error("[createPurchaseOrder] Failed to complete purchase order for vendor: {}", vendor.getVendorName());
        return null;
    }

    private <T> CreatePurchaseOrderDto buildCreatePurchaseOrderDto(VendorDto vendor, List<T> taskItems,
                                                                 String deliveryDate, Map<String, BigDecimal> itemCostMap) {
        Map<String, Integer> itemQtyBySku = new HashMap<>();

        for (T item : taskItems) {
            String skuNumber;
            Integer quantity;

            if (item instanceof ReceivingTaskItem receivingItem) {
                skuNumber = receivingItem.getSkuNumber();
                quantity = receivingItem.getReceivedQty();
            } else if (item instanceof PickingTaskItem pickingItem) {
                skuNumber = pickingItem.getSkuNumber();
                quantity = pickingItem.getPickedQty();
            } else {
                continue;
            }

            itemQtyBySku.merge(skuNumber, quantity, Integer::sum);
        }

        List<CreatePurchaseOrderDto.OrderItem> orderItems = itemQtyBySku.entrySet().stream()
            .map(entry -> {
                CreatePurchaseOrderDto.OrderItem orderItem = new CreatePurchaseOrderDto.OrderItem();
                orderItem.setProductId(entry.getKey());
                orderItem.setQuantity(entry.getValue());
                orderItem.setUnitPrice(itemCostMap.get(entry.getKey()));
                return orderItem;
            })
            .collect(Collectors.toList());

        CreatePurchaseOrderDto.OrderRole orderRole = new CreatePurchaseOrderDto.OrderRole();
        orderRole.setPartyId(vendor.getFinaleId());
        orderRole.setRoleTypeId("SUPPLIER");

        return CreatePurchaseOrderDto.builder()
            .destinationFacilityUrl(finaleConfigProperties.getMfcFacilityUrl())
            .orderId("J" + deliveryDate.replaceAll("-","") + NumberGeneration.generateSecureRandomNumeric(6))
            .orderItemList(orderItems)
            .orderRoleList(List.of(orderRole))
            .build();
    }

    private ReceivePurchaseOrderDto buildReceivePurchaseOrderDto(PurchaseOrderResponse purchaseOrder) {
        Location shipSbLocation = locationRepository.findByName(finaleConfigProperties.getShipSb());
        List<ReceivePurchaseOrderDto.ShipmentItem> shipmentItems = purchaseOrder.getOrderItemList().stream()
            .map(orderItem -> ReceivePurchaseOrderDto.ShipmentItem.builder()
                .productUrl(orderItem.getProductUrl())
                .quantity(orderItem.getQuantity())
                .facilityUrl("/" + finaleConfigProperties.getDomain() + FACILITY_URL_PREFIX + shipSbLocation.getFinaleId())
                .build())
            .collect(Collectors.toList());

        return ReceivePurchaseOrderDto.builder()
            .primaryOrderUrl(purchaseOrder.getOrderUrl())
            .receiveDate(LocalDate.now().toString())
            .shipmentItemList(shipmentItems)
            .build();
    }

    private Map<String, BigDecimal> getItemCostMap(List<ItemCategoryDto> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new HashMap<>();
        }

        Map<String, BigDecimal> itemCostMap = new HashMap<>();
        for (ItemCategoryDto item : items) {
            if (!CollectionUtils.isEmpty(item.getVendorItemDtos())) {
                item.getVendorItemDtos().stream()
                    .filter(vendorItem -> vendorItem.getVendorId() != null
                        && vendorItem.getItemId() != null
                        && vendorItem.getVendorId().equals(item.getBackupVendorId())
                        && vendorItem.getItemId().equals(item.getId())
                        && vendorItem.getBackupCost() != null)
                    .findFirst()
                    .ifPresent(vendorItem -> {
                        BigDecimal cost = vendorItem.getBackupCost();
                        if (cost != null
                            && item.getPack() != null && item.getPack() > 0
                            && item.getCrv() != null) {
                            BigDecimal crvCost = item.getCrv().multiply(new BigDecimal(item.getPack()));
                            cost = cost.add(crvCost);
                            log.info("[createPurchaseOrder] Calculated cost for item {}: {}", item.getSkuNumber(), cost);
                        }
                        itemCostMap.put(item.getSkuNumber(), cost);
                    });
            }
        }
        return itemCostMap;
    }

    private boolean isOnlineSupplier(SourceEnum source) {
        return SourceEnum.onlineVendors().stream().anyMatch(onlineVendorSource -> onlineVendorSource == source);
    }

    private void updateFinaleEntities(Batch batch, String purchaseOrderId, SourceEnum source) {
        JsonNode finaleEntities = batch.getFinaleEntities();
        List<FinaleEntity> finaleEntityList = Optional.ofNullable(finaleEntities)
            .filter(node -> !node.isNull())
            .map(node -> new ObjectMapper().convertValue(node, new TypeReference<List<FinaleEntity>>() {}))
            .orElseGet(ArrayList::new);

        FinaleEntity finaleEntity = new FinaleEntity();
        String purchaseOrderUrl = "/mercaso/api/order/" + purchaseOrderId;

        finaleEntity.setEntityUrl(PURCHASE_ORDER_URL_PREFIX + Base64.getEncoder().encodeToString(purchaseOrderUrl.getBytes()));
        finaleEntity.setEntityId(purchaseOrderId);
        finaleEntity.setEntityType(FinaleEntityTypeEnum.PURCHASE_ORDER);
        finaleEntity.setVendorName(source.name());
        finaleEntityList.add(finaleEntity);

        batch.setFinaleEntities(SerializationUtils.toTree(finaleEntityList));
        batchRepository.save(batch);
    }

}