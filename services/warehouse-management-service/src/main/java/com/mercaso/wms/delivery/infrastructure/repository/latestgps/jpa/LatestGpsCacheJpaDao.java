package com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa;

import com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.dataobject.LatestGpsCacheDo;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface LatestGpsCacheJpaDao extends JpaRepository<LatestGpsCacheDo, UUID> {

    LatestGpsCacheDo findByUserId(UUID userId);

    List<LatestGpsCacheDo> findByReportAtBetween(Instant start, Instant end);

    LatestGpsCacheDo findByDeliveryOrderId(UUID deliveryOrderId);

} 