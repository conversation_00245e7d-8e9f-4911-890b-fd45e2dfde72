package com.mercaso.wms.delivery.application.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.slack.BuildDeliveryTaskExceptionEventDto;
import com.mercaso.wms.delivery.application.dto.slack.SlackDeliveryEventDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.infrastructure.external.slack.SlackAdaptor;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SlackDeliveryNotificationService {

    private final SlackAdaptor slackAdaptor;
    private final DeliveryTaskRepository deliveryTaskRepository;
    private final AccountRepository accountRepository;
    private final FeatureFlagsManager featureFlagsManager;

    @Value("${mercaso.delivery-portal-base-url}")
    private String deliveryPortalBaseUrl;

    @Value("${slack.delivery.webhook-url}")
    private String webhookUrl;

    @Value("${slack.delivery.order-issues}")
    private String orderIssuesWebhookUrl;

    @Value("${slack.delivery.task-build-exception}")
    private String taskBuildExceptionWebhookUrl;

    @Async
    public void sendDeliveryCompletedNotification(DeliveryOrderDto deliveryOrderDto) {

        boolean isEnabled = featureFlagsManager.isFeatureOn(FeatureFlagKeys.TRIGGER_DELIVERY_DETAILS_WORKFLOW);
        if (!isEnabled) {
            log.info("Feature flag SEND_EXECUTION_EVENTS_TO_RM is off, skipping send delivery notification");
            return;
        }

        if (deliveryOrderDto == null) {
            log.warn("Cannot send notification: delivery order is null");
            return;
        }

        UUID deliveryTaskId = deliveryOrderDto.getDeliveryTaskId();
        DeliveryTask task = deliveryTaskRepository.findById(deliveryTaskId);
        UUID driverUserId = task.getDriverUserId();
        Optional<Account> driverAccount = accountRepository.findByUserId(driverUserId);

        try {
            SlackDeliveryEventDto event = buildDeliveryEvent(deliveryOrderDto, driverAccount);
            slackAdaptor.notifyDeliveryCompletion(event);
        } catch (Exception e) {
            log.error("Failed to send Slack notification for delivery order: {}. Error: {}",
                deliveryOrderDto.getId(), e.getMessage(), e);
        }
    }

    @Async
    public void batchNotifyBuildTaskException(List<BuildDeliveryTaskExceptionEventDto> events) {

        Set<UUID> taskIds = events.stream()
            .map(BuildDeliveryTaskExceptionEventDto::getPreviousTaskId)
            .collect(Collectors.toSet());

        List<DeliveryTask> tasks = deliveryTaskRepository.findByIdIn(taskIds);

        events.forEach(event -> {
            List<DeliveryTask> matchingTasks = tasks.stream()
                .filter(t -> t.getId().equals(event.getPreviousTaskId()))
                .collect(Collectors.toList());
            
            DeliveryTask task = null;
            if (!matchingTasks.isEmpty()) {
                task = matchingTasks.get(matchingTasks.size() - 1);
            }
            
            if (task != null) {
                event.setPreviousTaskNumber(task.getNumber());
            } else {
                log.warn("No delivery task found for ID: {}", event.getPreviousTaskId());
            }

            event.setWebhookUrl(taskBuildExceptionWebhookUrl);
            event.setOrderDetailUrl(buildOrderDetailUrl(event.getOrderId()));
            event.setTaskDetailUrl(buildTaskDetailUrl(event.getCurrentTaskId()));
            event.setPreviousTaskDetailUrl(buildTaskDetailUrl(event.getPreviousTaskId()));

            slackAdaptor.batchNotifyBuildTaskException(event);
        });
    }


    private SlackDeliveryEventDto buildDeliveryEvent(DeliveryOrderDto orderDto, Optional<Account> driverAccount) {

        String paymentType = formatPaymentTypes(orderDto.getPaymentType());

        String notes = orderDto.getNotes();

        Map<String, List<String>> itemIssues = collectItemIssues(orderDto);

        log.info("Collected item issues for order {}: {}", orderDto.getOrderNumber(), itemIssues);
        String missing = String.join(", ", itemIssues.get("MISSING"));
        String added = String.join(", ", itemIssues.get("UNPLANNED"));
        String returned = formatReturnedItems(itemIssues);

        String url = determineWebhookUrl(notes, missing, added, returned, orderDto.getOrderNumber());

        SlackDeliveryEventDto eventDto = SlackDeliveryEventDto.builder()
            .paymentMethod(paymentType)
            .customerName(getCustomerName(orderDto))
            .orderDetailUrl(buildOrderDetailUrl(orderDto.getId()))
            .orderNumber(orderDto.getOrderNumber())
            .additionalNotes(notes)
            .total(String.valueOf(orderDto.getTotalPrice()))
            .webhookUrl(url)
            .missingItems(missing)
            .addedItems(added)
            .returnedItems(returned)
            .build();

        driverAccount.ifPresent(account -> populateDriverInfo(eventDto, account));

        log.info("Sending Slack delivery event for order: {}, event: {}", orderDto.getOrderNumber(), eventDto);
        return eventDto;
    }

    private String formatPaymentTypes(List<PaymentType> paymentTypes) {
        return Optional.ofNullable(paymentTypes)
            .filter(CollectionUtils::isNotEmpty)
            .map(types -> types.stream()
                .map(PaymentType::name)
                .collect(Collectors.joining(",")))
            .orElse(null);
    }

    private Map<String, List<String>> collectItemIssues(DeliveryOrderDto orderDto) {
        List<String> reasonTypes = List.of("MISSING", "RETURNS", "UNPLANNED", "DAMAGED", "EXPIRED");
        Map<String, List<String>> reasonTypeMap = reasonTypes.stream()
            .collect(Collectors.toMap(type -> type, type -> new ArrayList<>()));

        orderDto.getDeliveryOrderItems().stream()
            .filter(i -> i.getReasonCode() != null)
            .forEach(item -> processItemReasonCode(item, reasonTypes, reasonTypeMap));

        return reasonTypeMap;
    }

    private void processItemReasonCode(DeliveryOrderItemDto item, List<String> reasonTypes,
        Map<String, List<String>> reasonTypeMap) {
        String reasonCode = item.getReasonCode();
        if (reasonCode == null || reasonCode.trim().isEmpty()) {
            log.info("Item {} has no reason code, skipping", item.getLine());
            return;
        }

        try {
            Map<String, Object> reasonCodeMap = SerializationUtils.readValue(reasonCode, new TypeReference<>() {
            });
            if (reasonCodeMap == null || reasonCodeMap.isEmpty()) {
                log.info("Item {}  after serialization, the reason code map is empty", item.getLine());
                return;
            }

            String itemLineRef = " #" + item.getLine();
            reasonTypes.stream()
                .filter(reasonCodeMap::containsKey)
                .filter(type -> reasonCodeMap.get(type) != null)
                .forEach(type -> reasonTypeMap.get(type).add(itemLineRef));

        } catch (Exception e) {
            log.warn("Failed to parse reasonCode JSON: {}", reasonCode, e);
        }
    }

    private String formatReturnedItems(Map<String, List<String>> itemIssues) {
        return String.join(", ", itemIssues.get("RETURNS")) +
            String.join(", ", itemIssues.get("DAMAGED")) +
            String.join(", ", itemIssues.get("EXPIRED"));
    }

    private String determineWebhookUrl(String notes, String missing, String added, String returned, String orderNumber) {
        if (StringUtils.isNotBlank(notes) || StringUtils.isNotBlank(missing) ||
            StringUtils.isNotBlank(added) || StringUtils.isNotBlank(returned)) {
            log.info("Notification for order {} contains issues, using order issues webhook URL", orderNumber);
            return orderIssuesWebhookUrl;
        }
        return webhookUrl;
    }

    private void populateDriverInfo(SlackDeliveryEventDto eventDto, Account account) {
        eventDto.setDriverName(account.getUserName());
        eventDto.setDriverUserEmail(account.getEmail());
        eventDto.setDriverDetail(buildDriverDetailUrl(account.getUserName()));
    }

    private String getCustomerName(DeliveryOrderDto orderDto) {
        if (orderDto.getCustomer() == null) {
            return "";
        }

        StringBuilder nameBuilder = new StringBuilder();
        if (orderDto.getCustomer().getFirstName() != null) {
            nameBuilder.append(orderDto.getCustomer().getFirstName()).append(" ");
        }
        if (orderDto.getCustomer().getLastName() != null) {
            nameBuilder.append(orderDto.getCustomer().getLastName());
        }
        return nameBuilder.toString().trim();
    }

    private String buildOrderDetailUrl(UUID orderId) {
        return String.format("%s/delivery-orders/%s", deliveryPortalBaseUrl, orderId);
    }

    private String buildDriverDetailUrl(String driverName) {
        return String.format("%s/drivers?page=1&pageSize=20&userName=%s", deliveryPortalBaseUrl, driverName.replace(" ", "+"));
    }

    private String buildTaskDetailUrl(UUID taskId) {
        return String.format("%s/delivery-tasks/%s", deliveryPortalBaseUrl, taskId);
    }
} 