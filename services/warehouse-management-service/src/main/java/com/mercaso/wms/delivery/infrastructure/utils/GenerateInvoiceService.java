package com.mercaso.wms.delivery.infrastructure.utils;

import com.mercaso.wms.delivery.application.command.UploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto;
import com.mercaso.wms.delivery.application.helper.GenerateInvoiceHelper;
import com.mercaso.wms.delivery.application.service.DocumentApplicationService;
import com.mercaso.wms.delivery.domain.document.enums.DocumentType;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GenerateInvoiceService {

    private final DocumentApplicationService documentApplicationService;

    private final GenerateInvoiceHelper generateInvoiceHelper;

    public DocumentDto generateInvoice(DeliveryOrderDto deliveryOrderDto) throws IOException, InterruptedException {
        return generateInvoiceAndUpload(generateInvoiceHelper.buildGenerateInvoiceDto(deliveryOrderDto));
    }

    public DocumentDto generateInvoiceAndUpload(GenerateInvoiceDto generateInvoiceDto) throws IOException, InterruptedException {
        File templateDir = new File("/opt/mercaso/warehouse-management-service/resources/template/invoice");
        String scriptPath = new File(templateDir, "generateInvoice.js").getAbsolutePath();
        String generatedFileName = new File(templateDir, generateInvoiceDto.getOrderNumber() + ".pdf").getAbsolutePath();
        log.info("[generateInvoice] generatedFileName: {}", generatedFileName);
        Process process = null;
        try {
            String nodePath = "node";
            ProcessBuilder processBuilder = new ProcessBuilder(
                nodePath,
                scriptPath,
                "--data",
                SerializationUtils.serialize(generateInvoiceDto)
            );

            processBuilder.directory(templateDir);
            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info(line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new DeliveryBusinessException("PDF generation failed.");
            }

            File file = new File(generatedFileName);
            if (!file.exists()) {
                throw new DeliveryBusinessException("PDF generation failed.");
            }
            return documentApplicationService.uploadDocument(UploadDocumentCommand.builder()
                .entityId(generateInvoiceDto.getDeliveryOrderId())
                .entityName(EntityEnums.DELIVERY_ORDER)
                .documentType(DocumentType.INVOICE)
                .build(), file);
        } finally {
            if (process != null) {
                process.destroyForcibly();
            }
            File pdfFile = new File(generatedFileName);
            if (pdfFile.exists()) {
                pdfFile.deleteOnExit();
            }
        }

    }

}
