package com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa;

import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.dataobject.PickingTaskItemDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PickingTaskItemJpaDao extends JpaRepository<PickingTaskItemDo, UUID> {

    @Query(value = """
         select pti.* from picking_task_items pti 
         left join picking_task pt on pt.id = pti.picking_task_id
         left join batch b on b.id = pt.batch_id
         where pti.error_info is not null
         and pt.deleted_at is null
         and pti.deleted_at is null
         and pt.status = 'FAILED'
         and (:deliveryDate is null or b.tag = :deliveryDate)
         and (:pickingTaskNumbers is null or pt.number IN :pickingTaskNumbers)
        """, nativeQuery = true)
    List<PickingTaskItemDo> findBy(@Param("deliveryDate") String deliveryDate,
        @Param("pickingTaskNumbers") List<String> pickingTaskNumbers);


}
