package com.mercaso.wms.application.command.receivingtask;

import com.mercaso.wms.application.command.BaseCommand;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class UpdateReceivingTaskCommand extends BaseCommand {

    private UUID receivingTaskId;

    private List<UpdateReceivingTaskItem> receivingTaskItems;

    @Data
    @Builder
    public static class UpdateReceivingTaskItem {

        private UUID receivingTaskItemId;

        private Integer expectQty;
    }

}
