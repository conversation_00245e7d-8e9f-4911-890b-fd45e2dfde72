package com.mercaso.wms.application.mapper;

import com.mercaso.wms.application.dto.view.SearchTransferTaskView;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;
import org.springframework.jdbc.core.RowMapper;

public class SearchTransferTaskViewRowMapper implements RowMapper<SearchTransferTaskView> {

    @Override
    public SearchTransferTaskView mapRow(ResultSet rs, int rowNum) throws SQLException {
        SearchTransferTaskView task = new SearchTransferTaskView();

        String id = rs.getString("id");
        task.setId(id != null ? UUID.fromString(id) : null);
        task.setNumber(rs.getString("number"));
        task.setStatus(rs.getString("status") != null ? TransferTaskStatus.valueOf(rs.getString("status")) : null);

        task.setLoadingAt(rs.getTimestamp("loading_at") != null ? rs.getTimestamp("loading_at").toInstant() : null);
        task.setReceivedAt(rs.getTimestamp("received_at") != null ? rs.getTimestamp("received_at").toInstant() : null);

        task.setCreatedAt(rs.getTimestamp("created_at") != null ? rs.getTimestamp("created_at").toInstant() : null);
        task.setUpdatedAt(rs.getTimestamp("updated_at") != null ? rs.getTimestamp("updated_at").toInstant() : null);
        task.setCreatedBy(rs.getString("created_by"));
        task.setUpdatedBy(rs.getString("updated_by"));

        task.setLoaderUserName(rs.getString("loader_user_name"));
        task.setReceiverUserName(rs.getString("receiver_user_name"));

        task.setOriginWarehouseName(rs.getString("originWarehouseName"));
        task.setDestinationWarehouseName(rs.getString("destinationWarehouseName"));

        task.setTotalTransferQty(rs.getInt("totalTransferQty"));
        return task;
    }

}