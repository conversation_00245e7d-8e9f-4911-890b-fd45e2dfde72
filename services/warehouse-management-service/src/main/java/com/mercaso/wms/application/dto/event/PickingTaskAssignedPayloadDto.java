package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.PickingTaskDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PickingTaskAssignedPayloadDto extends BusinessEventPayloadDto<PickingTaskDto> {

    private UUID pickingTaskId;

    @Builder
    public PickingTaskAssignedPayloadDto(PickingTaskDto data, UUID pickingTaskId) {
        super(data);
        this.pickingTaskId = pickingTaskId;
    }

}
