package com.mercaso.wms.delivery.application.dto.view;

import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchDeliveryTaskView {

    private UUID id;
    private String taskNumber;
    private DeliveryTaskStatus status;
    private String deliveryDate;
    private String truckNumber;
    private String driverName;
    private Instant clockIn;
    private Instant dispatchAt;
    private Instant breakStartAt;
    private Instant breakEndAt;
    private Instant completedAt;
    private Instant createdAt;
    private Instant updatedAt;
    private int totalOrderCount;
    private int deliveredOrderCount;
    private boolean hasRescheduledOrders;
    private UUID activeOrderId;
    private Integer activeOrderDelayMinutes;
    private boolean gpsReported;
}