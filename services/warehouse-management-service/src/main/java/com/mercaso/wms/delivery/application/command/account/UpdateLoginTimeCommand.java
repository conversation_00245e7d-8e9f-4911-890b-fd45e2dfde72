package com.mercaso.wms.delivery.application.command.account;

import com.mercaso.wms.application.command.BaseCommand;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLoginTimeCommand extends BaseCommand {

    private List<UUID> userIds;
    private Instant loginTime;
}