package com.mercaso.wms.delivery.application.mapper;

import com.mercaso.wms.delivery.application.dto.RmRouteDto;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface RmRouteDtoApplicationMapper {

    RmRouteDto domainToDto(RmRoute domain);

    List<RmRouteDto> domainToDtos(List<RmRoute> domains);

    RmRoute dtoToDomain(RmRouteDto dto);

    List<RmRoute> dtoToDomains(List<RmRouteDto> dtos);
} 