package com.mercaso.wms.delivery.application.command.deliveryorder;

import com.mercaso.wms.application.command.BaseCommand;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateDeliveryOrderItemCommand extends BaseCommand {

    private UUID id;

    private BigDecimal deliveredQty;

    private String reasonCode;

}
