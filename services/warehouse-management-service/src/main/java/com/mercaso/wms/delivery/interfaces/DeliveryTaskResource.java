package com.mercaso.wms.delivery.interfaces;

import com.mercaso.wms.delivery.application.command.deliverytask.BuildDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.service.DeliveryTaskService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Delivery Tasks")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/delivery-tasks")
@RequiredArgsConstructor
public class DeliveryTaskResource {

    private final DeliveryTaskService deliveryTaskService;

    @PreAuthorize("hasAuthority('da:write:delivery-tasks')")
    @PostMapping("/build")
    public void build(@Valid @RequestBody BuildDeliveryTaskCommand command) {
        log.info("Processing request to build delivery tasks for date: {}", command.getDeliveryDate());
        deliveryTaskService.buildTasks(command.getDeliveryDate());
    }

    @PreAuthorize("hasAuthority('da:write:delivery-tasks')")
    @PostMapping("/automated-build")
    public void buildTasksAutomatically(@Valid @RequestBody BuildDeliveryTaskCommand command) {
        deliveryTaskService.buildTasksAutomatically(command.getDeliveryDate());
    }

    @Operation(summary = "Rebuild a delivery task based on the latest route information")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Task rebuilt successfully"),
        @ApiResponse(responseCode = "404", description = "Task or route not found"),
        @ApiResponse(responseCode = "500", description = "Failed to rebuild task")
    })
    @PreAuthorize("hasAuthority('da:write:delivery-tasks')")
    @PostMapping("/{taskId}/rebuild")
    public void rebuildTask(@PathVariable UUID taskId) {
        log.info("Processing request to rebuild delivery task with ID: {}", taskId);
        deliveryTaskService.rebuildTask(taskId);
    }

    @SingleDeviceLoginCheck
    @Operation(summary = "Complete a delivery task")
    @PreAuthorize("hasAuthority('da:write:delivery-tasks')")
    @PutMapping("/{taskId}/complete")
    public DeliveryTaskDto completeTask(@PathVariable UUID taskId) {
        log.info("Processing request to complete delivery task with ID: {}", taskId);
        return deliveryTaskService.completeTask(taskId);
    }

    @Operation(summary = "Complete multiple delivery tasks in batch")
    @PreAuthorize("hasAuthority('da:write:delivery-tasks')")
    @PutMapping("/batch-complete")
    public List<UUID> batchCompleteTasks(@RequestBody List<UUID> taskIds) {
        log.info("Processing request to complete {} delivery tasks", taskIds.size());
        return deliveryTaskService.batchCompleteTasks(taskIds);
    }
}
