package com.mercaso.wms.application.mapper.batchitem;

import com.mercaso.wms.application.dto.BatchItemDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.batchitem.BatchItem;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BatchItemDtoApplicationMapper extends BaseDtoApplicationMapper<BatchItem, BatchItemDto> {

    BatchItemDtoApplicationMapper INSTANCE = Mappers.getMapper(BatchItemDtoApplicationMapper.class);

}
