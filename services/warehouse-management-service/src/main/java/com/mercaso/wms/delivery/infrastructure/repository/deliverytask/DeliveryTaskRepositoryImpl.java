package com.mercaso.wms.delivery.infrastructure.repository.deliverytask;

import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.mapper.DeliveryTaskDoMapper;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Limit;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class DeliveryTaskRepositoryImpl implements DeliveryTaskRepository {

    private final DeliveryTaskDoMapper mapper;
    private final DeliveryTaskJpaDao jpaDao;

    @Override
    public DeliveryTask save(DeliveryTask domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public DeliveryTask findById(UUID id) {
        Optional<DeliveryTaskDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public DeliveryTask update(DeliveryTask domain) {
        DeliveryTaskDo deliveryTaskDo = jpaDao.findById(domain.getId())
            .orElseThrow(() -> new RuntimeException("deliveryTask not found."));
        DeliveryTaskDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = List.of("createdBy", "createdAt", "createdUserName", "number");
        BeanUtils.copyProperties(target, deliveryTaskDo, ignoreProperties.toArray(new String[0]));
        return mapper.doToDomain(jpaDao.save(deliveryTaskDo));
    }

    @Override
    public Optional<DeliveryTask> findByDriverUserIdAndStatus(UUID deliveryTaskId, DeliveryTaskStatus status) {

        Optional<DeliveryTaskDo> task = jpaDao.findByDriverUserIdAndStatusOrderByCreatedAtDesc(deliveryTaskId,
            status,
            Limit.of(1));

        return task.map(mapper::doToDomain);
    }

    @Override
    public List<DeliveryTask> saveAll(List<DeliveryTask> deliveryTasks) {

        List<DeliveryTaskDo> deliveryTaskDos = deliveryTasks.stream()
            .map(mapper::domainToDo)
            .toList();

        return jpaDao.saveAll(deliveryTaskDos).stream()
            .map(mapper::doToDomain)
            .toList();
    }

    @Override
    public List<DeliveryTask> findByDeliveryDate(String deliveryDate) {
        List<DeliveryTaskDo> tasks = jpaDao.findByDeliveryDate(deliveryDate);
        return tasks.stream()
            .map(mapper::doToDomain)
            .toList();
    }

    @Override
    public void deleteAllByIdIn(Collection<UUID> ids) {
        jpaDao.deleteAllByIdIn(ids);
    }

    @Override
    public List<DeliveryTask> updateAll(List<DeliveryTask> deliveryTasks) {

        List<DeliveryTaskDo> deliveryTaskDos = deliveryTasks.stream()
            .map(mapper::domainToDo)
            .toList();

        return jpaDao.saveAll(deliveryTaskDos).stream()
            .map(mapper::doToDomain)
            .toList();
    }

    @Override
    public List<DeliveryTask> findByIdIn(Collection<UUID> ids) {
        List<DeliveryTaskDo> deliveryTaskDos = jpaDao.findAllById(ids);
        return deliveryTaskDos.stream()
            .map(mapper::doToDomain)
            .toList();
    }
}