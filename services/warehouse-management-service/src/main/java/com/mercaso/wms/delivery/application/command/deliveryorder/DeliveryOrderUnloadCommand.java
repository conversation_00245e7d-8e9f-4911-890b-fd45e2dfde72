package com.mercaso.wms.delivery.application.command.deliveryorder;

import com.mercaso.wms.application.command.BaseCommand;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryOrderUnloadCommand extends BaseCommand {

    @NotEmpty
    private List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos;

}
