package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder;

import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.DeliveryOrderJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.mapper.DeliveryOrderDoMapper;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class DeliveryOrderRepositoryImpl implements DeliveryOrderRepository {

    private final DeliveryOrderDoMapper mapper;
    private final DeliveryOrderJpaDao jpaDao;

    @Override
    public DeliveryOrder save(DeliveryOrder domain) {
        DeliveryOrderDo deliveryOrderDo = mapper.domainToDo(domain);
        deliveryOrderDo.getDeliveryOrderItems().forEach(item -> item.setDeliveryOrder(deliveryOrderDo));
        return mapper.doToDomain(jpaDao.save(deliveryOrderDo));
    }

    @Override
    public DeliveryOrder findById(UUID id) {
        Optional<DeliveryOrderDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public DeliveryOrder update(DeliveryOrder domain) {
        DeliveryOrderDo deliveryOrderDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == deliveryOrderDo) {
            throw new WmsBusinessException("DeliveryOrder not found.");
        }
        DeliveryOrderDo target = mapper.domainToDo(domain);
        target.getDeliveryOrderItems().forEach(item -> item.setDeliveryOrder(target));
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, deliveryOrderDo, ignoreProperties.toArray(new String[0]));
        deliveryOrderDo = jpaDao.save(deliveryOrderDo);
        return mapper.doToDomain(deliveryOrderDo);
    }

    @Override
    public List<DeliveryOrder> findAllByDeliveryTaskIdAndStatusIn(UUID deliveryTaskId, List<DeliveryOrderStatus> statuses) {
        List<DeliveryOrderDo> deliveryOrderDos = jpaDao.findAllByDeliveryTaskIdAndStatusIn(deliveryTaskId, statuses);
        if (null == deliveryOrderDos || deliveryOrderDos.isEmpty()) {
            return List.of();
        }
        return deliveryOrderDos.stream().map(mapper::doToDomain).toList();
    }

    @Override
    public DeliveryOrder findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId) {
        return mapper.doToDomain(jpaDao.findByOrderNumberAndShopifyOrderId(orderNumber, shopifyOrderId));
    }

    @Override
    public List<DeliveryOrderDo> saveAll(List<DeliveryOrder> deliveryOrders) {
        List<DeliveryOrderDo> deliveryOrderDos = deliveryOrders.stream().map(mapper::domainToDo).toList();
        deliveryOrderDos.forEach(deliveryOrderDo -> deliveryOrderDo.getDeliveryOrderItems()
            .forEach(item -> item.setDeliveryOrder(deliveryOrderDo)));
        return jpaDao.saveAll(deliveryOrderDos);
    }

    @Override
    public List<DeliveryOrder> findAllByOrderNumberIn(Collection<String> orderNumbers) {

        List<DeliveryOrderDo> deliveryOrderDos = jpaDao.findAllByOrderNumberIn(orderNumbers);
        if (null == deliveryOrderDos || deliveryOrderDos.isEmpty()) {
            return List.of();
        }
        return deliveryOrderDos.stream().map(mapper::doToDomain).toList();
    }

    @Override
    public List<DeliveryOrder> findAllByDeliveryTaskIdIn(Collection<UUID> taskIds) {
        List<DeliveryOrderDo> deliveryOrderDos = jpaDao.findAllByDeliveryTaskIdIn(taskIds);
        if (null == deliveryOrderDos || deliveryOrderDos.isEmpty()) {
            return List.of();
        }
        return deliveryOrderDos.stream().map(mapper::doToDomain).toList();
    }

    @Override
    public List<DeliveryOrder> findAllByDeliveryTaskId(UUID id) {
        List<DeliveryOrderDo> deliveryOrderDos = jpaDao.findAllByDeliveryTaskId(id);

        if (CollectionUtils.isEmpty(deliveryOrderDos)) {
            return List.of();
        }
        return deliveryOrderDos.stream().map(mapper::doToDomain).toList();
    }

    @Override
    public DeliveryOrder findByOrderNumber(String orderNumber) {
        return mapper.doToDomain(jpaDao.findByOrderNumber(orderNumber));
    }

    @Override
    public List<DeliveryOrder> updateAll(List<DeliveryOrder> deliveryOrders) {
        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return List.of();
        }

        List<DeliveryOrderDo> doList = new ArrayList<>();

        for (DeliveryOrder order : deliveryOrders) {
            DeliveryOrderDo orderDo = jpaDao.findById(order.getId()).orElse(null);
            if (orderDo != null) {
                DeliveryOrderDo updatedDo = mapper.domainToDo(order);
                updatedDo.getDeliveryOrderItems().forEach(item -> item.setDeliveryOrder(updatedDo));

                // Preserve audit fields
                List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
                BeanUtils.copyProperties(updatedDo, orderDo, ignoreProperties.toArray(new String[0]));
                doList.add(orderDo);
            }
        }

        List<DeliveryOrderDo> savedDoList = jpaDao.saveAll(doList);
        return savedDoList.stream().map(mapper::doToDomain).toList();
    }
}