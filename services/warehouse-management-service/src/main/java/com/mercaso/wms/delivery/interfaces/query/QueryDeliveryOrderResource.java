package com.mercaso.wms.delivery.interfaces.query;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.queryservice.DeliveryOrderQueryService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Delivery Orders")
@Slf4j
@RestController
@RequestMapping("/delivery/query/delivery-orders")
@RequiredArgsConstructor
public class QueryDeliveryOrderResource {

    private final DeliveryOrderQueryService deliveryOrderQueryService;

    @PreAuthorize("hasAuthority('da:read:delivery-orders')")
    @GetMapping("/{deliveryOrderId}")
    public DeliveryOrderDto findById(@PathVariable UUID deliveryOrderId) {
        return deliveryOrderQueryService.findById(deliveryOrderId);
    }

    @PreAuthorize("hasAuthority('da:read:delivery-orders')")
    @GetMapping("/by-order-number/{orderNumber}")
    public DeliveryOrderDto findByOrderNumber(@PathVariable String orderNumber) {
        return deliveryOrderQueryService.findByOrderNumber(orderNumber);
    }

}
