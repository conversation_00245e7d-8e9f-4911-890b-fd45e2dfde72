package com.mercaso.wms.domain.transfertask;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.transfertask.CreateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskTransitionEvents;
import com.mercaso.wms.domain.transfertaskitem.TransferTaskItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.util.CollectionUtils;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class TransferTask extends BaseStateMachine<TransferTask, TransferTaskStatus, TransferTaskTransitionEvents> {

    private UUID id;

    private String number;

    private String deliveryDate;

    private Warehouse originWarehouse;

    private Warehouse destinationWarehouse;

    private UUID loaderUserId;

    private String loaderUserName;

    private UUID receiverUserId;

    private String receiverUserName;

    private String truckNumber;

    private List<TransferTaskItem> transferTaskItems;

    private Instant loadingAt;

    private Instant receivedAt;

    public void activate() {
        if (TransferTaskStatus.IN_TRANSIT == this.getState()) {
            return;
        }
        this.processEvent(TransferTaskTransitionEvents.ACTIVATED);
    }

    public void revertToDraft() {
        if (TransferTaskStatus.DRAFT == this.getState()) {
            return;
        }
        this.processEvent(TransferTaskTransitionEvents.REVERT_TO_DRAFT);
    }

    public void loaded() {
        this.loaderUserId =
            SecurityContextUtil.getLoginUserId() != null ? UUID.fromString(SecurityContextUtil.getLoginUserId()) : null;
        this.loaderUserName = SecurityContextUtil.getUsername() != null ? SecurityContextUtil.getUsername() : null;
        this.loadingAt = Instant.now();
        processEvent(TransferTaskTransitionEvents.LOADED);
    }

    public void received() {
        this.receiverUserId =
            SecurityContextUtil.getLoginUserId() != null ? UUID.fromString(SecurityContextUtil.getLoginUserId()) : null;
        this.receiverUserName = SecurityContextUtil.getUsername() != null ? SecurityContextUtil.getUsername() : null;
        this.receivedAt = Instant.now();
        processEvent(TransferTaskTransitionEvents.RECEIVED);
    }

    public TransferTask create(Warehouse originWarehouse,
        Warehouse destinationWarehouse,
        Location originLocation,
        Location destinationLocation,
        CreateTransferTaskCommand command,
        Map<UUID, ItemCategoryDto> itemCategoriesMap) {
        this.deliveryDate = command.getDeliveryDate();
        this.originWarehouse = originWarehouse;
        this.destinationWarehouse = destinationWarehouse;
        super.setState(TransferTaskStatus.DRAFT);
        this.transferTaskItems = createTransferTaskItems(command, originLocation, destinationLocation, itemCategoriesMap);
        return this;
    }

    public TransferTask update(UpdateTransferTaskCommand command,
        Map<UUID, ItemCategoryDto> itemCategoriesMap,
        Location originLocation,
        Location destinationLocation) {
        Map<UUID, TransferTaskItem> existingItemsMap = this.transferTaskItems.stream()
            .collect(Collectors.toMap(TransferTaskItem::getPickingTaskItemId, item -> item));

        List<TransferTaskItem> newTaskItems = new ArrayList<>();

        for (UpdateTransferTaskCommand.UpdateTransferTaskItemDto transferTaskItem : command.getTransferTaskItems()) {
            TransferTaskItem existingItem = existingItemsMap.get(transferTaskItem.getPickingTaskItemId());
            if (existingItem != null) {
                existingItem.setTransferQty(transferTaskItem.getTransferQty());
            } else {
                ItemCategoryDto item = itemCategoriesMap.get(transferTaskItem.getItemId());
                if (item == null) {
                    log.error("Item not found for ID: {}", transferTaskItem.getItemId());
                    continue;
                }

                newTaskItems.add(TransferTaskItem.builder()
                    .itemId(transferTaskItem.getItemId())
                    .skuNumber(item.getSkuNumber())
                    .title(item.getTitle())
                    .pickingTaskItemId(transferTaskItem.getPickingTaskItemId())
                    .originLocationId(originLocation != null ? originLocation.getId() : null)
                    .destinationLocationId(destinationLocation != null ? destinationLocation.getId() : null)
                    .originLocationName(originLocation != null ? originLocation.getName() : null)
                    .destinationLocationName(destinationLocation != null ? destinationLocation.getName() : null)
                    .transferQty(transferTaskItem.getTransferQty())
                    .transferTaskId(this.id)
                    .build());
            }
        }

        if (!CollectionUtils.isEmpty(newTaskItems)) {
            this.transferTaskItems.addAll(newTaskItems);
        }
        return this;
    }

    private List<TransferTaskItem> createTransferTaskItems(CreateTransferTaskCommand command,
        Location originLocation,
        Location destinationLocation,
        Map<UUID, ItemCategoryDto> itemCategoriesMap) {
        if (CollectionUtils.isEmpty(command.getTransferTaskItems())) {
            return List.of();
        }

        return command.getTransferTaskItems().stream()
            .map(item -> {
                ItemCategoryDto itemCategoryDto = itemCategoriesMap.get(item.getItemId());
                if (itemCategoryDto == null || item.getTransferQty() <= 0) {
                    log.warn("Item not found or transfer quantity is invalid, skipping item: {}", item.getItemId());
                    return null;
                }

                return TransferTaskItem.builder()
                    .itemId(item.getItemId())
                    .skuNumber(itemCategoryDto.getSkuNumber())
                    .title(itemCategoryDto.getTitle())
                    .pickingTaskItemId(item.getPickingTaskItemId())
                    .originLocationId(originLocation != null ? originLocation.getId() : null)
                    .destinationLocationId(destinationLocation != null ? destinationLocation.getId() : null)
                    .transferQty(item.getTransferQty())
                    .build();
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

}
