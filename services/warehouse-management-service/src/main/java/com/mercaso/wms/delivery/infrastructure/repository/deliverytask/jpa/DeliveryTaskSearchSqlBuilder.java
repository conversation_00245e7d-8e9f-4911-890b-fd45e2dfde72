package com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa;

import com.mercaso.wms.delivery.application.query.DeliveryTaskQuery;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;

@Builder
@Slf4j
public class DeliveryTaskSearchSqlBuilder {

    private static final String BASE_SELECT_TEMPLATE = """
        WITH active_orders AS (
            SELECT 
                dorder.delivery_task_id,
                dorder.id AS order_id,
                dorder.plan_arrive_at,
                dorder.arrived_at,
                ROW_NUMBER() OVER (
                    PARTITION BY dorder.delivery_task_id 
                    ORDER BY dorder.created_at ASC
                ) AS rn
            FROM da_delivery_order dorder
            JOIN da_delivery_task dt ON dorder.delivery_task_id = dt.id
            WHERE dorder.status IN ('ASSIGNED', 'IN_TRANSIT', 'ARRIVED', 'UNLOADED')
              AND dorder.reschedule_type IS NULL
              AND dorder.deleted_at IS NULL
              AND dt.deleted_at IS NULL
              %s
        )
        SELECT dt.id AS id,
               dt.number AS task_number,
               dt.status AS status,
               dt.delivery_date AS delivery_date,
               dt.truck_number AS truck_number,
               dt.driver_user_name AS driver_user_name,
               dt.clock_in AS clock_in,
               dt.dispatch_at AS dispatch_at,
               dt.break_start_at AS break_start_at,
               dt.break_end_at AS break_end_at,
               dt.completed_at AS completed_at,
               dt.created_at AS created_at,
               dt.updated_at AS updated_at,
               COUNT(CASE WHEN dorder.reschedule_type is not null THEN 1 END) AS total_reschedule_orders,
               COUNT(CASE WHEN dorder.status != 'CANCELED' THEN 1 END) AS total_orders,
               COUNT(CASE WHEN dorder.status = 'DELIVERED' THEN 1 END) AS delivered_orders,
               CASE WHEN dlgc.user_id IS NOT NULL THEN TRUE ELSE FALSE END AS gps_reported,
               ao.order_id AS active_order_id,
               CAST(
                      CASE
                          WHEN ao.arrived_at IS NOT NULL THEN
                              EXTRACT(EPOCH FROM (date_trunc('minute', ao.arrived_at) - date_trunc('minute', ao.plan_arrive_at))) / 60
                          WHEN ao.arrived_at IS NULL AND date_trunc('minute', NOW()) > date_trunc('minute', ao.plan_arrive_at) THEN
                              EXTRACT(EPOCH FROM (date_trunc('minute', NOW()) - date_trunc('minute', ao.plan_arrive_at))) / 60
                          ELSE 0
                      END AS BIGINT
                  ) AS active_order_delay_minutes
        FROM da_delivery_task dt
        LEFT JOIN da_delivery_order dorder ON dt.id = dorder.delivery_task_id
        LEFT JOIN active_orders ao ON dt.id = ao.delivery_task_id AND ao.rn = 1
        LEFT JOIN da_latest_gps_cache dlgc ON dt.id = dlgc.delivery_task_id
        """;

    private final DeliveryTaskQuery query;
    private final Pageable pageable;
    private final MapSqlParameterSource params;

    public String buildSelectSql() {
        String deliveryDateCondition = "";
        if (query.getDeliveryDate() != null) {
            deliveryDateCondition = "AND dorder.delivery_date = :deliveryDate";
            String deliveryDate = query.getDeliveryDate().toString();
            params.addValue("deliveryDate", deliveryDate);
        }

        String sql = String.format(BASE_SELECT_TEMPLATE, deliveryDateCondition)
            + buildWhereClause()
            + " GROUP BY dt.id, ao.order_id, ao.plan_arrive_at, ao.arrived_at, dt.delivery_date, dlgc.user_id"
            + buildOrderBySql()
            + " LIMIT :limit OFFSET :offset";

        params.addValue("limit", pageable.getPageSize());
        params.addValue("offset", pageable.getOffset());

        return sql;
    }

    public String buildCountSql() {
        return
            "SELECT COUNT(DISTINCT dt.id) FROM da_delivery_task dt LEFT JOIN da_delivery_order dorder ON dt.id = dorder.delivery_task_id"
                + buildWhereClause();
    }

    private String buildWhereClause() {
        List<String> conditions = new ArrayList<>();

        if (!CollectionUtils.isEmpty(query.getTaskNumbers())) {
            conditions.add("dt.number IN (:taskNumbers)");
            params.addValue("taskNumbers", query.getTaskNumbers());
        }

        if (!CollectionUtils.isEmpty(query.getStatuses())) {
            conditions.add("dt.status IN (:statuses)");
            List<String> statusNames = query.getStatuses().stream().map(Enum::name).toList();
            params.addValue("statuses", statusNames);
        }

        if (query.getDeliveryDate() != null) {
            conditions.add("dt.delivery_date = :deliveryDate");
            String deliveryDate = query.getDeliveryDate().toString();
            params.addValue("deliveryDate", deliveryDate);
        }

        if (!CollectionUtils.isEmpty(query.getDriverNames())) {
            conditions.add("dt.driver_user_name IN (:driverNames)");
            params.addValue("driverNames", query.getDriverNames());
        }

        if (!CollectionUtils.isEmpty(query.getDriverUserIds())) {
            conditions.add("dt.driver_user_id IN (:driverUserIds)");
            params.addValue("driverUserIds", query.getDriverUserIds());
        }

        if (!CollectionUtils.isEmpty(query.getTruckNumbers())) {
            conditions.add("dt.truck_number IN (:truckNumbers)");
            params.addValue("truckNumbers", query.getTruckNumbers());
        }
        if (query.getHasRescheduledOrders() != null) {
            if (Boolean.TRUE.equals(query.getHasRescheduledOrders())) {
                conditions.add("dorder.reschedule_type is not null");
            } else {
                conditions.add("dorder.reschedule_type is null");
            }
        }

        return conditions.isEmpty() ? " WHERE dt.deleted_at is null "
            : " WHERE dt.deleted_at is null AND " + String.join(" AND ", conditions);
    }

    private String buildOrderBySql() {
        StringBuilder orderSql = new StringBuilder();

        if (pageable.getSort().isSorted()) {
            orderSql.append(" ORDER BY ");
            pageable.getSort().forEach(order -> orderSql.append(order.getProperty())
                .append(" ")
                .append(order.getDirection().name())
                .append(", "));
            if (!orderSql.isEmpty()) {
                orderSql.setLength(orderSql.length() - 2);
            }
        } else {
            orderSql.append(" ORDER BY dt.created_at DESC");
        }

        return orderSql.toString();
    }
}