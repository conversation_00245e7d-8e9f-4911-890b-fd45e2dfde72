package com.mercaso.wms.batch.writer.impl;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(9)
@Slf4j
public class ExoticTotalSheetWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        Map<String, List<ExcelBatchDto>> sourceAndListMap = condition.getSourceAndListMap();
        List<ExcelBatchDto> excelBatchDtos = sourceAndListMap.get(GeneratedDocNameEnum.EXOTIC.getValue());
        if (CollectionUtils.isEmpty(excelBatchDtos)) {
            excelBatchDtos = Lists.newArrayList();
        }
        WriteSheet writeSheet = writerSheet(GeneratedDocNameEnum.EXOTIC_TOTALS.getValue()).build();
        excelWriter.fill(calculateExoticTotalData(excelBatchDtos), writeSheet);
        if (StringUtils.isNotEmpty(condition.getTaggedWith())) {
            excelWriter.fill(Map.of("deliveryDate", condition.getTaggedWith()), writeSheet);
        }
    }

    private List<ExcelBatchDto> calculateExoticTotalData(List<ExcelBatchDto> excelBatchDtos) {
        List<ExcelBatchDto> exoticTotalExcelBatchDtos = Lists.newArrayList();
        excelBatchDtos.stream().collect(Collectors.groupingBy(ExcelBatchDto::getItemNumber))
            .forEach((itemNumber, list) -> exoticTotalExcelBatchDtos.add(ExcelBatchDto.itemNumberQtyTotal(list)));
        return exoticTotalExcelBatchDtos;
    }
}
