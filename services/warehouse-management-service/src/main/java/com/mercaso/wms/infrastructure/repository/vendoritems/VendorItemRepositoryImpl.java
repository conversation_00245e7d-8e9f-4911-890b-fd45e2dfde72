package com.mercaso.wms.infrastructure.repository.vendoritems;

import com.mercaso.wms.domain.vendoritem.VendorItem;
import com.mercaso.wms.domain.vendoritem.VendorItemRepository;
import com.mercaso.wms.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.repository.vendoritems.jpa.VendorItemJpaDao;
import com.mercaso.wms.infrastructure.repository.vendoritems.jpa.dataobject.VendorItemDo;
import com.mercaso.wms.infrastructure.repository.vendoritems.jpa.mapper.VendorItemDoMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class VendorItemRepositoryImpl implements VendorItemRepository {

    public final VendorItemDoMapper mapper;
    private final VendorItemJpaDao jpaDao;

    @Override
    public VendorItem save(VendorItem domain) {
        VendorItemDo vendorItemDo = mapper.domainToDo(domain);
        vendorItemDo = jpaDao.save(vendorItemDo);
        return mapper.doToDomain(vendorItemDo);
    }

    @Override
    public VendorItem findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public VendorItem update(VendorItem domain) {
        VendorItemDo vendorItemDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == vendorItemDo) {
            throw new WmsBusinessException("Vendor item not found.");
        }
        VendorItemDo target = mapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy", "createdAt"));
        BeanUtils.copyProperties(target, vendorItemDo, ignoreProperties.toArray(new String[0]));
        vendorItemDo = jpaDao.save(vendorItemDo);
        return mapper.doToDomain(vendorItemDo);
    }

    @Override
    public VendorItem findByWarehouseIdAndItemId(UUID warehouseId, UUID itemId) {
        return mapper.doToDomain(jpaDao.findByWarehouseIdAndItemId(warehouseId, itemId));
    }

    @Override
    public Page<VendorItem> findByWarehouseIdAndSkuNumbers(UUID warehouseId, List<String> skuNumbers, Pageable pageable) {
        Page<VendorItemDo> vendorItemDos = jpaDao.findByWarehouseIdAndSkuNumbers(warehouseId, skuNumbers, pageable);
        return vendorItemDos.map(mapper::doToDomain);
    }

    @Override
    public void saveAll(List<VendorItem> vendorItems) {
        jpaDao.saveAll(vendorItems.stream().map(mapper::domainToDo).toList());
    }

    @Override
    public List<VendorItem> findAll() {
        return mapper.doToDomains(jpaDao.findAll());
    }

    @Override
    public List<VendorItem> findBySkuNumbersAndStatus(List<String> skuNumbers, VendorItemStatus status) {
        return mapper.doToDomains(jpaDao.findBySkuNumberInAndStatus(skuNumbers, status));
    }

}
