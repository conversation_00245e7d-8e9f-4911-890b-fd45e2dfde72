package com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa;

import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Limit;
import org.springframework.data.jpa.repository.JpaRepository;

public interface DeliveryTaskJpaDao extends JpaRepository<DeliveryTaskDo, UUID> {

    Optional<DeliveryTaskDo> findByDriverUserIdAndStatusOrderByCreatedAtDesc(UUID driverUserId, DeliveryTaskStatus status,
        Limit limit);

    List<DeliveryTaskDo> findByDeliveryDate(String deliveryDate);

    void deleteAllByIdIn(Collection<UUID> ids);

}