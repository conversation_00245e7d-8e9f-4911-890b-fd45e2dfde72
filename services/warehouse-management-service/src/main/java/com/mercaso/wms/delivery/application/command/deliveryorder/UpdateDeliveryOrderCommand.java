package com.mercaso.wms.delivery.application.command.deliveryorder;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateDeliveryOrderCommand extends BaseCommand {

    private RescheduleType rescheduleType;

    private List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos;

}