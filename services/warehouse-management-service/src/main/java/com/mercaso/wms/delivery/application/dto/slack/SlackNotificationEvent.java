package com.mercaso.wms.delivery.application.dto.slack;

/**
 * Common interface for Slack notification events
 * Defines the contract for objects that can be sent as Slack notifications
 */
public interface SlackNotificationEvent {
    
    /**
     * Gets the webhook URL for this notification
     * 
     * @return the Slack webhook URL
     */
    String getWebhookUrl();
    
    /**
     * Gets the order number associated with this notification
     * 
     * @return the order number for context
     */
    String getOrderNumber();
} 