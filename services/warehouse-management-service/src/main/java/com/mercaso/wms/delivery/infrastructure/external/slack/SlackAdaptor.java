package com.mercaso.wms.delivery.infrastructure.external.slack;

import com.mercaso.wms.delivery.application.dto.slack.BuildDeliveryTaskExceptionEventDto;
import com.mercaso.wms.delivery.application.dto.slack.SlackDeliveryEventDto;
import com.mercaso.wms.delivery.application.dto.slack.SlackNotificationEvent;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.slack.api.Slack;
import com.slack.api.webhook.WebhookResponse;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * Adaptor for Slack integration providing notification capabilities
 * Handles delivery notifications and build task exception alerts
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SlackAdaptor {

    private static final int SUCCESS_CODE = 200;
    
    private final Slack slackClient;

    /**
     * Sends delivery completion notification to Slack
     * 
     * @param event delivery event containing notification details
     */
    public void notifyDeliveryCompletion(SlackDeliveryEventDto event) {
        log.info("Sending delivery completion notification for order: {}", event.getOrderNumber());
        sendSlackNotification(event, SlackNotificationType.DELIVERY_COMPLETION);
    }

    /**
     * Asynchronously sends build task exception notification to Slack
     * 
     * @param exceptionEvent exception event containing error details
     */
    @Async
    public void batchNotifyBuildTaskException(BuildDeliveryTaskExceptionEventDto exceptionEvent) {
        log.info("Sending build task exception notification for order: {}", exceptionEvent.getOrderNumber());
        sendSlackNotification(exceptionEvent, SlackNotificationType.BUILD_TASK_EXCEPTION);
    }
    
    /**
     * Generic method to send any Slack notification event
     * 
     * @param event notification event that implements SlackNotificationEvent
     * @param notificationType standardized notification type
     * @param <T> type of notification event
     */
    private <T extends SlackNotificationEvent> void sendSlackNotification(T event, SlackNotificationType notificationType) {
        try {
            String jsonPayload = SerializationUtils.serialize(event);
            WebhookResponse response = slackClient.send(event.getWebhookUrl(), jsonPayload);
            
            handleSlackResponse(response, notificationType, event.getOrderNumber());
            
        } catch (IOException e) {
            log.error("Failed to send Slack {} for order: {}. Error: {}", 
                notificationType.getDescription(), event.getOrderNumber(), e.getMessage(), e);
        }
    }
    
    /**
     * Handles the Slack webhook response
     * 
     * @param response webhook response from Slack
     * @param notificationType standardized notification type
     * @param orderNumber order number for context
     */
    private void handleSlackResponse(WebhookResponse response, SlackNotificationType notificationType, String orderNumber) {
        if (response.getCode() == SUCCESS_CODE) {
            log.info("Successfully sent {} for order: {}", notificationType.getDescription(), orderNumber);
        } else {
            log.error("Failed to send Slack notification. Response code: {}, body: {}", 
                response.getCode(), response.getBody());
        }
    }
}
