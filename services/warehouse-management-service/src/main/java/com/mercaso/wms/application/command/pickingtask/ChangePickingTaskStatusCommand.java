package com.mercaso.wms.application.command.pickingtask;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class ChangePickingTaskStatusCommand extends BaseCommand {

    private PickingTaskStatus currentStatus;

    private PickingTaskStatus targetStatus;

}
