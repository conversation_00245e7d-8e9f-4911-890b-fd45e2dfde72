package com.mercaso.wms.delivery.application.dto.slack;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlackDeliveryEventDto implements SlackNotificationEvent {

    @JsonProperty("payment_method")
    private String paymentMethod;

    @JsonProperty("customer_name")
    private String customerName;

    @JsonProperty("order_detail_url")
    private String orderDetailUrl;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("additional_notes")
    private String additionalNotes;

    @JsonProperty("total")
    private String total;

    @JsonProperty("driver_name")
    private String driverName;

    @JsonProperty("driver_user_email")
    private String driverUserEmail;

    @JsonProperty("driver_detail")
    private String driverDetail;

    @JsonProperty("missing_items")
    private String missingItems;

    @JsonProperty("added_items")
    private String addedItems;

    @JsonProperty("returned_items")
    private String returnedItems;

    private String webhookUrl;
} 