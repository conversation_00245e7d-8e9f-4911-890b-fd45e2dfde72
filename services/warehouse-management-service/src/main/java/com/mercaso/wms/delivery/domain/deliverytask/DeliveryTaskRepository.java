package com.mercaso.wms.delivery.domain.deliverytask;

import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface DeliveryTaskRepository extends BaseDomainRepository<DeliveryTask, UUID> {

    Optional<DeliveryTask> findByDriverUserIdAndStatus(UUID deliveryTaskId, DeliveryTaskStatus status);

    List<DeliveryTask> saveAll(List<DeliveryTask> deliveryTasks);

    List<DeliveryTask> findByDeliveryDate(String deliveryDate);

    void deleteAllByIdIn(Collection<UUID> ids);

    List<DeliveryTask> updateAll(List<DeliveryTask> deliveryTasks);

    List<DeliveryTask> findByIdIn(Collection<UUID> ids);
}
