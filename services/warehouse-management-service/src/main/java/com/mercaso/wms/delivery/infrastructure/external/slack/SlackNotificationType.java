package com.mercaso.wms.delivery.infrastructure.external.slack;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enumeration of supported Slack notification types
 * Defines standardized notification categories for logging and tracking
 */
@Getter
@RequiredArgsConstructor
public enum SlackNotificationType {

    /**
     * Delivery completion notification
     */
    DELIVERY_COMPLETION("delivery completion notification"),

    /**
     * Build task exception notification
     */
    BUILD_TASK_EXCEPTION("build task exception notification"),
    ;

    private final String description;
}