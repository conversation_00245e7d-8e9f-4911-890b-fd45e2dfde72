package com.mercaso.wms.batch.writer.impl;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(3)
@Slf4j
@RequiredArgsConstructor
public class MfcBreakdownSheetWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        log.info("[MfcBreakdownSheetWriter] Start to write MFC breakdown sheets");
        Map<String, List<ExcelBatchDto>> sourceAndListMap = condition.getSourceAndListMap();
        Map<String, String> deliveryDateMap = new HashMap<>();
        deliveryDateMap.put("deliveryDate", condition.getTaggedWith());
        List<ExcelBatchDto> mfcExcelBatchDtos = sourceAndListMap.get(SourceEnum.MFC.name());
        if (CollectionUtils.isEmpty(mfcExcelBatchDtos)) {
            mfcExcelBatchDtos = new ArrayList<>();
        }
        List<ExcelBatchDto> mfcBeverageBigOrder = new ArrayList<>(mfcExcelBatchDtos.stream()
            .filter(batchDto -> BatchConstants.BEVERAGE.equalsIgnoreCase(batchDto.getPrep())
                || BatchConstants.CLEANING.equalsIgnoreCase(batchDto.getPrep()))
            .filter(ExcelBatchDto::isBigOrder)
            .sorted(Comparator.comparing(ExcelBatchDto::getPos).reversed()
                .thenComparing(ExcelBatchDto::getFrom))
            .toList());

        List<ExcelBatchDto> mfcBeverageSmallOrder = new LinkedList<>(mfcExcelBatchDtos.stream()
            .filter(batchDto -> BatchConstants.BEVERAGE.equalsIgnoreCase(batchDto.getPrep())
                || BatchConstants.CLEANING.equalsIgnoreCase(batchDto.getPrep()))
            .filter(excelBatchDto -> !excelBatchDto.isBigOrder())
            .sorted(Comparator.comparing(ExcelBatchDto::getFrom, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ExcelBatchDto::getItemDescription, Comparator.nullsLast(Comparator.naturalOrder())))
            .toList());
        List<ExcelBatchDto> rdBeverageSmallOrder = filterByFrom(mfcBeverageSmallOrder);
        mfcBeverageSmallOrder.addAll(rdBeverageSmallOrder);

        List<ExcelBatchDto> rdBeverageBigOrder = filterByFrom(mfcBeverageBigOrder);
        if (!CollectionUtils.isEmpty(rdBeverageBigOrder)) {
            mfcBeverageSmallOrder.addAll(rdBeverageBigOrder);
        }

        List<ExcelBatchDto> mfcCandyOrderItems = new LinkedList<>(mfcExcelBatchDtos.stream()
            .filter(batchDto -> !BatchConstants.BEVERAGE.equalsIgnoreCase(batchDto.getPrep())
                && !BatchConstants.CLEANING.equalsIgnoreCase(batchDto.getPrep()))
            .sorted(Comparator.comparing(ExcelBatchDto::getFrom, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ExcelBatchDto::getItemDescription, Comparator.nullsLast(Comparator.naturalOrder())))
            .toList());
        List<ExcelBatchDto> rdCandyOrderItems = filterByFrom(mfcCandyOrderItems);
        mfcCandyOrderItems.addAll(rdCandyOrderItems);

        fillSheet(excelWriter,
            mfcBeverageBigOrder,
            fillConfig,
            deliveryDateMap,
            GeneratedDocNameEnum.MFC_BEVERAGES_BIG_ORDER_WMS_PICKING.getValue());

        fillSheet(excelWriter,
            mfcBeverageSmallOrder,
            fillConfig,
            deliveryDateMap,
            GeneratedDocNameEnum.MFC_BEVERAGES_SMALL_ORDER.getValue());

        fillSheet(excelWriter,
            mfcCandyOrderItems,
            fillConfig,
            deliveryDateMap,
            GeneratedDocNameEnum.MFC_CANDY.getValue());

        condition.setMfcBeverageBigOrder(mfcBeverageBigOrder);
        condition.setMfcBeverageSmallOrder(mfcBeverageSmallOrder);
        condition.setMfcCandyOrderItems(mfcCandyOrderItems);
        log.info("[MfcBreakdownSheetWriter] Finish writing MFC breakdown sheets");
    }

    private List<ExcelBatchDto> filterByFrom(List<ExcelBatchDto> excelBatchDtos) {
        List<ExcelBatchDto> filteredList = excelBatchDtos.stream()
            .filter(batchDto -> batchDto.getFrom().contains(".RD"))
            .toList();
        excelBatchDtos.removeAll(filteredList);
        return filteredList;
    }

    private void fillSheet(ExcelWriter excelWriter,
        List<ExcelBatchDto> excelBatchDtos,
        FillConfig fillConfig,
        Map<String, String> map,
        String sheetName) {
        WriteSheet writeSheet = writerSheet(sheetName).build();
        excelWriter.fill(excelBatchDtos, fillConfig, writeSheet);
        excelWriter.fill(map, writeSheet);
    }

}
