package com.mercaso.wms.infrastructure.schedule;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleTransferScheduler {

    private final BatchRepository batchRepository;

    private final PickingTaskApplicationService pickingTaskApplicationService;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final FeatureFlagsManager featureFlagsManager;

    private static final Integer LOCK_KEY = "FinaleTransferScheduler.finaleInventoryTransfer".hashCode();

    @Scheduled(cron = "0 55 * * * *", zone = "America/Los_Angeles")
    public void finaleInventoryTransfer() {
        finaleInventoryTransfer(null);
    }

    public void finaleInventoryTransfer(String deliveryDate) {
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(LOCK_KEY);
            if (Boolean.TRUE.equals(isAcquired)) {
                log.info("[finaleInventoryTransfer] FinaleTransferScheduler started ");
                if (StringUtils.isEmpty(deliveryDate)) {
                    deliveryDate = DateUtils.getNextDeliveryDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
                }
                log.info("[finaleInventoryTransfer] FinaleTransferScheduler deliveryDate: {}", deliveryDate);
                List<Batch> batchList = batchRepository.findUntransferredBatches(deliveryDate);
                if (CollectionUtils.isEmpty(batchList)) {
                    log.info("[finaleInventoryTransfer] No Untransferred batches found for deliveryDate: {}", deliveryDate);
                    return;
                }
                for (Batch batch : batchList) {
                    if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_FINALE_TRANSFER_FEATURE)) {
                        pickingTaskApplicationService.bulkInventoryTransfer(batch);
                    }
                }
                log.info("[finaleInventoryTransfer] FinaleTransferScheduler completed successfully");
            }
        } finally {
            pgAdvisoryLock.unLock(LOCK_KEY.hashCode());
        }
    }

}

