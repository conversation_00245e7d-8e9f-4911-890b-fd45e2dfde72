package com.mercaso.wms.batch.writer.impl;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(6)
@Slf4j
public class TobaccoTotalSheetWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        Map<String, List<ExcelBatchDto>> sourceAndListMap = condition.getSourceAndListMap();
        List<ExcelBatchDto> excelBatchDtos = sourceAndListMap.get(GeneratedDocNameEnum.COSTCO.getValue());
        if (CollectionUtils.isEmpty(excelBatchDtos)) {
            excelBatchDtos = Lists.newArrayList();
        }
        excelWriter.fill(calculateCostcoTobaccoTotalData(excelBatchDtos),
            writerSheet(GeneratedDocNameEnum.COSTCO_TOBACCO_TOTALS.getValue()).build());
    }

    private List<ExcelBatchDto> calculateCostcoTobaccoTotalData(List<ExcelBatchDto> excelBatchDtos) {
        List<ExcelBatchDto> costcoTobaccoExcelBatchDtos = Lists.newArrayList();
        excelBatchDtos.stream().filter(batchDto -> BatchConstants.TOBACCO_DEPARTMENT.equalsIgnoreCase(batchDto.getDepartment()))
            .collect(Collectors.groupingBy(ExcelBatchDto::getItemNumber))
            .forEach((itemNumber, list) -> costcoTobaccoExcelBatchDtos.add(ExcelBatchDto.itemNumberQtyTotal(list)));
        return costcoTobaccoExcelBatchDtos;
    }
}
