package com.mercaso.wms.interfaces;

import com.mercaso.wms.application.service.PickingTaskApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/picking-task-items")
@RequiredArgsConstructor
public class FailedPickingTaskItemResource {

    private final PickingTaskApplicationService pickingTaskApplicationService;

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/{deliveryDate}/failed-item-export")
    public void failedItemExport(@PathVariable String deliveryDate, HttpServletResponse response) throws IOException {
        String fileName = URLEncoder.encode(deliveryDate + "-failed-picking-task-items-export", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        ByteArrayOutputStream byteArrayOutputStream = pickingTaskApplicationService.failedItemExport(deliveryDate);
        if (byteArrayOutputStream != null && byteArrayOutputStream.size() > 0) {
            response.getOutputStream().write(byteArrayOutputStream.toByteArray());
        }
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @DeleteMapping
    public void deletePickingTaskItems(@RequestParam List<UUID> ids) {
        pickingTaskApplicationService.deletePickingTaskItems(ids);
    }

}
