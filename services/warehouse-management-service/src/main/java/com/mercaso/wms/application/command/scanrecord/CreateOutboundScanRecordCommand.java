package com.mercaso.wms.application.command.scanrecord;

import com.mercaso.wms.application.command.BaseCommand;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CreateOutboundScanRecordCommand extends BaseCommand {

    @NotNull
    private UUID batchItemId;

}
