package com.mercaso.wms.delivery.application.mapper.deliveryorder;

import static java.util.Collections.emptyList;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.dto.DiscountApplication;
import com.mercaso.wms.delivery.application.mapper.customer.CustomerDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
    uses = {DeliveryOrderItemDtoApplicationMapper.class, AddressDtoApplicationMapper.class, CustomerDtoApplicationMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryOrderDtoApplicationMapper extends BaseDtoApplicationMapper<DeliveryOrder, DeliveryOrderDto> {

    DeliveryOrderDtoApplicationMapper INSTANCE = Mappers.getMapper(DeliveryOrderDtoApplicationMapper.class);

    @Mapping(target = "shopifyOrderUrl", expression = "java(shopifyOrderUrl(shopifyOrderUrl, domain))")
    @Mapping(target = "discountApplications", expression = "java(mapDiscountApplications(domain.getDiscountApplications()))")
    DeliveryOrderDto domainToDto(DeliveryOrder domain, String shopifyOrderUrl);

    default String shopifyOrderUrl(String shopifyOrderUrl, DeliveryOrder domain) {
        return domain != null && domain.getShopifyOrderId() != null ? shopifyOrderUrl.concat(domain.getShopifyOrderId()) : null;
    }

    default List<DiscountApplication> mapDiscountApplications(String discountApplications) {
        if (discountApplications == null || discountApplications.isEmpty()) {
            return emptyList();
        }
        try {
            return SerializationUtils.readValue(discountApplications, new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new DeliveryBusinessException("Failed to deserialize discount applications: " + e.getMessage(), e);
        }
    }

}
