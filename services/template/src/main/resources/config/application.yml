server:
  port: 8081

spring:
  application:
    name: template
  datasource:
    username: ims_user
    password: mercaso
    url: ***************************************
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate.dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    locations: classpath:/db/migration
    init-sqls: SET ROLE template_user
  kafka:
    bootstrap-servers: localhost:19092
    consumer:
      group-id: template
      max-poll-records: 10
    producer:
      bootstrap-servers: localhost:19092
    listener:
      concurrency: 1
  cloud:
    vault:
      enabled: false
otel:
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  exporter:
    otlp:
      endpoint: http://localhost:4317
