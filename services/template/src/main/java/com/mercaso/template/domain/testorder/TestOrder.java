package com.mercaso.template.domain.testorder;

import com.mercaso.template.domain.BaseDomain;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Configurable;

import java.util.UUID;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class TestOrder extends BaseDomain {

    private final UUID id;

    private String name;

    private Integer status;


}
