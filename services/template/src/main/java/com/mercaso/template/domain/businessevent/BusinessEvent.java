package com.mercaso.template.domain.businessevent;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.template.domain.BaseDomain;
import com.mercaso.template.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.template.infrastructure.repository.businessevent.BusinessEventRepositoryImpl;
import com.mercaso.template.infrastructure.util.SpringContextUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Getter
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class BusinessEvent extends BaseDomain {

    private final UUID id;

    private final EventTypeEnums type;

    private final JsonNode payload;

    private final String correlationId;

    public BusinessEvent create() {
        return this.getBusinessEventRepository().save(this);
    }

    private BusinessEventRepository getBusinessEventRepository() {
        return SpringContextUtil.getBean(BusinessEventRepositoryImpl.class);
    }

}
