package com.mercaso.template.infrastructure.event;

import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.template.infrastructure.event.kafka.dispatcher.KafkaEventDispatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessEventDispatcher {

    private final ApplicationEventDispatcher applicationEventDispatcher;

    private final KafkaEventDispatcher kafkaEventDispatcher;

    public BusinessEvent dispatch(BusinessEvent businessEvent) {
        log.debug("dispatch.business.event.{}", businessEvent.getType());

        BusinessEvent event = businessEvent.create();

        // application event
        applicationEventDispatcher.publishEvent(event);

        // Send to Kafka (optional)
        kafkaEventDispatcher.send(event);

        return event;
    }

}
