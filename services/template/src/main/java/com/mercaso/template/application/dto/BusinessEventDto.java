package com.mercaso.template.application.dto;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.Instant;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BusinessEventDto extends BaseDto {

    @Schema(required = true)
    private UUID id;

    private String type;

    private JsonNode payload;

    private UUID entityId;

    private String entityType;

    private Instant createdAt;

    private String createdBy;
}
