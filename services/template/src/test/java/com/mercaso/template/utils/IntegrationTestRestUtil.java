package com.mercaso.template.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;


public abstract class IntegrationTestRestUtil {

    private final Environment environment;

    private final LocalHostUriTemplateHandler uriTemplateHandler;

    private final RestTemplate restTemplate = new RestTemplate();

    private final TestRestTemplate testRestTemplate = new TestRestTemplate();

    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    private final String userProfileJson = "";

    public IntegrationTestRestUtil(Environment environment) {
        this.environment = environment;
        this.uriTemplateHandler = new LocalHostUriTemplateHandler(environment);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
    }

    private HttpHeaders createHeaders() {
        return createHeaders(MediaType.APPLICATION_JSON);
    }

    private HttpHeaders createHeaders(MediaType type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        if (userProfileJson != null) {
            headers.set("UserProfile", userProfileJson);
        }
        return headers;
    }


    public <DtoType> DtoType createEntity(String path, Object payload, Class<DtoType> dtoClass) throws Exception {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForObject(url, httpentity, dtoClass);
    }


}
