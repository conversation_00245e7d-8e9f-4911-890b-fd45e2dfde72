plugins {
    id "org.sonarqube"
    id "jacoco"
}

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml"
        def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.branch.name", branchName
        property "sonar.coverage.exclusions",
                "**/model/**,**/annotation/**,**/payload/**,**/command/**, **/mapper/**, **/dto/**, **/mapper/**, **/enums/**, **/adaptor/**, **/config/**, **/exception/**, **/entity/**, **/CommonExceptionController.java"
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
        html.required = true
    }
    executionData = files(
            "$buildDir/jacoco/test.exec",
            "$buildDir/jacoco/integrationTest.exec"
    )
}

test {
    finalizedBy jacocoTestReport
}

dependencies {
    implementation 'org.aspectj:aspectjrt:1.9.7'
    // lucene
    implementation 'org.apache.lucene:lucene-core:9.11.1'
    implementation 'org.apache.lucene:lucene-analysis-common:9.11.1'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'org.apache.skywalking:apm-toolkit-meter:9.2.0'
    // https://mvnrepository.com/artifact/com.squareup.okhttp3/mockwebserver
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
    implementation 'com.squareup:square:41.2.0.20241017'
    implementation 'com.opencsv:opencsv:5.9'
    implementation 'com.mercaso.components:mercaso-business-event-starter:1.0.376'
    // https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java
    implementation 'com.google.protobuf:protobuf-java:4.30.1'
    implementation 'io.pinecone:pinecone-client:4.0.0'
    implementation 'com.mercaso.wms:wms_client:1.0.62513'
    implementation 'com.mercaso.ims:ims_client:1.0.84203'
}

springBoot {
    mainClass = 'com.mercaso.data.Application'
}

integrationTest {
    systemProperty 'spring.profiles.active', 'integration'
}

