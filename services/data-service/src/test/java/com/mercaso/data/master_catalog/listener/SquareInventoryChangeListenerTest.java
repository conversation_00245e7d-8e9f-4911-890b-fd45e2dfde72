package com.mercaso.data.master_catalog.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.alibaba.excel.context.AnalysisContext;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareInventoryChangeDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.utils.entity.MasterCatalogLocationUtils;
import com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils;
import com.mercaso.data.master_catalog.utils.entity.MasterCatalogSquareVariationMappingUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SquareInventoryChangeListenerTest {

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository =
        mock(MasterCatalogSquareVariationMappingRepository.class);
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository =
        mock(MasterCatalogRawDataRepository.class);
    private final MasterCatalogLocationRepository masterCatalogLocationRepository =
        mock(MasterCatalogLocationRepository.class);
    private final AnalysisContext context = mock(AnalysisContext.class);

    private SquareInventoryChangeListener listener;
    private UUID storeId;
    private static final UUID MERCASO_STORE_ID = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        listener = new SquareInventoryChangeListener(
            storeId,
            squareApiAdapter,
            variationMappingRepository,
            masterCatalogRawDataRepository,
            masterCatalogLocationRepository
        );
    }

    @Test
    void testInvoke_WithValidData_Success() {
        // Arrange
        String sku = "DEMO-SKU-001";
        String upc = "123456789012";
        String variationId = "LKQWERTY12";
        String locationId = "LKJHGFDSA1";
        UUID rawDataId = UUID.randomUUID();

        MasterCatalogRawData skuRawData = MasterCatalogRawDataUtils.buildMasterCatalogRawData(MERCASO_STORE_ID);
        skuRawData.setSkuNumber(sku);
        skuRawData.setUpc(upc);

        MasterCatalogRawData upcRawData = MasterCatalogRawDataUtils.buildMasterCatalogRawData(storeId);
        upcRawData.setId(rawDataId);
        upcRawData.setUpc(upc);

        MasterCatalogSquareVariationMapping mapping = MasterCatalogSquareVariationMappingUtils
            .buildMasterCatalogSquareVariationMapping(rawDataId, variationId);

        MasterCatalogLocation location = MasterCatalogLocationUtils
            .buildMasterCatalogLocation(storeId, locationId);

        // Setup mocks
        when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(MERCASO_STORE_ID, "ACTIVE",
            List.of(sku)))
            .thenReturn(List.of(skuRawData));
        when(masterCatalogRawDataRepository.findAllByStoreIdAndUpcIn(storeId, List.of(upc)))
            .thenReturn(List.of(upcRawData));
        when(variationMappingRepository.findAllByMasterCatalogRawDataIdIn(List.of(rawDataId)))
            .thenReturn(List.of(mapping));
        when(masterCatalogLocationRepository.findAllByStoreId(storeId))
            .thenReturn(List.of(location));

        // Act
        listener.invoke(createAdjustment(sku, 100), context);
        listener.doAfterAllAnalysed(context);

        // Assert
        verify(squareApiAdapter).batchChangeInventory(
            eq(storeId),
            eq(List.of(locationId)),
            eq(Map.of(variationId, 100))
        );
    }

    @Test
    void testInvoke_WithNoSkuData_NoAdjustment() {
        // Arrange
        String sku = "DEMO-SKU-002";

        when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(MERCASO_STORE_ID, "ACTIVE",
            List.of(sku))).thenReturn(Collections.emptyList());

        // Act
        listener.invoke(createAdjustment(sku, 100), context);
        listener.doAfterAllAnalysed(context);

        // Assert
        verify(squareApiAdapter, never()).batchChangeInventory(any(), any(), any());
    }

    @Test
    void testInvoke_WithBatchProcessing_Success() {
        // Arrange
        String sku1 = "DEMO-SKU-001";
        String sku2 = "DEMO-SKU-002";
        String upc1 = "123456789012";
        String upc2 = "123456789013";
        String variationId1 = "LKQWERTY12";
        String variationId2 = "LKQWERTY13";
        String locationId = "LKJHGFDSA1";

        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();

        // Setup raw data and mappings
        MasterCatalogRawData skuRawData1 = MasterCatalogRawDataUtils.buildMasterCatalogRawData(MERCASO_STORE_ID);
        skuRawData1.setSkuNumber(sku1);
        skuRawData1.setUpc(upc1);

        MasterCatalogRawData skuRawData2 = MasterCatalogRawDataUtils.buildMasterCatalogRawData(MERCASO_STORE_ID);
        skuRawData2.setSkuNumber(sku2);
        skuRawData2.setUpc(upc2);

        MasterCatalogRawData upcRawData1 = MasterCatalogRawDataUtils.buildMasterCatalogRawData(storeId);
        upcRawData1.setId(rawDataId1);
        upcRawData1.setUpc(upc1);

        MasterCatalogRawData upcRawData2 = MasterCatalogRawDataUtils.buildMasterCatalogRawData(storeId);
        upcRawData2.setId(rawDataId2);
        upcRawData2.setUpc(upc2);

        MasterCatalogSquareVariationMapping mapping1 = MasterCatalogSquareVariationMappingUtils
            .buildMasterCatalogSquareVariationMapping(rawDataId1, variationId1);

        MasterCatalogSquareVariationMapping mapping2 = MasterCatalogSquareVariationMappingUtils
            .buildMasterCatalogSquareVariationMapping(rawDataId2, variationId2);

        MasterCatalogLocation location = MasterCatalogLocationUtils
            .buildMasterCatalogLocation(storeId, locationId);

        // Setup mocks
        when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(
            eq(MERCASO_STORE_ID),
            any(),
            eq(Arrays.asList(sku1, sku2))))
            .thenReturn(Arrays.asList(skuRawData1, skuRawData2));

        when(masterCatalogRawDataRepository.findAllByStoreIdAndUpcIn(
            eq(storeId),
            eq(Arrays.asList(upc1, upc2))))
            .thenReturn(Arrays.asList(upcRawData1, upcRawData2));

        when(variationMappingRepository.findAllByMasterCatalogRawDataIdIn(
            eq(Arrays.asList(rawDataId1, rawDataId2))))
            .thenReturn(Arrays.asList(mapping1, mapping2));

        when(masterCatalogLocationRepository.findAllByStoreId(storeId))
            .thenReturn(List.of(location));

        // Act
        listener.invoke(createAdjustment(sku1, 100), context);
        listener.invoke(createAdjustment(sku2, 200), context);
        listener.doAfterAllAnalysed(context);

        // Assert
        verify(squareApiAdapter).batchChangeInventory(
            eq(storeId),
            eq(List.of(locationId)),
            eq(Map.of(variationId1, 100, variationId2, 200))
        );
    }

    private SquareInventoryChangeDto createAdjustment(String sku, int change) {
        SquareInventoryChangeDto adjustment = new SquareInventoryChangeDto();
        adjustment.setSku(sku);
        adjustment.setChange(change);
        return adjustment;
    }
}