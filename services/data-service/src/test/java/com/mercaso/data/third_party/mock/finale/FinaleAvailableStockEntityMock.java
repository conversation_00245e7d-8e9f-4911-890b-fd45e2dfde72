package com.mercaso.data.third_party.mock.finale;

import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockItemsOnHandEntity;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.RandomUtils;

public class FinaleAvailableStockEntityMock {

    public static FinaleAvailableStockEntity customerEntityMock() {

        FinaleAvailableStockEntity finaleAvailableStockEntity = new FinaleAvailableStockEntity();

        finaleAvailableStockEntity.setId(RandomUtils.nextLong());
        finaleAvailableStockEntity.setName("Mock Product");
        finaleAvailableStockEntity.setMfcQoh(56);
        finaleAvailableStockEntity.setShopifyQoh(141000);
        finaleAvailableStockEntity.setSku(RandomUtils.nextLong() + "");
        finaleAvailableStockEntity.setReservationsQoh(331);
        finaleAvailableStockEntity.setStockSublocations("02-25-A-2, Main");
        finaleAvailableStockEntity.setRecordLastUpdated(new Timestamp(System.currentTimeMillis()));
        finaleAvailableStockEntity.setCreatedAt(OffsetDateTime.now(ZoneOffset.UTC));
        finaleAvailableStockEntity.setUpdatedAt(OffsetDateTime.now(ZoneOffset.UTC));
        finaleAvailableStockEntity.setStockItemsOnHand(buildStockItemsOnHandEntityMock());
        return finaleAvailableStockEntity;
    }

    private static List<FinaleAvailableStockItemsOnHandEntity> buildStockItemsOnHandEntityMock() {
        List<FinaleAvailableStockItemsOnHandEntity> stockItemsOnHandList = new ArrayList<>();

        FinaleAvailableStockItemsOnHandEntity item1 = new FinaleAvailableStockItemsOnHandEntity();
        item1.setQuantityOnHand("56");
        FinaleAvailableStockItemsOnHandEntity.Location location1 = new FinaleAvailableStockItemsOnHandEntity.Location();
        location1.setName("02-25-A-2");
        location1.setFacilityUrl("/mercaso/api/facility/101505");
        item1.setSubLocation(location1);
        stockItemsOnHandList.add(item1);

        FinaleAvailableStockItemsOnHandEntity item2 = new FinaleAvailableStockItemsOnHandEntity();
        item2.setQuantityOnHand("141000");
        FinaleAvailableStockItemsOnHandEntity.Location location2 = new FinaleAvailableStockItemsOnHandEntity.Location();
        location2.setName("Main");
        location2.setFacilityUrl("/mercaso/api/facility/100001");
        item2.setSubLocation(location2);
        stockItemsOnHandList.add(item2);

        return stockItemsOnHandList;
    }
}

