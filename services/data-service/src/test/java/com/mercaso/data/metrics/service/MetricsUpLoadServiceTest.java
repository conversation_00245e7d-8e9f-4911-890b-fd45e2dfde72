package com.mercaso.data.metrics.service;

import com.mercaso.data.metrics.adapter.MetricsS3OperationAdapter;
import com.mercaso.data.metrics.dto.DocumentResponseDto;
import com.mercaso.data.metrics.dto.UploadDocumentRequestDto;
import com.mercaso.data.metrics.enums.UploadFileType;
import com.mercaso.data.metrics.service.impl.MetricsUpLoadServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MetricsUpLoadServiceTest {

    @Mock
    private MetricsS3OperationAdapter metricsS3OperationAdapter;

    @InjectMocks
    private MetricsUpLoadServiceImpl metricsUpLoadService;

    private MultipartFile validFile;

    @BeforeEach
    void setUp() {
        validFile = new MockMultipartFile(
            "test.txt",
            "test.txt",
            "text/plain",
            "test content".getBytes()
        );
    }

    @Test
    void upload_WithValidFile_ShouldUploadSuccessfully() {
        // Arrange
        UploadFileType fileType = UploadFileType.ANY;
        when(metricsS3OperationAdapter.upload(any(UploadDocumentRequestDto.class))).thenReturn(any(DocumentResponseDto.class));

        // Act
        assertDoesNotThrow(() -> metricsUpLoadService.upload(fileType, validFile));

        // Assert
        verify(metricsS3OperationAdapter, times(1)).upload(any(UploadDocumentRequestDto.class));
    }

    @Test
    void upload_WithNullFile_ShouldThrowIllegalArgumentException() {
        // Arrange
        UploadFileType fileType = UploadFileType.ANY;

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> metricsUpLoadService.upload(fileType, null)
        );
        assertEquals("File cannot be empty", exception.getMessage());
        verify(metricsS3OperationAdapter, never()).upload(any());
    }

    @Test
    void upload_WithEmptyFile_ShouldThrowIllegalArgumentException() {
        // Arrange
        UploadFileType fileType = UploadFileType.ANY;
        MultipartFile emptyFile = new MockMultipartFile(
            "empty.txt",
            new byte[0]
        );

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> metricsUpLoadService.upload(fileType, emptyFile)
        );
        assertEquals("File cannot be empty", exception.getMessage());
        verify(metricsS3OperationAdapter, never()).upload(any());
    }

    @Test
    void upload_WhenS3UploadFails_ShouldThrowRuntimeException() {
        // Arrange
        UploadFileType fileType = UploadFileType.ANY;
        doThrow(new RuntimeException("S3 upload failed"))
            .when(metricsS3OperationAdapter)
            .upload(any(UploadDocumentRequestDto.class));

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> metricsUpLoadService.upload(fileType, validFile)
        );
        assertTrue(exception.getMessage().contains("upload file content failed"));
        verify(metricsS3OperationAdapter, times(1)).upload(any());
    }
}
