package com.mercaso.data.master_catalog.service;

import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.CATEGORY;
import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.IMAGE;
import static com.mercaso.data.master_catalog.enums.square.CatalogObjectTypes.ITEM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsRequestDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsResponseDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.impl.SquareItemSyncServiceImpl;
import com.mercaso.data.utils.FileUtils;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.CatalogCategory;
import com.squareup.square.models.CatalogImage;
import com.squareup.square.models.CatalogItem;
import com.squareup.square.models.CatalogItemVariation;
import com.squareup.square.models.CatalogObject;
import com.squareup.square.models.CatalogObjectCategory;
import com.squareup.square.models.Money;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;

class SquareItemSyncServiceTest {

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);
    private final MasterCatalogRawDataRepository rawDataRepository = mock(MasterCatalogRawDataRepository.class);
    private final MasterCatalogImageRepository imageRepository = mock(MasterCatalogImageRepository.class);
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository = mock(
        MasterCatalogSquareVariationMappingRepository.class);
    private final S3OperationAdapter s3OperationAdapter = mock(S3OperationAdapter.class);
    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository = mock(
        MasterCatalogSquareRawDataRepository.class);
    private final MasterCatalogSquareVariationMappingRepository masterCatalogSquareVariationMappingRepository = mock(
        MasterCatalogSquareVariationMappingRepository.class);

    private final SquareItemSyncService squareItemSyncService = new SquareItemSyncServiceImpl(
        squareApiAdapter,
        rawDataRepository,
        imageRepository,
        variationMappingRepository,
        s3OperationAdapter,
        masterCatalogSquareRawDataRepository,
        masterCatalogSquareVariationMappingRepository);

    private UUID storeId;
    private Store store;
    private SquareDataSyncRequest request = new SquareDataSyncRequest();

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        store = new Store();
        store.setId(storeId);
        store.setIntegrationType("SQUARE");
        request.setStoreId(storeId);
    }


    @Test
    void testSyncSquareData_WithImages() {
        String itemId = UUID.randomUUID().toString();
        String itemIdNullUpc = UUID.randomUUID().toString();
        String variationId = UUID.randomUUID().toString();
        String variationIdNullUpu = UUID.randomUUID().toString();
        String categoryId = UUID.randomUUID().toString();
        String imageId = UUID.randomUUID().toString();

        // Mock latest sync time
        when(rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock CatalogItemVariation
        CatalogItemVariation variationData = new CatalogItemVariation.Builder()
            .itemId(itemId)
            .upc("UPC12345")
            .priceMoney(new Money.Builder().amount(1000L).currency("USD").build())
            .build();

        CatalogObject variation = new CatalogObject.Builder("ITEM_VARIATION", variationId)
            .itemVariationData(variationData)
            .updatedAt("2024-10-10T10:00:00Z")
            .build();

        // Mock CatalogItem
        CatalogItem itemData = new CatalogItem.Builder()
            .name("Test Item")
            .variations(Collections.singletonList(variation))
            .categoryId(categoryId)
            .isArchived(false)
            .imageIds(Collections.singletonList(imageId))
            .reportingCategory(new CatalogObjectCategory.Builder().id(categoryId).build())
            .build();

        CatalogObject item = new CatalogObject.Builder(ITEM.name(), itemId)
            .itemData(itemData)
            .build();

        // Mock CatalogItem null upc

        CatalogItemVariation variationDataNullUpc = new CatalogItemVariation.Builder()
            .itemId(itemIdNullUpc)
            .upc(null)
            .priceMoney(new Money.Builder().amount(1000L).currency("USD").build())
            .build();

        CatalogObject variationNullUpc = new CatalogObject.Builder("ITEM_VARIATION", variationIdNullUpu)
            .itemVariationData(variationDataNullUpc)
            .updatedAt("2024-10-10T10:00:00Z")
            .build();

        CatalogItem itemDataUpcNull = new CatalogItem.Builder()
            .name("Test Item")
            .variations(Collections.singletonList(variationNullUpc))
            .categoryId(categoryId)
            .isArchived(false)
            .imageIds(Collections.singletonList(imageId))
            .reportingCategory(new CatalogObjectCategory.Builder().id(categoryId).build())
            .build();

        CatalogObject itemUpcNull = new CatalogObject.Builder(ITEM.name(), itemIdNullUpc)
            .itemData(itemDataUpcNull)
            .build();

        // Mock CatalogCategory
        CatalogCategory categoryData = new CatalogCategory.Builder()
            .name("Test Category")
            .build();

        CatalogObject category = new CatalogObject.Builder(CATEGORY.name(), categoryId)
            .categoryData(categoryData)
            .build();

        // Mock CatalogImage
        CatalogImage imageData = new CatalogImage.Builder()
            .url("http://example.com/image.jpg")
            .build();

        CatalogObject imageObject = new CatalogObject.Builder(IMAGE.name(), imageId)
            .imageData(imageData)
            .build();

        // Mock API response
        SearchCatalogObjectsResponseDto response = SearchCatalogObjectsResponseDto.builder()
            .objects(List.of(item, itemUpcNull))
            .relatedObjects(Arrays.asList(category, imageObject))
            .build();

        // Mock squareApiAdapter.searchCatalogObjects to return the mocked response
        when(squareApiAdapter.searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class)))
            .thenReturn(response);

        // Use two MockedStatic to mock both UUID.randomUUID() and FileUtils.download
        UUID fixedUuid = UUID.fromString("da88de20-b9d0-4ff6-8f7a-03de7b64ee48");
        try (MockedStatic<UUID> mockedUuid = mockStatic(UUID.class);
            MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class)) {

            // Mock UUID.randomUUID() to return a fixed value
            mockedUuid.when(UUID::randomUUID).thenReturn(fixedUuid);

            // Mock FileUtils.download to return image bytes
            mockedFileUtils.when(() -> FileUtils.download("http://example.com/image.jpg"))
                .thenReturn("image content".getBytes());

            // Mock s3OperationAdapter.upload
            DocumentResponseDto documentResponseDto = new DocumentResponseDto();
            documentResponseDto.setSignedUrl("http://s3.amazonaws.com/bucket/image.jpg");

            when(s3OperationAdapter.upload(any()))
                .thenReturn(documentResponseDto);

            // Mock save operations
            when(rawDataRepository.saveAll(any()))
                .thenAnswer(invocation -> {
                    List<MasterCatalogRawData> rawDataList = invocation.getArgument(0);
                    // No need to set IDs as we've mocked UUID.randomUUID()
                    return rawDataList;
                });

            when(imageRepository.saveAll(any()))
                .thenAnswer(invocation -> {
                    List<MasterCatalogImage> imageList = invocation.getArgument(0);
                    return imageList;
                });

            when(variationMappingRepository.saveAll(any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

            UUID sourceId = UUID.randomUUID();
            when(masterCatalogSquareRawDataRepository.save(any()))
                .thenAnswer(invocation -> {
                    MasterCatalogSquareRawData rawData = invocation.getArgument(0);
                    rawData.setId(sourceId);
                    rawData.setData(SerializationUtils.readTree("{ \"key\": \"value\" }"));
                    return rawData;
                });

            // Execute the method
            squareItemSyncService.syncItems(request);

            // Verify that images are saved
            ArgumentCaptor<List<MasterCatalogImage>> imageCaptor = ArgumentCaptor.forClass(List.class);
            verify(imageRepository, times(1)).saveAll(imageCaptor.capture());
            List<MasterCatalogImage> savedImages = imageCaptor.getValue();
            assertEquals(1, savedImages.size());
            MasterCatalogImage savedImage = savedImages.get(0);
            assertTrue(savedImage.getImagePath().contains(StringUtils.right(String.valueOf(storeId), 6) + "_UPC12345"));

            // Verify raw data is saved
            ArgumentCaptor<List<MasterCatalogRawData>> rawDataCaptor = ArgumentCaptor.forClass(List.class);
            verify(rawDataRepository, times(1)).saveAll(rawDataCaptor.capture());
            List<MasterCatalogRawData> savedRawDataList = rawDataCaptor.getValue();
            assertEquals(1, savedRawDataList.size());
            MasterCatalogRawData savedRawData = savedRawDataList.get(0);

            // Verify variation mappings
            verify(variationMappingRepository, times(1)).saveAll(any());

            // Additional assertions
            assertEquals("Test Item", savedRawData.getName());
            assertEquals("Test Category", savedRawData.getCategory());
            assertEquals("UPC12345", savedRawData.getUpc());
            assertEquals(storeId, savedRawData.getStoreId());
            assertEquals(sourceId.toString(), savedRawData.getSourceId());
            assertEquals(RawDataStatus.DRAFT.name(), savedRawData.getStatus());
            assertEquals(savedRawData.getId(), savedImage.getMasterCatalogRawDataId());

            // Verify ID is our mocked fixed value
            assertEquals(fixedUuid, savedRawData.getId());
        }
    }

    @Test
    void testSyncItems_NoCatalogSquareDataFound() {

        when(rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock API response with no items
        SearchCatalogObjectsResponseDto response = SearchCatalogObjectsResponseDto.builder()
            .objects(Collections.emptyList())
            .relatedObjects(Collections.emptyList())
            .build();

        // Mock squareApiAdapter.searchCatalogObjects to return the empty response
        when(squareApiAdapter.searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class)))
            .thenReturn(response);

        // Execute the method
        squareItemSyncService.syncItems(request);

        verify(rawDataRepository, times(1)).findTopByStoreIdOrderByCreatedAtDesc(storeId);
        verify(squareApiAdapter, times(1)).searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class));
        verifyNoMoreInteractions(rawDataRepository, squareApiAdapter);
        verifyNoInteractions(imageRepository, variationMappingRepository);
    }

    @Test
    void testSyncItems_WithCatalogSquareData() {
        String itemId = UUID.randomUUID().toString();
        String variationId = UUID.randomUUID().toString();
        String categoryId = UUID.randomUUID().toString();

        // Mock latest sync time
        when(rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock CatalogItemVariation
        CatalogItemVariation variationData = new CatalogItemVariation.Builder()
            .itemId(itemId)
            .upc("UPC12345")
            .priceMoney(new Money.Builder().amount(100L).currency("USD").build())
            .build();

        CatalogObject variation = new CatalogObject.Builder("ITEM_VARIATION", variationId)
            .itemVariationData(variationData)
            .updatedAt("2024-10-10T10:00:00Z")
            .build();

        // Mock CatalogItem
        CatalogItem itemData = new CatalogItem.Builder()
            .name("Test Item")
            .variations(Collections.singletonList(variation))
            .categoryId(categoryId)
            .isArchived(false)
            .reportingCategory(new CatalogObjectCategory.Builder().id(categoryId).build())
            .imageIds(Collections.emptyList())
            .build();

        CatalogObject item = new CatalogObject.Builder(ITEM.name(), itemId)
            .itemData(itemData)
            .build();

        // Mock CatalogCategory
        CatalogCategory categoryData = new CatalogCategory.Builder()
            .name("Test Category")
            .build();

        CatalogObject category = new CatalogObject.Builder(CATEGORY.name(), categoryId)
            .categoryData(categoryData)
            .build();

        // Mock API response
        SearchCatalogObjectsResponseDto response = SearchCatalogObjectsResponseDto.builder()
            .objects(Collections.singletonList(item))
            .relatedObjects(Collections.singletonList(category))
            .build();

        // Mock squareApiAdapter.searchCatalogObjects to return the mocked response
        when(squareApiAdapter.searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class)))
            .thenReturn(response);

        // Use MockedStatic to mock UUID.randomUUID()
        UUID fixedUuid = UUID.fromString("08d41c82-689b-4ecc-b682-a71c25d52642");
        try (MockedStatic<UUID> mockedUuid = mockStatic(UUID.class)) {
            // Mock UUID.randomUUID() to return a fixed value
            mockedUuid.when(UUID::randomUUID).thenReturn(fixedUuid);

            // Mock save operations
            when(masterCatalogSquareRawDataRepository.save(any()))
                .thenAnswer(invocation -> {
                    MasterCatalogSquareRawData rawData = invocation.getArgument(0);
                    rawData.setId(UUID.randomUUID());
                    return rawData;
                });

            when(rawDataRepository.saveAll(any()))
                .thenAnswer(invocation -> {
                    List<MasterCatalogRawData> rawDataList = invocation.getArgument(0);
                    // No need to set IDs as we've mocked UUID.randomUUID()
                    return rawDataList;
                });

            when(variationMappingRepository.saveAll(any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

            // Execute the method
            squareItemSyncService.syncItems(request);

            // Verify raw data is saved
            ArgumentCaptor<List<MasterCatalogRawData>> rawDataCaptor = ArgumentCaptor.forClass(List.class);
            verify(rawDataRepository, times(1)).saveAll(rawDataCaptor.capture());
            List<MasterCatalogRawData> savedRawDataList = rawDataCaptor.getValue();
            // Adjust the expected size based on the variations
            assertEquals(1, savedRawDataList.size());
            MasterCatalogRawData savedRawData = savedRawDataList.get(0);

            // Verify raw data properties
            assertEquals("Test Item", savedRawData.getName());
            assertEquals("Test Category", savedRawData.getCategory());
            assertEquals("UPC12345", savedRawData.getUpc());
            assertEquals(new BigDecimal("1"), savedRawData.getPrice()); // Price is amount divided by 100
            assertEquals(storeId, savedRawData.getStoreId());
            assertEquals(RawDataStatus.DRAFT.name(), savedRawData.getStatus());

            // Verify ID is our mocked fixed value
            assertEquals(fixedUuid, savedRawData.getId());

            // Verify variation mappings
            ArgumentCaptor<List<MasterCatalogSquareVariationMapping>> mappingCaptor = ArgumentCaptor.forClass(List.class);
            verify(variationMappingRepository, times(1)).saveAll(mappingCaptor.capture());
            List<MasterCatalogSquareVariationMapping> savedMappings = mappingCaptor.getValue();
            assertEquals(1, savedMappings.size());
            MasterCatalogSquareVariationMapping savedMapping = savedMappings.get(0);
            assertEquals(variationId, savedMapping.getVariationId());
            assertEquals(savedRawData.getId(), savedMapping.getMasterCatalogRawDataId());

            // Since there are no images, imageRepository.saveAll() should not be called
            verify(imageRepository, never()).saveAll(any());
        }
    }

    @Test
    void testSyncItems_WithStoreIdAndCatalogSquareData() {
        String itemId = UUID.randomUUID().toString();
        String variationId = UUID.randomUUID().toString();
        String categoryId = UUID.randomUUID().toString();

        // Mock latest sync time
        when(rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock CatalogItemVariation
        CatalogItemVariation variationData = new CatalogItemVariation.Builder()
            .itemId(itemId)
            .upc("UPC12345")
            .sku("SKU12345")
            .priceMoney(new Money.Builder().amount(100L).currency("USD").build())
            .build();

        CatalogObject variation = new CatalogObject.Builder("ITEM_VARIATION", variationId)
            .itemVariationData(variationData)
            .updatedAt("2024-10-10T10:00:00Z")
            .build();

        // Mock CatalogItem
        CatalogItem itemData = new CatalogItem.Builder()
            .name("Test Item")
            .variations(Collections.singletonList(variation))
            .categoryId(categoryId)
            .isArchived(false)
            .reportingCategory(new CatalogObjectCategory.Builder().id(categoryId).build())
            .imageIds(Collections.emptyList())
            .build();

        CatalogObject item = new CatalogObject.Builder(ITEM.name(), itemId)
            .itemData(itemData)
            .build();

        // Mock CatalogCategory
        CatalogCategory categoryData = new CatalogCategory.Builder()
            .name("Test Category")
            .build();

        CatalogObject category = new CatalogObject.Builder(CATEGORY.name(), categoryId)
            .categoryData(categoryData)
            .build();

        // Mock API response
        SearchCatalogObjectsResponseDto response = SearchCatalogObjectsResponseDto.builder()
            .objects(Collections.singletonList(item))
            .relatedObjects(Collections.singletonList(category))
            .build();

        // Mock squareApiAdapter.searchCatalogObjects to return the mocked response
        when(squareApiAdapter.searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class)))
            .thenReturn(response);

        // Use MockedStatic to mock UUID.randomUUID()
        UUID fixedUuid = UUID.fromString("367f853b-4ec4-4cb8-a925-477902a84598");
        try (MockedStatic<UUID> mockedUuid = mockStatic(UUID.class)) {
            // Mock UUID.randomUUID() to return a fixed value
            mockedUuid.when(UUID::randomUUID).thenReturn(fixedUuid);

            // Mock save operations
            when(masterCatalogSquareRawDataRepository.save(any()))
                .thenAnswer(invocation -> {
                    MasterCatalogSquareRawData rawData = invocation.getArgument(0);
                    rawData.setId(UUID.randomUUID());
                    return rawData;
                });

            when(rawDataRepository.saveAll(any()))
                .thenAnswer(invocation -> {
                    List<MasterCatalogRawData> rawDataList = invocation.getArgument(0);
                    // No need to set IDs as we've mocked UUID.randomUUID()
                    return rawDataList;
                });

            when(variationMappingRepository.saveAll(any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

            // Execute the method
            squareItemSyncService.syncItems(request);

            // Verify raw data is saved
            ArgumentCaptor<List<MasterCatalogRawData>> rawDataCaptor = ArgumentCaptor.forClass(List.class);
            verify(rawDataRepository, times(1)).saveAll(rawDataCaptor.capture());
            List<MasterCatalogRawData> savedRawDataList = rawDataCaptor.getValue();
            // Adjust the expected size based on the variations
            assertEquals(1, savedRawDataList.size());
            MasterCatalogRawData savedRawData = savedRawDataList.get(0);

            // Verify raw data properties
            assertEquals("Test Item", savedRawData.getName());
            assertEquals("Test Category", savedRawData.getCategory());
            assertEquals("UPC12345", savedRawData.getUpc());
            assertEquals("SKU12345", savedRawData.getSkuNumber());
            assertEquals(new BigDecimal("1"), savedRawData.getPrice()); // Price is amount divided by 100
            assertEquals(storeId, savedRawData.getStoreId());
            assertEquals(RawDataStatus.DRAFT.name(), savedRawData.getStatus());

            // Verify ID is our mocked fixed value
            assertEquals(fixedUuid, savedRawData.getId());

            // Verify variation mappings
            ArgumentCaptor<List<MasterCatalogSquareVariationMapping>> mappingCaptor = ArgumentCaptor.forClass(List.class);
            verify(variationMappingRepository, times(1)).saveAll(mappingCaptor.capture());
            List<MasterCatalogSquareVariationMapping> savedMappings = mappingCaptor.getValue();
            assertEquals(1, savedMappings.size());
            MasterCatalogSquareVariationMapping savedMapping = savedMappings.get(0);
            assertEquals(variationId, savedMapping.getVariationId());
            assertEquals(savedRawData.getId(), savedMapping.getMasterCatalogRawDataId());

            // Since there are no images, imageRepository.saveAll() should not be called
            verify(imageRepository, never()).saveAll(any());
        }
    }

    @Test
    void testSyncSquareData_NoRawDataGenerated() {
        String itemId = UUID.randomUUID().toString();
        String categoryId = UUID.randomUUID().toString();

        // Mock latest sync time
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenAnswer(invocation -> {
                MasterCatalogSquareRawData rawData = invocation.getArgument(0);
                rawData.setId(UUID.randomUUID());
                return rawData;
            });

        when(rawDataRepository.findTopByStoreIdOrderByCreatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock CatalogItem with no variations
        CatalogItem itemData = new CatalogItem.Builder()
            .name("Item With No Variations")
            .variations(Collections.emptyList())
            .categoryId(categoryId)
            .isArchived(false)
            .build();

        CatalogObject item = new CatalogObject.Builder(ITEM.name(), itemId)
            .itemData(itemData)
            .build();

        // Mock CatalogCategory
        CatalogCategory categoryData = new CatalogCategory.Builder()
            .name("Test Category")
            .build();

        CatalogObject category = new CatalogObject.Builder(CATEGORY.name(), categoryId)
            .categoryData(categoryData)
            .build();

        // Mock API response
        SearchCatalogObjectsResponseDto response = SearchCatalogObjectsResponseDto.builder()
            .objects(Collections.singletonList(item))
            .relatedObjects(Collections.singletonList(category))
            .build();

        // Mock squareApiAdapter.searchCatalogObjects to return the mocked response
        when(squareApiAdapter.searchCatalogObjects(eq(storeId), any(SearchCatalogObjectsRequestDto.class)))
            .thenReturn(response);

        // Execute the method
        squareItemSyncService.syncItems(request);

        // Verify that saveAll methods are never called because rawDataList is empty
        verify(rawDataRepository, never()).saveAll(any());
        verify(imageRepository, never()).saveAll(any());
        verify(variationMappingRepository, never()).saveAll(any());
    }
}