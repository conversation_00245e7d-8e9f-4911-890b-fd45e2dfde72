package com.mercaso.data.recommendation.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.recommendation.dto.ItemRecommendationRecordDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;
import com.mercaso.data.recommendation.utils.resource_utils.ItemRecommendationResourceApiUtils;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

/**
 * Integration test for ItemRecommendationResource.
 */
class ItemRecommendationResourceIT extends AbstractIT {

  @Autowired
  private ItemRecommendationResourceApiUtils itemRecommendationResourceApiUtils;

  @Autowired
  private ItemRecommendationRepository itemRecommendationRepository;

  private String storeId;
  private List<ItemRecommendation> testRecommendations;

  @BeforeEach
  void setUp() {
    // Initialize test data
    storeId = "store-" + UUID.randomUUID();
    testRecommendations = new ArrayList<>();

    // Create first recommendation with high reasonValue for sorting test
    ItemRecommendation recommendation1 = new ItemRecommendation();
    recommendation1.setStoreId(storeId);
    recommendation1.setSkuNumber("SKU123");
    recommendation1.setSkuName("Test Product 1");
    recommendation1.setProductId("PROD123");
    recommendation1.setDepartment("Electronics");
    recommendation1.setReason("PURCHASE_RATE");
    recommendation1.setReasonValue("85.5"); // This will be rounded to 86
    recommendation1.setCreatedAt(Instant.now());
    recommendation1.setUpdatedAt(Instant.now());

    // Create second recommendation with lower reasonValue
    ItemRecommendation recommendation2 = new ItemRecommendation();
    recommendation2.setStoreId(storeId);
    recommendation2.setSkuNumber("SKU456");
    recommendation2.setSkuName("Test Product 2");
    recommendation2.setProductId("PROD456");
    recommendation2.setDepartment("Home");
    recommendation2.setReason("VIEW_COUNT");
    recommendation2.setReasonValue("42.7"); // This will be rounded to 43
    recommendation2.setCreatedAt(Instant.now());
    recommendation2.setUpdatedAt(Instant.now());

    // Create third recommendation with different store ID (should not be returned in the test)
    ItemRecommendation recommendation3 = new ItemRecommendation();
    recommendation3.setStoreId("different-store");
    recommendation3.setSkuNumber("SKU789");
    recommendation3.setSkuName("Test Product 3");
    recommendation3.setProductId("PROD789");
    recommendation3.setDepartment("Clothing");
    recommendation3.setReason("POPULARITY");
    recommendation3.setReasonValue("99.9");
    recommendation3.setCreatedAt(Instant.now());
    recommendation3.setUpdatedAt(Instant.now());

    // Save all test entities to the database
    testRecommendations.add(itemRecommendationRepository.save(recommendation1));
    testRecommendations.add(itemRecommendationRepository.save(recommendation2));
    testRecommendations.add(itemRecommendationRepository.save(recommendation3));
  }

  @AfterEach
  void tearDown() {
    // Clean up test data
    itemRecommendationRepository.deleteAll(testRecommendations);
  }

  @Test
  @DisplayName("Search item recommendations")
  void searchItemRecommendations() throws Exception {
    // When: Perform the request with actual API call
    PageableResponse<ItemRecommendationRecordDto> result = itemRecommendationResourceApiUtils.searchItemRecommendations(
        storeId, 0, 10);

    // Verify response structure
    assertNotNull(result);
    assertEquals(0, result.getPageNumber());
    assertEquals(10, result.getPageSize());
    assertEquals(1, result.getTotalPages());
    assertEquals(2, result.getTotalElements());

    // Verify data - expect items to be sorted by reasonValue DESC
    List<ItemRecommendationRecordDto> resultData = result.getData();
    assertEquals(2, resultData.size());

    // Verify first item (should be recommendation1 with higher reasonValue)
    ItemRecommendationRecordDto firstItem = resultData.get(0);
    assertEquals("SKU123", firstItem.getSku());
    assertEquals("PROD123", firstItem.getProductId());
    assertEquals("PURCHASE_RATE", firstItem.getReason().getType());
    assertEquals(86, firstItem.getReason().getValue()); // Rounded from 85.5

    // Verify second item
    ItemRecommendationRecordDto secondItem = resultData.get(1);
    assertEquals("SKU456", secondItem.getSku());
    assertEquals("PROD456", secondItem.getProductId());
    assertEquals("VIEW_COUNT", secondItem.getReason().getType());
    assertEquals(43, secondItem.getReason().getValue()); // Rounded from 42.7
  }
}