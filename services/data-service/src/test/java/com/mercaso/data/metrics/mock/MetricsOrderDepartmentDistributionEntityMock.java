package com.mercaso.data.metrics.mock;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.metrics.entity.MetricsOrderDepartmentDistributionEntity;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MetricsOrderDepartmentDistributionEntityMock {

    public static MetricsOrderDepartmentDistributionEntity metricsOrderDepartmentDistributionEntityMock(String addressId,
        String dateType) {

        MetricsOrderDepartmentDistributionEntity metricsOrderDepartmentDistributionEntity = new MetricsOrderDepartmentDistributionEntity();

        metricsOrderDepartmentDistributionEntity.setAddressId(addressId);
        metricsOrderDepartmentDistributionEntity.setDateType(dateType);
        metricsOrderDepartmentDistributionEntity.setDate(LocalDateTime.now());
        metricsOrderDepartmentDistributionEntity.setCreatedAt(LocalDateTime.now());
        metricsOrderDepartmentDistributionEntity.setDateLength(11);

        List<Map<String, String>> itemsList = new ArrayList<>();
        itemsList.add(new HashMap<>() {{
            put("rank", "1");
            put("depName", "All");
            put("depAmount", "3000.00");
            put("depProportion", "60.00");
        }});
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.valueToTree(itemsList);

        metricsOrderDepartmentDistributionEntity.setDeps(jsonNode);

        return metricsOrderDepartmentDistributionEntity;
    }

}
