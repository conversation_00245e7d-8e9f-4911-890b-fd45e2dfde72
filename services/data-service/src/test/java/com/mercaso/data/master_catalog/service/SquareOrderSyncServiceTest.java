package com.mercaso.data.master_catalog.service;

import static com.mercaso.data.master_catalog.constants.SquareConstants.DAYS_IN_ONE_YEAR;
import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveOrdersRequestDto;
import com.mercaso.data.master_catalog.dto.square.LineItemsDto;
import com.mercaso.data.master_catalog.dto.square.OrdersDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrderLineItem;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.impl.SquareOrderSyncServiceImpl;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.OrderReturn;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class SquareOrderSyncServiceTest {

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository = mock(
        MasterCatalogSquareVariationMappingRepository.class);
    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository = mock(
        MasterCatalogSquareRawDataRepository.class);
    private final MasterCatalogSquareOrderRepository masterCatalogSquareOrderRepository = mock(
        MasterCatalogSquareOrderRepository.class);
    private final MasterCatalogSquareOrderLineItemRepository masterCatalogSquareOrderLineItemRepository = mock(
        MasterCatalogSquareOrderLineItemRepository.class);
    private final SquareOrderSyncServiceImpl squareOrderSyncService = new SquareOrderSyncServiceImpl(
        squareApiAdapter,
        masterCatalogSquareRawDataRepository,
        variationMappingRepository,
        masterCatalogSquareOrderRepository,
        masterCatalogSquareOrderLineItemRepository);
    private UUID storeId;
    private List<String> presentAtLocationIds;
    private SquareDataSyncRequest request = new SquareDataSyncRequest();

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        presentAtLocationIds = Arrays.asList(UUID.randomUUID().toString(),
            UUID.randomUUID().toString());
        request.setStoreId(storeId);
    }

    @Test
    void testSyncOrder_NoOrder() {

        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getOrderCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        //Mock fetch order to return an empty list
        List<OrdersDto> orderList = Collections.emptyList();
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class))).thenReturn(orderList);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify the fetchOrder was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(retrieveCreateAt.toString(), capturedRequest.getCreateAt());

        // Verify saveRawOrderData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(
            any(MasterCatalogSquareRawData.class));

        // Verify saveOrder was not called
        verify(masterCatalogSquareOrderRepository, never()).saveAll(any(List.class));

        // Verify saveOrderLineItem was not called
        verify(masterCatalogSquareOrderLineItemRepository, never()).saveAll(any(List.class));


    }

    @Test
    void testSyncOrder_NoExistingOrderWithOrderWithinOneYear() {
        // Mock retrieveLatestSyncTimeForOrder to return a fixed Instant
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.empty());

        // Mock fetchOrders to return a list
        String retrieveCreateAt = String.valueOf(Instant.now().minus(364, ChronoUnit.DAYS));
        OrdersDto ordersDto = mock(OrdersDto.class);

        // Mock Orders Instance
        UUID orderId = UUID.randomUUID();
        when(ordersDto.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(ordersDto.getUpdatedAt()).thenReturn(retrieveCreateAt);
        when(ordersDto.getClosedAt()).thenReturn(retrieveCreateAt);
        when(ordersDto.getOrderId()).thenReturn(String.valueOf(orderId));

        // Mock LineItem instance
        LineItemsDto lineItemsDto = mock(LineItemsDto.class);
        when(lineItemsDto.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(lineItemsDto.getQuantity()).thenReturn("20");
        when(lineItemsDto.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId));
        when(lineItemsDto.getCatalogObjectId()).thenReturn("variation123");

        when(ordersDto.getLineItems()).thenReturn(List.of(lineItemsDto));
        List<OrdersDto> orderList = List.of(ordersDto);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(orderList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        UUID masterCatalogRawDataId = UUID.randomUUID();
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId);
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(List.of(variationMapping));

        // Mock saveRawOrderData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(DAYS_IN_ONE_YEAR,
            ChronoUnit.DAYS.between(Instant.parse(capturedRequest.getCreateAt()), Instant.now()),
            1);

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(orderList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Verify saveOrderList was called
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(1, savedOrders.size());
        MasterCatalogSquareOrder savedOrder = savedOrders.getFirst();
        assertEquals(orderId, savedOrder.getId());

        // Verify saveOrderLineItemList was called
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(1, savedLineItems.size());

        // Verify order line item was saved correctly
        MasterCatalogSquareOrderLineItem savedLineItem = savedLineItems.getFirst();
        assertEquals(Integer.valueOf("20"), savedLineItem.getQuantity());
        assertEquals(savedOrder.getId(), savedLineItem.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId, savedLineItem.getMasterCatalogRawDataId());

        // Ensure getCreateAt is set
        assertTrue(savedOrder.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));

    }

    @Test
    void testSyncOrder_ExistingOrderWithOrderEarlierThanExistingOrder() {
        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-02T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));
        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId2 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-01T23:59:59Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:01Z";

        // Mock fetchOrders to return multiple Orders
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));

        LineItemsDto lineItemsDto2 = mock(LineItemsDto.class);
        when(lineItemsDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(lineItemsDto2.getQuantity()).thenReturn("10");
        when(lineItemsDto2.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId2));
        when(lineItemsDto2.getCatalogObjectId()).thenReturn("variation456");
        when(ordersDto2.getLineItems()).thenReturn(List.of(lineItemsDto2));

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping2 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping2.getVariationId()).thenReturn("variation456");
        when(variationMapping2.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId2);

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Arrays.asList("variation456"),
            storeId)).thenReturn(Arrays.asList(variationMapping2));

        // Mock saving raw order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation456"), capturedVariationIds);

        // Verify orders were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(1, savedOrders.size());

        MasterCatalogSquareOrder savedOrder2 = savedOrders.get(0);

        assertEquals(orderId2, savedOrder2.getId());
        assertEquals(retrieveCreateAtOrder2, String.valueOf(savedOrder2.getOrderClosedAt()));

        // Verify order line items were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(1, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem2 = savedLineItems.getLast();

        assertEquals(Integer.valueOf("10"), savedLineItem2.getQuantity());
        assertEquals(savedOrder2.getId(), savedLineItem2.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId2, savedLineItem2.getMasterCatalogRawDataId());

        // Ensure getCreatedAt is set
        assertTrue(savedOrder2.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncOrder_WithMultipleOrders() {
        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));
        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID masterCatalogRawDataId2 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:00Z";

        // Mock fetchOrders to return multiple Orders
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));

        LineItemsDto lineItemsDto2 = mock(LineItemsDto.class);
        when(lineItemsDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(lineItemsDto2.getQuantity()).thenReturn("10");
        when(lineItemsDto2.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId2));
        when(lineItemsDto2.getCatalogObjectId()).thenReturn("variation456");
        when(ordersDto2.getLineItems()).thenReturn(List.of(lineItemsDto2));

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto1, ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);

        MasterCatalogSquareVariationMapping variationMapping2 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping2.getVariationId()).thenReturn("variation456");
        when(variationMapping2.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId2);

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Arrays.asList("variation123", "variation456"),
            storeId)).thenReturn(Arrays.asList(variationMapping1, variationMapping2));

        // Mock saving raw order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation123", "variation456"), capturedVariationIds);

        // Verify orders were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(2, savedOrders.size());

        MasterCatalogSquareOrder savedOrder1 = savedOrders.get(0);
        MasterCatalogSquareOrder savedOrder2 = savedOrders.get(1);

        assertEquals(orderId1, savedOrder1.getId());
        assertEquals(retrieveCreateAtOrder1, String.valueOf(savedOrder1.getOrderClosedAt()));

        assertEquals(orderId2, savedOrder2.getId());
        assertEquals(retrieveCreateAtOrder2, String.valueOf(savedOrder2.getOrderClosedAt()));

        // Verify order line items were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(2, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem1 = savedLineItems.getFirst();
        MasterCatalogSquareOrderLineItem savedLineItem2 = savedLineItems.getLast();

        assertEquals(Integer.valueOf("20"), savedLineItem1.getQuantity());
        assertEquals(savedOrder1.getId(), savedLineItem1.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId1, savedLineItem1.getMasterCatalogRawDataId());

        assertEquals(Integer.valueOf("10"), savedLineItem2.getQuantity());
        assertEquals(savedOrder2.getId(), savedLineItem2.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId2, savedLineItem2.getMasterCatalogRawDataId());

        // Ensure getCreatedAt is set
        assertTrue(savedOrder1.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
        assertTrue(savedOrder2.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncOrder_WithUnmappedVariationIds() {
        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:00Z";

        // Mock fetchOrder to return multiple orders
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));

        LineItemsDto lineItemsDto2 = mock(LineItemsDto.class);
        when(lineItemsDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(lineItemsDto2.getQuantity()).thenReturn("10");
        when(lineItemsDto2.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId2));
        when(lineItemsDto2.getCatalogObjectId()).thenReturn("variationUnmapped");
        when(ordersDto2.getLineItems()).thenReturn(List.of(lineItemsDto2));

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto1, ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);

        // Mock variationMappingRepository.findAllByVariationIdIn to return only one mapping
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Arrays.asList("variation123", "variationUnmapped"),
            storeId)).thenReturn(Collections.singletonList(variationMapping1));

        // Mock saving raw order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation123", "variationUnmapped"), capturedVariationIds);

        // Verify orders were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(2, savedOrders.size());

        MasterCatalogSquareOrder savedOrder1 = savedOrders.get(0);
        MasterCatalogSquareOrder savedOrder2 = savedOrders.get(1);

        assertEquals(orderId1, savedOrder1.getId());
        assertEquals(retrieveCreateAtOrder1, String.valueOf(savedOrder1.getOrderClosedAt()));

        assertEquals(orderId2, savedOrder2.getId());
        assertEquals(retrieveCreateAtOrder2, String.valueOf(savedOrder2.getOrderClosedAt()));

        // Verify saveOrderLineItemList was called only for mapped variation
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(1, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem1 = savedLineItems.getFirst();

        assertEquals(Integer.valueOf("20"), savedLineItem1.getQuantity());
        assertEquals(savedOrder1.getId(), savedLineItem1.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId1, savedLineItem1.getMasterCatalogRawDataId());

        // Ensure updatedAt is set
        assertTrue(savedOrder1.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
        assertTrue(savedOrder2.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncOrder_WithExceptionDuringSaveRawOrderData() {
        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        UUID orderId1 = UUID.randomUUID();
        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";

        // Mock fetchOrders to return order
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        List<OrdersDto> ordersList = Collections.singletonList(ordersDto1);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class))).thenReturn(ordersList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(UUID.randomUUID());
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(Collections.singletonList(variationMapping));

        // Mock saveRawOrderData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Mock saveRawOrderData to throw an exception
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenThrow(new RuntimeException("Database error"));

        // Execute the method and expect an exception
        try {
            squareOrderSyncService.syncOrder(request, presentAtLocationIds);
        } catch (RuntimeException e) {
            // Expected exception
            assertEquals("Database error", e.getMessage());
        }

        // Verify fetchOrderCounts was called
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class));

        // Verify saveRawOrderData was called
        verify(masterCatalogSquareRawDataRepository, times(1)).save(
            any(MasterCatalogSquareRawData.class));

        // Verify that masterCatalogSquareOrderRepository was interacted as expected
        verify(masterCatalogSquareOrderRepository,
            times(1)).findTopByStoreIdOrderByOrderCreatedAtDesc(any());

        // Verify saveOrderList was never called
        verify(masterCatalogSquareOrderRepository, never()).saveAll(any(List.class));

        // Verify saveOrderLineItemList was never called
        verify(masterCatalogSquareOrderLineItemRepository, never()).saveAll(any(List.class));

    }

    @Test
    void testSyncOrder_NoOrderException() {
        // Mock retrieveLatestSyncTimeForOrderChanges to return an empty Optional
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.empty());
        // Mock fetchOrderChanges to return an empty list
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(Collections.emptyList());

        // Execute the method
        try {
            squareOrderSyncService.syncOrder(request, presentAtLocationIds);
        } catch (RuntimeException e) {
            // Expected exception
            assertEquals("Synchronized order line items, no valid line items to save.",
                e.getMessage());
        }

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertNotNull(capturedRequest.getCreateAt());

        // Verify saveRawOrderChangesData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(
            any(MasterCatalogSquareRawData.class));

        // Verify findThenBuildVariationIdToRawDataIdMap was not called
        verify(variationMappingRepository, never()).findAllByVariationIdInAndStoreIdIs(any(),
            any());

        // Verify saveOrderList was not called
        verify(masterCatalogSquareOrderRepository, never()).saveAll(any());

        // Verify saveOrderLineItemList was not called
        verify(masterCatalogSquareOrderLineItemRepository, never()).saveAll(any());

    }

    @Test
    void testSyncOrder_NoOrderLineItem() {

        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        UUID orderId1 = UUID.randomUUID();
        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";

        // Mock fetchOrder to return one order with no line item.
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        List<OrdersDto> ordersList = Arrays.asList(ordersDto1);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class))).thenReturn(ordersList);

        // Mock saveRawOrderData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrder was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertNotNull(capturedRequest.getCreateAt());

        // Verify saveRawOrderData was called
        verify(masterCatalogSquareRawDataRepository, times(1)).save(
            any(MasterCatalogSquareRawData.class));

        // Verify findThenBuildVariationIdToRawDataIdMap was not called
        verify(variationMappingRepository, never()).findAllByVariationIdInAndStoreIdIs(any(),
            any());

        // Verify saveOrderList was called
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(any());

        // Verify saveOrderLineItemList was not called
        verify(masterCatalogSquareOrderLineItemRepository, never()).saveAll(any());

    }

    @Test
    void testSyncOrder_NoLineItemWithReturn() {

        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:00Z";

        // Mock fetchOrder to return multiple Order
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));

        OrderReturn orderReturn = mock(OrderReturn.class);
        // Mock existing OrderReturn
        when(ordersDto2.getReturns()).thenReturn(List.of(orderReturn));
        // Mock no line item
        when(ordersDto2.getLineItems()).thenReturn(null);

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto1, ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Collections.singletonList("variation123"),
            storeId)).thenReturn(Arrays.asList(variationMapping1));

        // Mock saving raw Order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Verify only order with line item and no return is saved
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(1, savedOrders.size());

        MasterCatalogSquareOrder savedOrder1 = savedOrders.get(0);

        assertEquals(orderId1, savedOrder1.getId());
        assertEquals(retrieveCreateAtOrder1, String.valueOf(savedOrder1.getOrderClosedAt()));

        // Verify order line item is saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(1, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem1 = savedLineItems.getFirst();

        assertEquals(Integer.valueOf("20"), savedLineItem1.getQuantity());
        assertEquals(savedOrder1.getId(), savedLineItem1.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId1, savedLineItem1.getMasterCatalogRawDataId());

        // Ensure updatedAt is set
        assertTrue(savedOrder1.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));


    }

    @Test
    void testSyncOrder_NoLineItemWithNoReturn() {

        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID masterCatalogRawDataId2 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:00Z";

        // Mock fetchOrders to return multiple orders
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        // Mock no returns
        when(ordersDto2.getReturns()).thenReturn(null);
        // Mock no line item
        when(ordersDto2.getLineItems()).thenReturn(null);

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto1, ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Collections.singletonList("variation123"),
            storeId)).thenReturn(Arrays.asList(variationMapping1));

        // Mock saving raw Order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Verify orders were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(2, savedOrders.size());

        MasterCatalogSquareOrder savedOrder1 = savedOrders.get(0);

        assertEquals(orderId1, savedOrder1.getId());
        assertEquals(retrieveCreateAtOrder1, String.valueOf(savedOrder1.getOrderClosedAt()));

        // Verify order line items were saved
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(1, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem1 = savedLineItems.getFirst();

        assertEquals(Integer.valueOf("20"), savedLineItem1.getQuantity());
        assertEquals(savedOrder1.getId(), savedLineItem1.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId1, savedLineItem1.getMasterCatalogRawDataId());

        // Ensure updatedAt is set
        assertTrue(savedOrder1.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));

    }

    @Test
    void testSyncOrder_LineItemWithReturn() {

        // Mock findTopByOrderByCreatedAtDesc to return a fixed Instant
        MasterCatalogSquareOrder existOrder = mock(MasterCatalogSquareOrder.class);
        Instant retrieveCreateAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existOrder.getCreatedAt()).thenReturn(retrieveCreateAt);
        when(masterCatalogSquareOrderRepository.findTopByStoreIdOrderByOrderCreatedAtDesc(any()))
            .thenReturn(Optional.of(existOrder));

        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID masterCatalogRawDataId2 = UUID.randomUUID();
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();

        String retrieveCreateAtOrder1 = "2024-01-02T00:00:00Z";
        String retrieveCreateAtOrder2 = "2024-01-02T00:00:00Z";

        // Mock fetchOrders to return multiple orders
        OrdersDto ordersDto1 = mock(OrdersDto.class);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));
        when(ordersDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getUpdatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getClosedAt()).thenReturn(retrieveCreateAtOrder1);
        when(ordersDto1.getOrderId()).thenReturn(String.valueOf(orderId1));

        LineItemsDto lineItemsDto1 = mock(LineItemsDto.class);
        when(lineItemsDto1.getCreatedAt()).thenReturn(retrieveCreateAtOrder1);
        when(lineItemsDto1.getQuantity()).thenReturn("20");
        when(lineItemsDto1.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId1));
        when(lineItemsDto1.getCatalogObjectId()).thenReturn("variation123");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));

        OrdersDto ordersDto2 = mock(OrdersDto.class);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        when(ordersDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getUpdatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getClosedAt()).thenReturn(retrieveCreateAtOrder2);
        when(ordersDto2.getOrderId()).thenReturn(String.valueOf(orderId2));
        OrderReturn orderReturn = mock(OrderReturn.class);
        // Mock existing OrderReturn
        when(ordersDto2.getReturns()).thenReturn(List.of(orderReturn));

        LineItemsDto lineItemsDto2 = mock(LineItemsDto.class);
        when(lineItemsDto2.getCreatedAt()).thenReturn(retrieveCreateAtOrder2);
        when(lineItemsDto2.getQuantity()).thenReturn("10");
        when(lineItemsDto2.getMasterCatalogOrderId()).thenReturn(String.valueOf(orderId2));
        when(lineItemsDto2.getCatalogObjectId()).thenReturn("variation456");
        when(ordersDto1.getLineItems()).thenReturn(List.of(lineItemsDto1));
        // Mock line item
        when(ordersDto2.getLineItems()).thenReturn(List.of(lineItemsDto2));

        List<OrdersDto> ordersDtoList = Arrays.asList(ordersDto1, ordersDto2);
        when(squareApiAdapter.batchRetrieveOrders(eq(storeId),
            any(BatchRetrieveOrdersRequestDto.class)))
            .thenReturn(ordersDtoList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);

        MasterCatalogSquareVariationMapping variationMapping2 = mock(
            MasterCatalogSquareVariationMapping.class);
        when(variationMapping2.getVariationId()).thenReturn("variation456");
        when(variationMapping2.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId2);

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(
            Arrays.asList("variation123", "variation456"),
            storeId)).thenReturn(Arrays.asList(variationMapping1, variationMapping2));

        // Mock saving raw Order data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        squareOrderSyncService.syncOrder(request, presentAtLocationIds);

        // Verify fetchOrders was called
        ArgumentCaptor<BatchRetrieveOrdersRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveOrdersRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveOrders(eq(storeId),
            requestCaptor.capture());
        BatchRetrieveOrdersRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());

        // Verify saveRawOrderData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(
            MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(ordersDtoList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(
            variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation123", "variation456"), capturedVariationIds);

        // Verify orders were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareOrder>> orderListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderRepository, times(1)).saveAll(orderListCaptor.capture());
        List<MasterCatalogSquareOrder> savedOrders = orderListCaptor.getValue();
        assertEquals(2, savedOrders.size());

        MasterCatalogSquareOrder savedOrder1 = savedOrders.get(0);
        MasterCatalogSquareOrder savedOrder2 = savedOrders.get(1);

        assertEquals(orderId1, savedOrder1.getId());
        assertEquals(retrieveCreateAtOrder1, String.valueOf(savedOrder1.getOrderClosedAt()));

        assertEquals(orderId2, savedOrder2.getId());
        assertEquals(retrieveCreateAtOrder2, String.valueOf(savedOrder2.getOrderClosedAt()));

        // Verify saveOrderLineItemList was called only for mapped variation
        ArgumentCaptor<List<MasterCatalogSquareOrderLineItem>> lineItemListCaptor = ArgumentCaptor.forClass(
            List.class);
        verify(masterCatalogSquareOrderLineItemRepository, times(1)).saveAll(
            lineItemListCaptor.capture());
        List<MasterCatalogSquareOrderLineItem> savedLineItems = lineItemListCaptor.getValue();
        assertEquals(2, savedLineItems.size());

        MasterCatalogSquareOrderLineItem savedLineItem1 = savedLineItems.getFirst();
        MasterCatalogSquareOrderLineItem savedLineItem2 = savedLineItems.getLast();

        assertEquals(Integer.valueOf("20"), savedLineItem1.getQuantity());
        assertEquals(savedOrder1.getId(), savedLineItem1.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId1, savedLineItem1.getMasterCatalogRawDataId());

        assertEquals(Integer.valueOf("10"), savedLineItem2.getQuantity());
        assertEquals(savedOrder2.getId(), savedLineItem2.getMasterCatalogOrderId());
        assertEquals(masterCatalogRawDataId2, savedLineItem2.getMasterCatalogRawDataId());

        // Ensure getCreatedAt is set
        assertTrue(savedOrder1.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
        assertTrue(savedOrder2.getOrderCreatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));

    }


}
