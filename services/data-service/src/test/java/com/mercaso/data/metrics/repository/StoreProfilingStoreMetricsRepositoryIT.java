package com.mercaso.data.metrics.repository;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.repository.specification.StoreProfilingStoreMetricsSpecification;
import org.apache.commons.lang3.tuple.Pair;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

@Transactional
class StoreProfilingStoreMetricsRepositoryIT extends AbstractIT {

    @Autowired
    private StoreProfilingStoreMetricsRepository repository;

    private StoreProfilingStoreMetricsEntity createEntity(String storeId, String metricName, String metricValue) {
        StoreProfilingStoreMetricsEntity entity = new StoreProfilingStoreMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricName(metricName);
        entity.setMetricValue(metricValue);
        return entity;
    }

    @Test
    void findDistinctStoreIds_whenMatchAny_singleTag_returnsMatchingStoreIds() {
        repository.save(createEntity("s1_any_single", "category", "electronics"));
        repository.save(createEntity("s1_any_single", "brand", "alpha"));
        repository.save(createEntity("s2_any_single", "category", "books"));
        repository.save(createEntity("s3_any_single", "category", "electronics"));
        repository.save(createEntity("s3_any_single", "brand", "beta"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("category", "electronics"));
        
        String matchType = "any";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(2); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("s1_any_single", "s3_any_single");
    }

    @Test
    void findDistinctStoreIds_whenMatchAny_multipleTags_returnsMatchingStoreIds() {
        repository.save(createEntity("s1_any_multi", "category", "electronics"));
        repository.save(createEntity("s1_any_multi", "brand", "samsung"));
        repository.save(createEntity("s2_any_multi", "category", "electronics"));
        repository.save(createEntity("s3_any_multi", "brand", "samsung"));
        repository.save(createEntity("s4_any_multi", "category", "books"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("category", "electronics"));
        tagsList.add(Pair.of("brand", "samsung"));
        
        String matchType = "any"; 
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(3); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("s1_any_multi", "s2_any_multi", "s3_any_multi");
    }
    
    @Test
    void findDistinctStoreIds_whenMatchAll_singleTag_returnsMatchingStoreIds() {
        repository.save(createEntity("s1_all_single", "stock", "available"));
        repository.save(createEntity("s1_all_single", "color", "blue"));
        repository.save(createEntity("s2_all_single", "stock", "none"));
        repository.save(createEntity("s3_all_single", "stock", "available"));
        repository.save(createEntity("s3_all_single", "color", "red"));
        repository.flush();
        
        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("stock", "available"));
        
        String matchType = "all"; 
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(2); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("s1_all_single", "s3_all_single");
    }

    @Test
    void findDistinctStoreIds_whenMatchAll_multipleTags_returnsMatchingStoreIds() {
        repository.save(createEntity("s1_all_multi", "category", "tools"));
        repository.save(createEntity("s1_all_multi", "brand", "delta"));
        repository.save(createEntity("s2_all_multi", "category", "tools"));
        repository.save(createEntity("s3_all_multi", "category", "tools"));
        repository.save(createEntity("s3_all_multi", "brand", "delta"));
        repository.save(createEntity("s3_all_multi", "feature", "new"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("category", "tools"));
        tagsList.add(Pair.of("brand", "delta"));
        
        String matchType = "all";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(2); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("s1_all_multi", "s3_all_multi");
    }

    @Test
    void findDistinctStoreIds_whenMatchAll_multipleIdenticalTags_returnsMatchingStoreIds() {
        repository.save(createEntity("s_multi_rec", "feature", "A"));
        repository.save(createEntity("s_multi_rec", "feature", "A"));
        repository.save(createEntity("s_multi_rec", "color", "blue"));
        repository.save(createEntity("s_other", "feature", "A"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("feature", "A"));
        tagsList.add(Pair.of("feature", "A"));
        tagsList.add(Pair.of("color", "blue"));
        
        String matchType = "all";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("s_multi_rec");
    }

    @Test
    void findDistinctStoreIds_whenMatchAll_nonExistingTagCombination_returnsEmpty() {
        repository.save(createEntity("s1_all_nonexist", "category", "books"));
        repository.save(createEntity("s1_all_nonexist", "brand", "alpha_press"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("category", "books"));
        tagsList.add(Pair.of("brand", "beta_press"));
        
        String matchType = "all";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(0);
        assertThat(result.getContent()).isEmpty();
    }

    @Test
    void findDistinctStoreIds_whenMatchTypeNull_returnsAllDistinctStoreIds() {
        repository.save(createEntity("s_null_1", "category", "electronics"));
        repository.save(createEntity("s_null_2", "category", "books"));
        repository.save(createEntity("s_null_3", "feature", "old"));
        repository.flush();
        
        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("category", "electronics"));
        
        String matchType = null; 
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        List<String> expectedStoreIds = Arrays.asList("s_null_1", "s_null_2", "s_null_3");
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(expectedStoreIds.size());
        assertThat(result.getContent()).containsExactlyInAnyOrderElementsOf(expectedStoreIds);
    }

    @Test
    void findDistinctStoreIds_whenTagsListEmpty_returnsAllDistinctStoreIds() {
        repository.save(createEntity("s_empty_1", "category", "furniture"));
        repository.save(createEntity("s_empty_2", "brand", "ikea"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        
        String matchType = "any";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);
        
        List<String> expectedStoreIds = Arrays.asList("s_empty_1", "s_empty_2");
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(expectedStoreIds.size());
        assertThat(result.getContent()).containsExactlyInAnyOrderElementsOf(expectedStoreIds);
    }
    
    @Test
    void findDistinctStoreIds_withPagination_returnsCorrectPage() {
        repository.save(createEntity("pg_store_aa", "type", "widget"));
        repository.save(createEntity("pg_store_bb", "type", "widget"));
        repository.save(createEntity("pg_store_cc", "type", "widget"));
        repository.save(createEntity("pg_store_dd", "type", "gadget"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("type", "widget"));
        
        String matchType = "any";
        Pageable firstPage = PageRequest.of(0, 2); 

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> resultFirstPage = repository.findDistinctStoreIds(spec, firstPage);

        assertThat(resultFirstPage).isNotNull();
        assertThat(resultFirstPage.getTotalElements()).isEqualTo(3); 
        assertThat(resultFirstPage.getContent()).hasSize(2);
        assertThat(resultFirstPage.getContent()).containsExactly("pg_store_aa", "pg_store_bb");


        Pageable secondPage = PageRequest.of(1, 2); 
        Page<String> resultSecondPage = repository.findDistinctStoreIds(spec, secondPage);
        assertThat(resultSecondPage).isNotNull();
        assertThat(resultSecondPage.getTotalElements()).isEqualTo(3);
        assertThat(resultSecondPage.getContent()).hasSize(1); 
        assertThat(resultSecondPage.getContent()).containsExactly("pg_store_cc");
    }

    @Test
    void findDistinctStoreIds_whenMatchAny_multipleIdenticalTags_returnsCorrectly() {

        repository.save(createEntity("store_dup_any1", "feature", "A"));
        repository.save(createEntity("store_dup_any1", "color", "blue"));

        repository.save(createEntity("store_dup_any2", "feature", "A"));
        repository.save(createEntity("store_dup_any2", "feature", "B"));

        repository.save(createEntity("store_dup_any3", "color", "blue"));

        repository.save(createEntity("store_dup_any4", "feature", "B"));
        repository.flush();

        List<Pair<String, String>> tagsList = new ArrayList<>();
        tagsList.add(Pair.of("feature", "A"));
        tagsList.add(Pair.of("feature", "A"));
        tagsList.add(Pair.of("color", "blue"));
        
        String matchType = "any";
        Pageable pageable = PageRequest.of(0, 10);

        Specification<StoreProfilingStoreMetricsEntity> spec = StoreProfilingStoreMetricsSpecification.matchTags(tagsList, matchType);
        Page<String> result = repository.findDistinctStoreIds(spec, pageable);

        assertThat(result).isNotNull();
        // Expecting store_dup_any1, store_dup_any2, store_dup_any3
        assertThat(result.getTotalElements()).isEqualTo(3); 
        assertThat(result.getContent()).containsExactlyInAnyOrder("store_dup_any1", "store_dup_any2", "store_dup_any3");
    }
}
