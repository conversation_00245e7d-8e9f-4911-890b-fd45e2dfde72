package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class DashboardResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_INVENTORY_REPLENISHMENT_TREND = "/master-catalog/v1/dashboard/inventory-replenishment-trend";
    public static final String V1_ORDER_TREND = "/master-catalog/v1/dashboard/order-trend";

    public DashboardResourceApiUtils(Environment environment) {
        super(environment);
    }

    public List<InventoryAndReplenishmentTrendDto> getInventoryReplenishmentTrend(String storeId, String upc) throws Exception {
        return getEntityList(V1_INVENTORY_REPLENISHMENT_TREND + "?storeId=" + storeId + "&upc=" + upc,
            null,
            InventoryAndReplenishmentTrendDto.class);
    }

    public List<OrderTrendDto> getOrderTrend(String storeId, String upc) throws Exception {
        return getEntityList(V1_ORDER_TREND + "?storeId=" + storeId + "&upc=" + upc, null, OrderTrendDto.class);
    }
}
