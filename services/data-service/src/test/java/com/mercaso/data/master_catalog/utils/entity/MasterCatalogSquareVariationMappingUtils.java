package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogSquareVariationMappingUtils {

    public static MasterCatalogSquareVariationMapping buildMasterCatalogSquareVariationMapping(
        UUID masterCatalogRawDataId,
        String variationId
    ) {
        return MasterCatalogSquareVariationMapping.builder()
            .id(UUID.randomUUID())
            .masterCatalogRawDataId(masterCatalogRawDataId)
            .variationId(variationId)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }
} 