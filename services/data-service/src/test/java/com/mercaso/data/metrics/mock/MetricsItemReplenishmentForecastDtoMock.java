package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.dto.MetricsItemReplenishmentForecastDto;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MetricsItemReplenishmentForecastDtoMock {

    public static MetricsItemReplenishmentForecastDto metricsItemReplenishmentForecastDtoMock() {
        return MetricsItemReplenishmentForecastDto.builder()
            .addressId(UUID.randomUUID().toString())
            .sku("399898989")
            .name("Test Product")
            .nextOrderTime(Instant.now().plus(6, ChronoUnit.DAYS))
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();

    }
}
