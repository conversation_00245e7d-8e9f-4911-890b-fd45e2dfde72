create table if not exists master_catalog_locations
(
    id             uuid primary key,
    store_id       uuid not null,
    name           varchar(255),
    address_line_1 varchar(255),
    postal_code    varchar(255),
    city           varchar(255),
    country        varchar(255),
    latitude       numeric,
    longitude      numeric,
    metadata       jsonb,
    created_at     timestamptz default now(),
    updated_at     timestamptz default now()
);

comment on table master_catalog_locations is 'Table to store master_catalog_locations';
comment on column master_catalog_locations.store_id is 'Store id of the location';
comment on column master_catalog_locations.name is 'Name of the location';
comment on column master_catalog_locations.address_line_1 is 'Address line 1 of the location';
comment on column master_catalog_locations.postal_code is 'Postal code of the location';
comment on column master_catalog_locations.city is 'City of the location';
comment on column master_catalog_locations.country is 'Country of the location';
comment on column master_catalog_locations.latitude is 'Latitude of the location';
comment on column master_catalog_locations.longitude is 'Longitude of the location';
comment on column master_catalog_locations.metadata is 'Metadata of the location, some personalized data';
