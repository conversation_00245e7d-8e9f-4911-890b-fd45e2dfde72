create table if not exists item_recommendation
(
    id uuid primary key default uuid_generate_v4(),
    store_id varchar(255),
    sku_number varchar(255),
    sku_name varchar(255),
    product_id varchar(255),
    department varchar(255),
    reason varchar(255),
    reason_value varchar(255),
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now()
);

create index if not exists item_recommendation_store_id_idx on item_recommendation (store_id);

comment on table item_recommendation is 'Item Recommendation';
comment on column item_recommendation.store_id is 'The store id that the recommended item belongs to';
comment on column item_recommendation.sku_number is 'The sku number of the recommended item';
comment on column item_recommendation.sku_name is 'The sku name of the recommended item';
comment on column item_recommendation.product_id is 'The product id of the recommended item';
comment on column item_recommendation.department is 'The department of the recommended item';
comment on column item_recommendation.reason is 'The reason of the recommended item, e.g. "PURCHASE_RATE"';
comment on column item_recommendation.reason_value is 'The value of the reason of the recommended item, e.g. "10"';;