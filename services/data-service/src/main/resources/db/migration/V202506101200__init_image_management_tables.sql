create table if not exists image_management_image
(
    id          uuid primary key,
    image_batch_id varchar(255) not null,
    file_name   varchar(255) not null,
    file_path   varchar(512) not null,
    file_size   bigint not null,
    mime_type   varchar(128) not null,
    width       integer,
    height      integer,
    shot_at     timestamptz not null,
    created_by  varchar(255) not null,
    created_at  timestamptz not null default now(),
    updated_at  timestamptz not null default now()
);

comment on table image_management_image is 'Table to store universal image attributes';
comment on column image_management_image.image_batch_id is 'ID of the image batch that the image belongs to';
comment on column image_management_image.file_name is 'Original name of the image file';
comment on column image_management_image.file_path is 'Storage path of the image file';
comment on column image_management_image.file_size is 'Size of the image file in bytes';
comment on column image_management_image.mime_type is 'MIME type of the image file (image/jpeg, image/png)';
comment on column image_management_image.width is 'Width of the image in pixels';
comment on column image_management_image.height is 'Height of the image in pixels';
comment on column image_management_image.shot_at is 'Date and time the image was shot, get from image folder';
comment on column image_management_image.created_by is 'Resource of the image (<PERSON>, <PERSON>, etc.)';

create table if not exists image_management_item_image
(
    id          uuid primary key,
    image_id    uuid not null unique,
    image_angel varchar(255) not null,
    sku         varchar(255),
    upc         varchar(255),
    is_primary  boolean not null default false,
    image_type  varchar(255) not null,
    created_at  timestamptz not null default now(),
    updated_at  timestamptz not null default now()
);

comment on table image_management_item_image is 'Table to store item-specific image metadata';
comment on column image_management_item_image.image_id is 'Reference to the base image record';
comment on column image_management_item_image.image_angel is 'Position/angle of the image in the item, read from image file name, Top_1, Front_3, etc.';
comment on column image_management_item_image.sku is 'SKU number of the item';
comment on column image_management_item_image.upc is 'UPC code of the item';
comment on column image_management_item_image.is_primary is 'Indicating if this is the primary image for the item';
comment on column image_management_item_image.image_type is 'Image type can be RAW, retouched(being edited by Rocky), or edited(being edited by Juan),etc';

create index if not exists idx_image_management_item_image_image_id on image_management_item_image (image_id);
create index if not exists idx_image_management_item_image_sku on image_management_item_image (sku);
create index if not exists idx_image_management_item_image_upc on image_management_item_image (upc); 