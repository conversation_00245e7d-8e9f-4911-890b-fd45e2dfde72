package com.mercaso.data.third_party.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.dto.CustomSort;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class CustomPageableDeserializer extends JsonDeserializer<CustomPageable> {

    @Override
    public CustomPageable deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        int pageSize = node.get("pageSize").asInt();
        int pageNumber = node.get("pageNumber").asInt();

        List<CustomSort> sortList = new ArrayList<>();
        JsonNode sortNode = node.get("sort");
        if (sortNode != null && sortNode.isArray()) {
            for (JsonNode sortItem : sortNode) {
                String property = sortItem.get("property").asText();
                String direction = sortItem.get("direction").asText();
                sortList.add(new CustomSort(property, direction));
            }
        }

        return new CustomPageable(pageSize, pageNumber);
    }
}
