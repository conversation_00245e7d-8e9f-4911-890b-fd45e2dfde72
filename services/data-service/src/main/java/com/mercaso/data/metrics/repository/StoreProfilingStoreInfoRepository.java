package com.mercaso.data.metrics.repository;

import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface StoreProfilingStoreInfoRepository extends JpaRepository<StoreProfilingStoreInfoEntity, String>,
    JpaSpecificationExecutor<StoreProfilingStoreInfoEntity> {

    @Query(value = """
        SELECT DISTINCT ON (ai.store_id) ai.*
        FROM public.store_profiling_store_info ai
        WHERE ai.address_name_search @@ to_tsquery('english', :query)
        ORDER BY ai.store_id
        """,
        countQuery = """
            SELECT COUNT(DISTINCT ai.store_id)
            FROM public.store_profiling_store_info ai
            WHERE ai.address_name_search @@ to_tsquery('english', :query)
            """,
        nativeQuery = true)
    Page<StoreProfilingStoreInfoEntity> findBySearchQuery(@Param("query") String searchQuery, Pageable pageable);

    @Query(value = """
        SELECT ai_outer.*
        FROM (
            SELECT DISTINCT ON (ai.store_id) ai.*, 1 AS sort_order
            FROM public.store_profiling_store_info ai
            ORDER BY ai.store_id
        ) ai_outer
        ORDER BY ai_outer.sort_order
        """,
        countQuery = """
            SELECT COUNT(DISTINCT ai.store_id)
            FROM public.store_profiling_store_info ai
            """,
        nativeQuery = true)
    Page<StoreProfilingStoreInfoEntity> queryAll(Pageable pageable);
}
