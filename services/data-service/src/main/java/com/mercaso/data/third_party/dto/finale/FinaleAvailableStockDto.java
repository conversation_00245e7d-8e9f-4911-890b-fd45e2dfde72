package com.mercaso.data.third_party.dto.finale;

import java.sql.Timestamp;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
public class FinaleAvailableStockDto {

    private Long id;

    private String name;

    private Integer mfcQoh;

    private Integer shopifyQoh;

    private String sku;

    private String productUrl;

    private Integer reservationsQoh;

    private String stockSublocations;

    private Timestamp recordLastUpdated;

    private List<FinaleAvailableStockItemsOnHandDto> stockItemsOnHand;

}
