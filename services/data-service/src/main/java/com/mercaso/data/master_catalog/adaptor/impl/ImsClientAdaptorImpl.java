package com.mercaso.data.master_catalog.adaptor.impl;

import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.ims.client.dto.ItemDto;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@AllArgsConstructor
public class ImsClientAdaptorImpl implements ImsClientAdaptor {

  private final QueryItemRestApiApi queryItemRestApiApi;

  @Override
  public Optional<ItemDto> searchItemDetailBySku(String sku) {
    try {
      ItemDto itemDetail = queryItemRestApiApi.getItemDetail(sku);
      if (itemDetail != null) {
        return Optional.of(itemDetail);
      }
    } catch (Exception e) {
      log.error("Error searching inventory stock by SKU: {} from IMS", sku, e);
    }
    return Optional.empty();
  }
}
