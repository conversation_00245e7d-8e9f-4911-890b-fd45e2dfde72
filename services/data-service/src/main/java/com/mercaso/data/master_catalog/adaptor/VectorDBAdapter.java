package com.mercaso.data.master_catalog.adaptor;

import com.google.protobuf.Struct;
import java.util.List;
import java.util.UUID;

public interface VectorDBAdapter {

    //The List<List<UUID>> is supposed to be the list of tuple of UUID that are duplicated. For example [[UUID1,UUID2],[UUID3,UUID2]]
    List<List<UUID>> getDuplicationPredictionForInBatch(List<String> idList,
        List<String> nameList, List<Boolean> packTypeList, List<Struct> metaDataList,
        String indexName,
        Float threshold, int batchSize);

    List<List<UUID>> getDuplicationPredictionForProduct(List<String> idList,
        List<String> nameList, List<Boolean> packTypeList, String indexName,
        Float threshold, int batchSize);

    Integer upsertToProductTable(List<String> idList, List<String> nameList,
        List<Struct> metaDataList,
        String indexName, int batchSize);

}
