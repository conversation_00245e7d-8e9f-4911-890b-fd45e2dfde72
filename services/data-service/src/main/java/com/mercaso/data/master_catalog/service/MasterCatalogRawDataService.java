package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import java.util.Collection;
import java.util.UUID;

import com.mercaso.data.recommendation.dto.PageableResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface MasterCatalogRawDataService {

    Page<MasterCatalogRawDataDto> searchMasterCatalogRawData(SearchMasterCatalogRequest searchRequest);

    Page<MasterCatalogRawDataDto> searchMasterCatalogRawDataByImage(MultipartFile file, Pageable pageable);

    void markAsCompleted(Collection<UUID> ids);

  MasterCatalogRawDataDto findById(UUID id);

    Page<MasterCatalogRawDataDto> searchDuplicationRawData(Integer pageNumber,Integer pageSize,String upc);
}
