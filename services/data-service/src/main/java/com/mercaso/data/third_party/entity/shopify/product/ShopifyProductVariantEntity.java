package com.mercaso.data.third_party.entity.shopify.product;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyProductVariantEntity {

    private String barcode;
    @JsonProperty("compare_at_price")
    private Double compareAtPrice;
    @JsonProperty("created_at")
    private ZonedDateTime createdAt;
    @JsonProperty("fulfillment_service")
    private String fulfillmentService;
    private int grams;
    private double weight;
    @JsonProperty("weight_unit")
    private String weightUnit;
    private Long id;
    @JsonProperty("inventory_item_id")
    private Long inventoryItemId;
    @JsonProperty("inventory_management")
    private String inventoryManagement;
    @JsonProperty("inventory_policy")
    private String inventoryPolicy;
    @JsonProperty("inventory_quantity")
    private int inventoryQuantity;
    private String option1;
    private String option2;
    private String option3;
    private int position;
    private double price;
    @JsonProperty("product_id")
    private Long productId;
    @JsonProperty("requires_shipping")
    private boolean requiresShipping;
    private String sku;
    private boolean taxable;
    private String title;
    @JsonProperty("updated_at")
    private ZonedDateTime updatedAt;
    @JsonProperty("image_id")
    private String imageId;


}
