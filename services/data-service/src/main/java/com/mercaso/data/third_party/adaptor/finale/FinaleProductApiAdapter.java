package com.mercaso.data.third_party.adaptor.finale;

import com.mercaso.data.third_party.dto.finale.FinaleFacilityPrimaryDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleProductDataResponseDto;
import java.util.Optional;

public interface FinaleProductApiAdapter {

    Optional<FinaleProductDataResponseDto> fetchProductData(String facilityUrl);

    Optional<FinaleFacilityDataResponseDto> fetchFacilityData();

    Optional<FinaleFacilityPrimaryDataResponseDto> fetchFacilityUrlData();
}
