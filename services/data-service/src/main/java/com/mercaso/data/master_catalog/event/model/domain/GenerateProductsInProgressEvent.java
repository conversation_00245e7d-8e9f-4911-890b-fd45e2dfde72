package com.mercaso.data.master_catalog.event.model.domain;

import com.mercaso.data.master_catalog.event.annotation.EventPublishConfig;
import com.mercaso.data.master_catalog.event.model.BaseApplicationEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsInProgressPayload;

@EventPublishConfig(
    publishLocal = true
)
public class GenerateProductsInProgressEvent extends
    BaseApplicationEvent<GenerateProductsInProgressPayload> {

    public GenerateProductsInProgressEvent(Object source,
        GenerateProductsInProgressPayload payload) {
        super(source, payload);
    }


}
