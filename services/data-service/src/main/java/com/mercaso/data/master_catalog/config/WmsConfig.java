package com.mercaso.data.master_catalog.config;

import com.mercaso.wms.client.ApiClient;
import com.mercaso.wms.client.api.SearchInventoryStockResourceApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import static java.util.Collections.singletonList;

@Configuration
public class WmsConfig {

    @Value("${mercaso.wms-url}")
    private String wmsHost;

    @Bean
    public RestTemplate wmsRestTemplate() {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectionRequestTimeout(5000);
        requestFactory.setConnectTimeout(5000);

        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.setInterceptors(singletonList(new AuthorizationClientInterceptor()));

        return restTemplate;
    }

    @Bean
    public SearchInventoryStockResourceApi queryWmsControllerApi(RestTemplate wmsRestTemplate) {
        ApiClient apiClient = new ApiClient(wmsRestTemplate);
        apiClient.setBasePath(wmsHost);
        return new SearchInventoryStockResourceApi(apiClient);
    }

}
