package com.mercaso.data.metrics.mapper;

import com.mercaso.data.metrics.dto.UploadDocumentRequestDto;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface MetricsUploadDocumentRequestDtoMapper {

    UploadDocumentRequest toDto(UploadDocumentRequestDto dto);

    UploadDocumentRequestDto fromDto(UploadDocumentRequest dto);
}