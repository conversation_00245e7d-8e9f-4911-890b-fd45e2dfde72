package com.mercaso.data.recommendation.controller;

import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.recommendation.service.impl.ReplenishmentRecommendationService;
import jakarta.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.TreeSet;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/recommendation/v1/replenishment-recommendations")
@RequiredArgsConstructor
public class ReplenishmentRecommendationResource {

  private final Environment environment;

  private final ReplenishmentRecommendationService replenishmentRecommendationService;

  private final List<ReplenishmentRecommendationDto> csvRecommendations = new ArrayList<>();

  @PostConstruct
  public void init() {
    loadCsvData();
  }

  @GetMapping("/departments")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public List<DepartmentDto> getDepartments(@RequestParam("storeId") String storeId) {
    log.info("Get departments for store: {}", storeId);

    if (shouldUseCsvData(storeId)) {
      log.info("Using CSV data for departments (dev profile + test store ID)");
      return csvRecommendations.stream()
          .collect(
              Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                  Comparator.comparing(ReplenishmentRecommendationDto::getDepartmentId)
                      .thenComparing(ReplenishmentRecommendationDto::getDepartmentName))), ArrayList::new)).stream()
          .map(dto -> new DepartmentDto(String.valueOf(dto.getDepartmentId()), dto.getDepartmentName()))
          .distinct()
          .collect(Collectors.toList());
    }

    return replenishmentRecommendationService.searchDepartment(UUID.fromString(storeId));
  }

  @GetMapping("/search")
  @PreAuthorize("hasAnyAuthority('recommendation:read:recommendations')")
  public PageableResponse<ReplenishmentRecommendationDto> searchReplenishmentRecommendations(
      @RequestParam(defaultValue = "0") Integer pageNumber,
      @RequestParam(defaultValue = "20") Integer pageSize,
      @RequestParam String storeId,
      @RequestParam(required = false) String departmentId) {

    log.info(
        "Search replenishment recommendations for store: {}, departmentId: {}, page: {}, size: {}",
        storeId, departmentId, pageNumber, pageSize);

    if (shouldUseCsvData(storeId)) {
      log.info("Using CSV data for search (dev profile + test store ID)");
      return searchCsvData(storeId, departmentId, pageNumber, pageSize);
    }
    Pageable pageable = PageRequest.of(pageNumber, pageSize);
    return replenishmentRecommendationService.searchReplenishmentRecommendation(UUID.fromString(storeId), departmentId,
        pageable);
  }

  private PageableResponse<ReplenishmentRecommendationDto> searchCsvData(
      String storeId, String departmentId, Integer pageNumber,
      Integer pageSize) {

    List<ReplenishmentRecommendationDto> filteredData = csvRecommendations.stream()
        .filter(dto -> storeId.equals(String.valueOf(dto.getStoreId())))
        .filter(dto -> StringUtils.isBlank(departmentId)
            || departmentId.equals(String.valueOf(dto.getDepartmentId())))
        .collect(Collectors.toList());

    int start = pageNumber * pageSize;
    int end = Math.min(start + pageSize, filteredData.size());

    List<ReplenishmentRecommendationDto> pagedData = filteredData.subList(
        Math.min(start, filteredData.size()),
        Math.min(end, filteredData.size())
    );

    return PageableResponse.<ReplenishmentRecommendationDto>builder()
        .data(pagedData)
        .pageNumber(pageNumber)
        .pageSize(pageSize)
        .totalPages((int) Math.ceil((double) filteredData.size() / pageSize))
        .totalElements(filteredData.size())
        .build();
  }

  private void loadCsvData() {
    try {
      BufferedReader reader = new BufferedReader(
          new InputStreamReader(
              new ClassPathResource("mock_replenishment_recommendation.csv").getInputStream()));

      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS XXX")
          .withZone(ZoneId.systemDefault());

      // Skip header
      String line = reader.readLine();

      while ((line = reader.readLine()) != null) {
        String[] data = line.split(",(?=([^\"]*\"[^\"]*\")*[^\"]*$)");
        if (data.length < 18) {
          log.warn("Invalid CSV line: {}", line);
          continue;
        }

        try {
          ReplenishmentRecommendationDto dto = ReplenishmentRecommendationDto.builder()
              .storeId(UUID.fromString(data[1]))
              .sku(cleanField(data[2]))
              .name(cleanField(data[3]))
              .recommendedQuantity(new BigDecimal(data[4]))
              .nextOrderTime(LocalDateTime.parse(data[5], formatter).atZone(ZoneId.systemDefault()).toInstant())
              .batchNumber(data[7])
              .upc(data[10])
              .productId(cleanField(data[11]))
              .lastPurchaseTime(LocalDateTime.parse(data[12], formatter).atZone(ZoneId.systemDefault()).toInstant())
              .lastPurchaseQuantity(Integer.parseInt(cleanField(data[15])))
              .departmentId(UUID.fromString(cleanField(data[16])))
              .departmentName(cleanField(data[17]))
              .build();
          csvRecommendations.add(dto);
        } catch (Exception e) {
          log.error("Error parsing line: {}", line, e);
        }
      }

      log.info("Loaded {} recommendations from CSV", csvRecommendations.size());
    } catch (IOException e) {
      log.error("Failed to load CSV data", e);
    }
  }

  private String cleanField(String field) {
    if (field == null) {
      return null;
    }
    return field.trim().replaceAll("^\"|\"$", "");
  }

  private boolean isDevProfile() {
    return Arrays.asList(environment.getActiveProfiles()).contains("dev");
  }

  private boolean shouldUseCsvData(String storeId) {
    return isDevProfile() && storeId.equals("78d259cf-1399-4e9c-9960-31c7667cd975");
  }
}
