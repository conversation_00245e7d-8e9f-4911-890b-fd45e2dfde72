package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Data;

@Entity
@Data
@Table(name = "metrics_order_frequency")
public class MetricsOrderFrequencyEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "address_id", nullable = false)
    private String addressId;

    @Column(name = "date_type", nullable = false)
    private String dateType;

    @Column(nullable = false)
    private LocalDateTime date;

    @Column(nullable = false)
    private Integer frequency;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

}
