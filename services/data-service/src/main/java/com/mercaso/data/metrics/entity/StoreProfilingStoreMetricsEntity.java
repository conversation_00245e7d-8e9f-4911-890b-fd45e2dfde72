package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@RequiredArgsConstructor
@Table(name = "store_profiling_store_metrics", schema = "public")
public class StoreProfilingStoreMetricsEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "store_id", nullable = false)
    private String storeId;

    @Column(name = "metric_category", nullable = false)
    private String metricCategory;

    @Column(name = "metric_name", nullable = false)
    private String metricName;

    @Column(name = "metric_desc", nullable = false)
    private String metricDesc;

    @Column(name = "metric_value", nullable = false)
    private String metricValue;
}
