package com.mercaso.data.master_catalog.config;

import static java.util.Collections.singletonList;

import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.ims.client.ApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class ImsConfig {

  @Value("${mercaso.ims-url}")
  private String imsHost;

  @Bean
  public RestTemplate imsRestTemplate() {
    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    requestFactory.setConnectionRequestTimeout(5000);
    requestFactory.setConnectTimeout(5000);

    RestTemplate restTemplate = new RestTemplate(requestFactory);
    restTemplate.setInterceptors(singletonList(new AuthorizationClientInterceptor()));

    return restTemplate;
  }

  @Bean
  public QueryItemRestApiApi queryImsControllerApi(RestTemplate imsRestTemplate) {
    ApiClient apiClient = new ApiClient(imsRestTemplate);
    apiClient.setBasePath(imsHost);
    return new QueryItemRestApiApi(apiClient);
  }

}
