package com.mercaso.data.master_catalog.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "master_catalog_square_order_line_item")
public class MasterCatalogSquareOrderLineItem extends BaseEntity {

    @Column(name = "master_catalog_order_id")
    private UUID masterCatalogOrderId;

    @Column(name = "master_catalog_raw_data_id")
    private UUID masterCatalogRawDataId;

    @Column(name = "quantity")
    private Integer quantity;

    @Size(max = 255)
    @Column(name = "source_id")
    private String sourceId;

}