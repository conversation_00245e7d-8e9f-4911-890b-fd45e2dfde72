package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.alibaba.excel.util.BooleanUtils;
import com.google.common.collect.Lists;
import com.mercaso.data.master_catalog.constants.CommonConstants;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductGenerationTask;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.mapper.MasterCatalogProductGenerationTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductGenerationTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractSubmitDuplicationEventListener {

    protected final MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository;
    protected final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    protected final MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService;
    protected final MasterCatalogProductGenerationTaskRepository masterCatalogProductGenerationTaskRepository;
    protected final MasterCatalogProductGenerationTaskMapper masterCatalogProductGenerationTaskMapper;
    protected final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    protected final MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    protected AbstractSubmitDuplicationEventListener(
            MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository,
            MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository,
            MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService,
            MasterCatalogProductGenerationTaskRepository masterCatalogProductGenerationTaskRepository,
            MasterCatalogProductGenerationTaskMapper masterCatalogProductGenerationTaskMapper,
            ApplicationEventPublisherProvider applicationEventPublisherProvider,
            MasterCatalogRawDataRepository masterCatalogRawDataRepository) {
        this.masterCatalogPotentiallyDuplicateRawDataRepository = masterCatalogPotentiallyDuplicateRawDataRepository;
        this.masterCatalogRawDataDuplicationRepository = masterCatalogRawDataDuplicationRepository;
        this.masterCatalogRawDataDuplicationService = masterCatalogRawDataDuplicationService;
        this.masterCatalogProductGenerationTaskRepository = masterCatalogProductGenerationTaskRepository;
        this.masterCatalogProductGenerationTaskMapper = masterCatalogProductGenerationTaskMapper;
        this.applicationEventPublisherProvider = applicationEventPublisherProvider;
        this.masterCatalogRawDataRepository = masterCatalogRawDataRepository;
    }

    protected record PotentiallyDuplicationContext(UUID taskId, MasterCatalogProductGenerationTask task,
            List<MasterCatalogPotentiallyDuplicateRawData> potentiallyDuplicateData) {

    }

    protected List<MasterCatalogPotentiallyDuplicateRawData> getMarkedDuplicates(
            List<MasterCatalogPotentiallyDuplicateRawData> allData) {
        return allData.stream().filter(data -> BooleanUtils.isTrue(data.getDuplicated())).toList();
    }

    protected List<MasterCatalogRawDataDuplication> createDuplicationRecords(
            List<MasterCatalogPotentiallyDuplicateRawData> markedData) {
        List<MasterCatalogRawDataDuplication> duplicationRecords = new ArrayList<>();

        for (MasterCatalogPotentiallyDuplicateRawData data : markedData) {
            List<MasterCatalogRawDataDuplication> existingDuplications = getDuplicationGroupsBy(
                    Lists.newArrayList(data.getUpc(), data.getPotentiallyDuplicateUpc()),
                    Lists.newArrayList(data.getName(), data.getPotentiallyDuplicateName()));

            UUID duplicationGroup = getOrCreateDuplicationGroup(existingDuplications);

            // Skip if the duplication group already has more than 2 records
            if (shouldSkipDuplicationCreation(existingDuplications, data)) {
                log.info(
                        "Skipping duplication creation for data {} because the upc {} and potentiallyDuplicateUpc {} already in the same duplication group",
                        data.getId(), data.getUpc(), data.getPotentiallyDuplicateUpc());
                continue;
            }

            duplicationRecords.addAll(buildDuplicationRecords(data, duplicationGroup));
        }

        return duplicationRecords;
    }

    /**
     * Gets an existing duplication group or creates a new one
     */
    private UUID getOrCreateDuplicationGroup(List<MasterCatalogRawDataDuplication> existingDuplications) {
        if (existingDuplications.isEmpty()) {
            return UUID.randomUUID();
        }
        return masterCatalogRawDataDuplicationService.mergeDuplicationRecords(existingDuplications);
    }

    /**
     * Checks if we should skip creating new duplication records based on existing
     * records
     */
    private boolean shouldSkipDuplicationCreation(List<MasterCatalogRawDataDuplication> existingDuplications,
            MasterCatalogPotentiallyDuplicateRawData data) {
        // Get all duplication groups that contain either UPC
        Map<UUID, Long> duplicationGroupCount = Stream.concat(
                findDuplicationsByUpc(existingDuplications, data.getUpc()).stream(),
                findDuplicationsByUpc(existingDuplications, data.getPotentiallyDuplicateUpc()).stream())
                .map(MasterCatalogRawDataDuplication::getDuplicationGroup)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        // Skip if any group has more than 1 records
        return duplicationGroupCount.values().stream().anyMatch(count -> count > 1);
    }

    /**
     * Finds all duplications that reference a specific UPC
     */
    private List<MasterCatalogRawDataDuplication> findDuplicationsByUpc(
            List<MasterCatalogRawDataDuplication> duplications, String upc) {
        return duplications.stream()
                .filter(duplication -> duplication.getUpc().equals(upc))
                .toList();
    }

    protected List<MasterCatalogRawDataDuplication> getDuplicationGroupsBy(List<String> upcs, List<String> names) {
        List<String> upcsByNames = masterCatalogRawDataRepository.findAllUpcsByNameInAndStatusIs(names,
                RawDataStatus.COMPLETED.name());

        upcs.addAll(upcsByNames);
        upcs = upcs.stream().distinct().toList();

        return batchFindAllByUpcIn(upcs);
    }

    private List<MasterCatalogRawDataDuplication> batchFindAllByUpcIn(List<String> upcs) {
        int batchSize = 100;

        List<List<String>> batches = Lists.partition(upcs, batchSize);

        List<MasterCatalogRawDataDuplication> allByUpcIn = new ArrayList<>();

        for (List<String> batch : batches) {
            allByUpcIn.addAll(masterCatalogRawDataDuplicationRepository.findAllUnderTheSameGroupByUpcIn(
                    batch));
        }

        return removeDuplicates(allByUpcIn);
    }

    private List<MasterCatalogRawDataDuplication> removeDuplicates(List<MasterCatalogRawDataDuplication> duplications) {
        return duplications.stream()
                .filter(duplication -> duplications.stream()
                        .noneMatch(existing -> existing != duplication &&
                                Objects.equals(existing.getDuplicationGroup(), duplication.getDuplicationGroup()) &&
                                Objects.equals(existing.getUpc(), duplication.getUpc())))
                .toList();
    }

    private List<MasterCatalogRawDataDuplication> buildDuplicationRecords(MasterCatalogPotentiallyDuplicateRawData data,
            UUID group) {
        List<MasterCatalogRawDataDuplication> duplicationRecords = new ArrayList<>();

        duplicationRecords.add(MasterCatalogRawDataDuplication.builder()
                .upc(data.getUpc())
                .duplicationGroup(group)
                .createdBy(CommonConstants.SYSTEM_USER_ID)
                .createdAt(Instant.now())
                .build());

        duplicationRecords.add(MasterCatalogRawDataDuplication.builder()
                .upc(data.getPotentiallyDuplicateUpc())
                .duplicationGroup(group)
                .createdBy(CommonConstants.SYSTEM_USER_ID)
                .createdAt(Instant.now())
                .build());

        return duplicationRecords;
    }

    protected abstract boolean anyMatchUnreviewedStatus(List<MasterCatalogPotentiallyDuplicateRawData> duplicateData);

    protected abstract void updateTaskStatus(PotentiallyDuplicationContext context);
}
